{"version": "2.0.0", "tasks": [{"label": "启动 Gemini CLI", "type": "shell", "command": "cmd", "args": ["/c", "set HTTP_PROXY=http://127.0.0.1:7897 && set HTTPS_PROXY=http://127.0.0.1:7897 && set http_proxy=http://127.0.0.1:7897 && set https_proxy=http://127.0.0.1:7897 && gemini"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}, {"label": "清除 Gemini 登录", "type": "shell", "command": "cmd", "args": ["/c", "set HTTP_PROXY=http://127.0.0.1:7897 && set HTTPS_PROXY=http://127.0.0.1:7897 && set http_proxy=http://127.0.0.1:7897 && set https_proxy=http://127.0.0.1:7897 && gemini auth logout"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}]}