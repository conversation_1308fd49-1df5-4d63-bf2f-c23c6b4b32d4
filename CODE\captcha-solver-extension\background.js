// background.js - 后台脚本
chrome.runtime.onInstalled.addListener(() => {
    console.log('AI验证码破解助手已安装');
    
    // 设置默认配置
    chrome.storage.sync.set({
        autoMode: false,
        apiKey: ''
    });
});

// 监听来自content script的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    switch (message.action) {
        case 'captureTab':
            captureTab(sender.tab.id).then(result => {
                sendResponse(result);
            }).catch(error => {
                sendResponse({success: false, error: error.message});
            });
            return true; // 保持消息通道开放

        case 'updateBadge':
            updateBadge(message.text, message.color);
            sendResponse({success: true});
            break;

        case 'notification':
            showNotification(message.title, message.message);
            sendResponse({success: true});
            break;

        default:
            sendResponse({success: false, error: '未知操作'});
    }
});

// 截取标签页
async function captureTab(tabId) {
    try {
        console.log('🖼️ 开始截取标签页，tabId:', tabId);

        // 直接使用activeTab权限截图，不需要额外的窗口操作
        console.log('📸 使用activeTab权限截图...');

        const dataUrl = await chrome.tabs.captureVisibleTab({
            format: 'png',
            quality: 100
        });

        console.log('✅ 标签页截取成功，图像大小:', Math.round(dataUrl.length / 1024), 'KB');
        return {success: true, dataUrl: dataUrl};

    } catch (error) {
        console.error('❌ 标签页截取失败:', error);
        console.error('❌ 错误详情:', {
            name: error.name,
            message: error.message,
            stack: error.stack
        });

        // 如果是权限错误，提供更明确的错误信息
        if (error.message.includes('permission')) {
            return {
                success: false,
                error: '权限不足：请确保在用户点击插件图标后立即调用截图功能'
            };
        }

        return {success: false, error: error.message};
    }
}

// 更新徽章
function updateBadge(text, color = '#4CAF50') {
    chrome.action.setBadgeText({text: text});
    chrome.action.setBadgeBackgroundColor({color: color});
}

// 显示通知
function showNotification(title, message) {
    chrome.notifications.create({
        type: 'basic',
        title: title,
        message: message
    });
}

// 监听标签页更新，自动检测验证码
chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
    if (changeInfo.status === 'complete' && tab.url) {
        // 检查是否启用了自动模式
        const result = await chrome.storage.sync.get(['autoMode']);
        if (result.autoMode) {
            // 延迟一下再检测，确保页面完全加载
            setTimeout(() => {
                chrome.tabs.sendMessage(tabId, {action: 'autoDetect'}).catch(() => {
                    // 忽略错误，可能是页面还没准备好
                });
            }, 2000);
        }
    }
});

// 处理快捷键
chrome.commands.onCommand.addListener(async (command) => {
    const [tab] = await chrome.tabs.query({active: true, currentWindow: true});
    
    switch (command) {
        case 'solve-captcha':
            // 获取API密钥
            const result = await chrome.storage.sync.get(['apiKey']);
            if (result.apiKey) {
                chrome.tabs.sendMessage(tab.id, {
                    action: 'solveCaptcha',
                    apiKey: result.apiKey
                });
            } else {
                showNotification('错误', '请先在插件设置中配置API密钥');
            }
            break;
            
        case 'detect-captcha':
            chrome.tabs.sendMessage(tab.id, {action: 'detectCaptcha'});
            break;
    }
});

// 监听存储变化
chrome.storage.onChanged.addListener((changes, namespace) => {
    if (namespace === 'sync') {
        console.log('设置已更新:', changes);
    }
});

// 处理扩展图标点击
chrome.action.onClicked.addListener(async (tab) => {
    // 这个事件在有popup的情况下不会触发
    // 但可以作为备用方案
    console.log('扩展图标被点击');
});
