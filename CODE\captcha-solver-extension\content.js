// content.js - 内容脚本，在网页中运行
class CaptchaSolver {
    constructor() {
        this.isProcessing = false;
        this.init();
    }

    init() {
        // 监听来自popup的消息
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sendResponse);
            return true; // 保持消息通道开放
        });

        // 自动检测模式
        this.checkAutoMode();
    }

    async checkAutoMode() {
        try {
            const result = await chrome.storage.sync.get(['autoMode']);
            if (result.autoMode) {
                this.startAutoDetection();
            }
        } catch (error) {
            console.error('检查自动模式失败:', error);
        }
    }

    startAutoDetection() {
        // 每5秒检查一次是否有新的验证码
        setInterval(() => {
            if (!this.isProcessing) {
                this.detectAndSolveAuto();
            }
        }, 5000);
    }

    async detectAndSolveAuto() {
        const captchas = this.detectCaptcha();
        if (captchas.length > 0) {
            const result = await chrome.storage.sync.get(['apiKey']);
            if (result.apiKey) {
                this.solveCaptcha(result.apiKey);
            }
        }
    }

    handleMessage(message, sendResponse) {
        try {
            switch (message.action) {
                case 'detectCaptcha':
                    const captchas = this.detectCaptcha();
                    sendResponse({
                        success: true,
                        count: captchas.length,
                        captchas: captchas
                    });
                    break;

                case 'solveCaptcha':
                    // 异步处理验证码破解
                    this.solveCaptcha(message.apiKey).then(result => {
                        sendResponse(result);
                    }).catch(error => {
                        sendResponse({success: false, error: error.message});
                    });
                    break;

                default:
                    sendResponse({success: false, error: '未知操作'});
            }
        } catch (error) {
            sendResponse({success: false, error: error.message});
        }
    }

    detectCaptcha() {
        const captchas = [];
        
        // 检测reCAPTCHA
        const recaptchaFrames = document.querySelectorAll('iframe[src*="recaptcha"]');
        recaptchaFrames.forEach((frame, index) => {
            captchas.push({
                type: 'recaptcha',
                element: frame,
                id: `recaptcha_${index}`
            });
        });

        // 检测其他类型的验证码
        const imageSelectors = [
            '.rc-imageselect',
            '[class*="captcha"]',
            '[id*="captcha"]',
            '.captcha-container'
        ];

        imageSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach((element, index) => {
                captchas.push({
                    type: 'image',
                    element: element,
                    id: `image_captcha_${index}`
                });
            });
        });

        return captchas;
    }

    async solveCaptcha(apiKey) {
        if (this.isProcessing) {
            return {success: false, error: '正在处理中，请稍候'};
        }

        this.isProcessing = true;

        try {
            // 创建进度显示器
            this.createProgressIndicator();

            // 步骤1: 检测验证码
            this.updateProgress('🔍 步骤1: 检测reCAPTCHA验证码框架...', 'info', '正在扫描页面中的验证码元素...');
            const captchaExists = await this.detectCaptchaElements();
            if (!captchaExists) {
                this.updateProgress('❌ 步骤1: 未发现验证码元素', 'error', '页面中没有找到reCAPTCHA验证码');
                return {success: false, error: '未发现验证码'};
            }
            this.updateProgress('✅ 步骤1: 验证码检测成功', 'success', '发现reCAPTCHA验证码框架，准备进行下一步');
            await this.sleep(1000);

            // 步骤2: 点击"我不是机器人"复选框
            this.updateProgress('🖱️ 步骤2: 点击"我不是机器人"复选框...', 'info', '正在定位并点击reCAPTCHA复选框...');
            const result1 = await this.clickRecaptchaCheckbox();
            if (!result1.success) {
                this.updateProgress('❌ 步骤2: 点击复选框失败', 'error', '无法点击复选框，可能是页面加载问题');
                return result1;
            }
            this.updateProgress('✅ 步骤2: 复选框点击成功', 'success', '成功点击复选框，等待图像验证码加载');

            // 步骤3: 等待图像验证码加载
            this.updateProgress('⏳ 步骤3: 等待图像验证码网格加载...', 'info', '等待reCAPTCHA显示图像选择界面...');
            await this.sleep(3000);
            const imageGridLoaded = await this.waitForImageGrid();
            if (!imageGridLoaded) {
                this.updateProgress('❌ 步骤3: 图像网格加载失败', 'error', '图像验证码界面未能正常加载');
                return {success: false, error: '图像验证码未加载'};
            }
            this.updateProgress('✅ 步骤3: 图像验证码加载完成', 'success', '图像网格已加载，准备截取图像');

            // 步骤4: 截取验证码图像
            this.updateProgress('📸 步骤4: 截取验证码图像区域...', 'info', '正在获取验证码图像的屏幕截图...');
            const imageData = await this.captureRecaptchaImage();

            if (!imageData) {
                this.updateProgress('❌ 步骤4: 图像截取失败', 'error', '无法获取验证码图像数据');
                return {success: false, error: '无法截取验证码图像'};
            }
            this.updateProgress('✅ 步骤4: 验证码图像截取成功', 'success', '图像数据已获取，准备发送给AI分析');

            // 步骤5: 获取验证码题目并AI识别分析
            this.updateProgress('🤖 步骤5: AI分析图像并获取位置...', 'info', '正在获取验证码题目...');
            const challenge = await this.getCaptchaChallenge();
            this.updateProgress('', 'info', `验证码题目: ${challenge || '未知'}`);

            this.updateProgress('', 'info', '正在调用Gemini AI识别验证码图像...');
            const indexes = await this.callGeminiAPI(apiKey, imageData, challenge);
            if (!indexes || !Array.isArray(indexes)) {
                this.updateProgress('❌ 步骤5: AI识别失败', 'error', 'AI无法识别图像或返回格式错误');
                return {success: false, error: 'AI识别失败'};
            }
            this.updateProgress(`✅ 步骤5: AI识别完成，获得目标位置`, 'success', `找到 ${indexes.length} 个目标图像，位置: [${indexes.join(', ')}]`);

            // 步骤6: 点击识别出的图像
            this.updateProgress('🎯 步骤6: 开始点击目标图像...', 'info', `准备点击位置 [${indexes.join(', ')}] 的图像...`);
            const result4 = await this.clickCaptchaImages(indexes);
            if (!result4.success) {
                this.updateProgress('❌ 步骤6: 图像点击失败', 'error', '无法点击指定位置的图像');
                return result4;
            }
            this.updateProgress(`✅ 步骤6: 图像点击完成`, 'success', `成功点击了 ${indexes.length} 个目标图像`);

            // 步骤7: 提交验证
            this.updateProgress('📤 步骤7: 提交验证结果...', 'info', '正在提交选择的图像给reCAPTCHA验证...');
            await this.submitCaptcha();

            // 步骤8: 验证结果
            this.updateProgress('✅ 步骤8: 检查验证是否成功...', 'info', '等待reCAPTCHA验证结果...');
            await this.sleep(2000);
            const verificationResult = await this.checkVerificationResult();

            if (verificationResult.success) {
                this.updateProgress('🎉 验证码破解成功！', 'success', 'reCAPTCHA验证通过，任务完成！');
                setTimeout(() => this.hideProgressIndicator(), 3000);
                return {success: true, message: '验证码破解成功'};
            } else {
                this.updateProgress('⚠️ 验证可能需要重试', 'warning', '验证未通过，可能需要重新尝试');
                setTimeout(() => this.hideProgressIndicator(), 5000);
                return {success: false, error: '验证未通过，可能需要重试'};
            }

        } catch (error) {
            this.updateProgress(`💥 错误: ${error.message}`, 'error');
            setTimeout(() => this.hideProgressIndicator(), 5000);
            return {success: false, error: error.message};
        } finally {
            this.isProcessing = false;
        }
    }

    async clickRecaptchaCheckbox() {
        try {
            this.updateProgress('', 'info', '正在查找reCAPTCHA复选框...');

            // 使用与Python代码相同的选择器：iframe[src*='anchor']
            const anchorFrame = document.querySelector('iframe[src*="anchor"]');
            if (!anchorFrame) {
                this.updateProgress('', 'error', '未找到anchor iframe');
                return {success: false, error: '未找到reCAPTCHA复选框'};
            }

            this.updateProgress('', 'info', '找到anchor iframe，准备点击复选框...');

            // 等待iframe加载完成
            await this.sleep(1000);

            // 尝试直接访问iframe内容（模拟Python的switch_to.frame）
            try {
                const iframeDocument = anchorFrame.contentDocument || anchorFrame.contentWindow.document;

                if (iframeDocument) {
                    // 使用与Python代码相同的选择器：#recaptcha-anchor
                    const checkbox = iframeDocument.querySelector('#recaptcha-anchor');

                    if (checkbox) {
                        this.updateProgress('', 'info', '找到复选框元素，执行点击...');
                        checkbox.click();
                        this.updateProgress('', 'success', '复选框点击成功');
                        return {success: true};
                    } else {
                        this.updateProgress('', 'error', '未找到recaptcha-anchor元素');
                    }
                } else {
                    this.updateProgress('', 'error', '无法访问iframe文档');
                }
            } catch (crossOriginError) {
                this.updateProgress('', 'warning', '跨域限制，使用备用方法...');
            }

            // 备用方法：注入脚本
            const script = document.createElement('script');
            script.src = chrome.runtime.getURL('injected.js');
            document.head.appendChild(script);

            // 发送消息给注入的脚本
            window.postMessage({
                type: 'CLICK_RECAPTCHA_CHECKBOX',
                frameSelector: 'iframe[src*="anchor"]'
            }, '*');

            await this.sleep(1500); // 等待点击完成
            this.updateProgress('', 'success', '复选框点击完成（注入脚本方式）');
            return {success: true};

        } catch (error) {
            this.updateProgress('', 'error', `点击复选框失败: ${error.message}`);
            return {success: false, error: '点击复选框失败: ' + error.message};
        }
    }

    async captureRecaptchaImage() {
        try {
            this.updateProgress('', 'info', '正在定位验证码图像框架...');

            // 查找bframe iframe
            const bframe = document.querySelector('iframe[src*="bframe"]');
            if (!bframe) {
                this.updateProgress('', 'error', '未找到验证码图像框架');
                throw new Error('未找到验证码图像框架');
            }

            // 获取iframe的位置和大小
            const rect = bframe.getBoundingClientRect();
            this.updateProgress('', 'info', `图像框架尺寸: ${Math.round(rect.width)}x${Math.round(rect.height)}`);

            if (rect.width < 100 || rect.height < 100) {
                this.updateProgress('', 'error', '图像框架尺寸异常');
                throw new Error('图像框架尺寸异常');
            }

            this.updateProgress('', 'info', '正在请求页面截图...');

            let response;
            try {
                // 使用chrome extension API截图，添加超时处理
                response = await Promise.race([
                    chrome.runtime.sendMessage({
                        action: 'captureTab'
                    }),
                    new Promise((_, reject) =>
                        setTimeout(() => reject(new Error('截图请求超时(5秒)')), 5000)
                    )
                ]);

                if (!response || !response.success) {
                    throw new Error(response ? response.error : '截图响应为空');
                }
            } catch (error) {
                this.updateProgress('', 'warning', `Chrome API截图失败，尝试直接获取图像: ${error.message}`);

                // 备用方案：直接从iframe获取图像URL
                response = await this.getImageDirectly(bframe);

                if (!response || !response.success) {
                    const errorMsg = response ? response.error : '无法获取图像';
                    this.updateProgress('', 'error', `所有截图方法都失败: ${errorMsg}`);
                    throw new Error(errorMsg);
                }
            }

            this.updateProgress('', 'info', '页面截图获取成功，正在裁剪验证码区域...');

            // 裁剪验证码区域
            const croppedImage = await this.cropImageArea(response.dataUrl, rect);

            if (!croppedImage) {
                this.updateProgress('', 'error', '图像裁剪失败');
                throw new Error('图像裁剪失败');
            }

            this.updateProgress('', 'success', '验证码图像截取完成');
            return croppedImage;

        } catch (error) {
            this.updateProgress('', 'error', `截图失败: ${error.message}`);
            console.error('截图失败:', error);
            return null;
        }
    }

    // 直接从iframe获取图像（备用方法）
    async getImageDirectly(bframe) {
        try {
            this.updateProgress('', 'info', '尝试直接从iframe获取图像...');

            if (!bframe) {
                return {success: false, error: 'iframe参数为空'};
            }

            try {
                // 尝试访问iframe内容
                const iframeDocument = bframe.contentDocument || bframe.contentWindow.document;

                if (iframeDocument) {
                    // 查找验证码图像
                    const images = iframeDocument.querySelectorAll('img');

                    if (images.length > 0) {
                        // 找到最大的图像（通常是验证码网格）
                        let largestImage = null;
                        let maxArea = 0;

                        for (const img of images) {
                            if (img.complete && img.naturalWidth > 0) {
                                const area = img.naturalWidth * img.naturalHeight;
                                if (area > maxArea) {
                                    maxArea = area;
                                    largestImage = img;
                                }
                            }
                        }

                        if (largestImage) {
                            this.updateProgress('', 'info', '找到验证码图像，转换为base64...');

                            // 创建canvas来转换图像
                            const canvas = document.createElement('canvas');
                            const ctx = canvas.getContext('2d');

                            canvas.width = largestImage.naturalWidth;
                            canvas.height = largestImage.naturalHeight;

                            ctx.drawImage(largestImage, 0, 0);
                            const dataUrl = canvas.toDataURL('image/png');

                            this.updateProgress('', 'success', `直接获取图像成功，尺寸: ${canvas.width}x${canvas.height}`);
                            return {success: true, dataUrl: dataUrl};
                        }
                    }
                }
            } catch (crossOriginError) {
                this.updateProgress('', 'warning', '跨域限制，无法直接访问iframe内容');
            }

            // 如果无法获取真实图像，创建一个测试图像
            this.updateProgress('', 'warning', '创建测试图像用于AI分析...');

            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = 300;
            canvas.height = 300;

            // 创建一个简单的网格图像用于测试
            ctx.fillStyle = '#f0f0f0';
            ctx.fillRect(0, 0, 300, 300);

            // 绘制网格
            ctx.strokeStyle = '#ccc';
            ctx.lineWidth = 2;
            for (let i = 0; i < 3; i++) {
                for (let j = 0; j < 3; j++) {
                    ctx.strokeRect(i * 100, j * 100, 100, 100);
                }
            }

            ctx.fillStyle = '#333';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('测试验证码图像', 150, 150);

            const dataUrl = canvas.toDataURL('image/png');

            this.updateProgress('', 'warning', '使用测试图像，AI可能无法正确识别');
            return {success: true, dataUrl: dataUrl, isPlaceholder: true};

        } catch (error) {
            this.updateProgress('', 'error', `直接获取图像失败: ${error.message}`);
            return {success: false, error: error.message};
        }
    }

    // 询问用户是否跳过截图
    async askUserToSkipScreenshot() {
        return new Promise((resolve) => {
            // 创建确认对话框
            const dialog = document.createElement('div');
            dialog.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                border: 2px solid #667eea;
                border-radius: 10px;
                padding: 20px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.3);
                z-index: 10000;
                font-family: Arial, sans-serif;
                max-width: 400px;
            `;

            dialog.innerHTML = `
                <h3 style="margin: 0 0 15px 0; color: #333;">📸 截图失败</h3>
                <p style="margin: 0 0 20px 0; color: #666; line-height: 1.4;">
                    无法获取页面截图，可能是权限限制或浏览器安全策略。
                </p>
                <p style="margin: 0 0 20px 0; color: #666; line-height: 1.4;">
                    您可以选择：
                </p>
                <div style="display: flex; gap: 10px; justify-content: center;">
                    <button id="skipBtn" style="
                        background: #4CAF50;
                        color: white;
                        border: none;
                        padding: 10px 20px;
                        border-radius: 5px;
                        cursor: pointer;
                        font-size: 14px;
                    ">跳过截图，手动完成</button>
                    <button id="retryBtn" style="
                        background: #2196F3;
                        color: white;
                        border: none;
                        padding: 10px 20px;
                        border-radius: 5px;
                        cursor: pointer;
                        font-size: 14px;
                    ">重试截图</button>
                </div>
            `;

            document.body.appendChild(dialog);

            // 绑定按钮事件
            document.getElementById('skipBtn').onclick = () => {
                document.body.removeChild(dialog);
                resolve(true);
            };

            document.getElementById('retryBtn').onclick = () => {
                document.body.removeChild(dialog);
                resolve(false);
            };

            // 10秒后自动选择跳过
            setTimeout(() => {
                if (document.body.contains(dialog)) {
                    document.body.removeChild(dialog);
                    resolve(true);
                }
            }, 10000);
        });
    }

    // 处理手动模式
    async handleManualMode() {
        try {
            this.updateProgress('🖱️ 手动模式: 等待用户操作...', 'info', '请手动点击验证码图像完成验证');

            // 显示手动操作指南
            this.showManualGuide();

            // 等待用户手动完成验证
            const result = await this.waitForManualCompletion();

            if (result.success) {
                this.updateProgress('🎉 手动验证完成！', 'success', '用户已手动完成验证码');
                setTimeout(() => this.hideProgressIndicator(), 3000);
                return {success: true, message: '手动验证完成'};
            } else {
                this.updateProgress('⚠️ 手动验证未完成', 'warning', '请继续手动完成验证');
                return {success: false, error: '手动验证未完成'};
            }

        } catch (error) {
            this.updateProgress('❌ 手动模式失败', 'error', error.message);
            return {success: false, error: error.message};
        }
    }

    // 显示手动操作指南
    showManualGuide() {
        const guide = document.createElement('div');
        guide.id = 'manual-guide';
        guide.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            z-index: 9999;
            font-family: Arial, sans-serif;
            max-width: 300px;
            font-size: 14px;
        `;

        guide.innerHTML = `
            <h4 style="margin: 0 0 10px 0;">🖱️ 手动模式</h4>
            <p style="margin: 0 0 10px 0; line-height: 1.4;">
                请手动完成以下步骤：
            </p>
            <ol style="margin: 0; padding-left: 20px; line-height: 1.6;">
                <li>查看验证码要求</li>
                <li>点击符合要求的图像</li>
                <li>点击"验证"按钮</li>
            </ol>
            <button onclick="document.getElementById('manual-guide').remove()" style="
                background: rgba(255,255,255,0.2);
                color: white;
                border: none;
                padding: 5px 10px;
                border-radius: 3px;
                cursor: pointer;
                margin-top: 10px;
                font-size: 12px;
            ">关闭指南</button>
        `;

        document.body.appendChild(guide);

        // 5分钟后自动移除
        setTimeout(() => {
            if (document.getElementById('manual-guide')) {
                document.getElementById('manual-guide').remove();
            }
        }, 300000);
    }

    // 等待手动完成验证
    async waitForManualCompletion() {
        return new Promise((resolve) => {
            let checkCount = 0;
            const maxChecks = 120; // 最多检查2分钟

            const checkInterval = setInterval(() => {
                checkCount++;

                // 检查验证是否完成
                const successIndicators = [
                    '.recaptcha-success',
                    '[data-recaptcha-status="success"]',
                    '.rc-anchor-normal[style*="display: none"]'
                ];

                for (const selector of successIndicators) {
                    if (document.querySelector(selector)) {
                        clearInterval(checkInterval);
                        resolve({success: true, message: '手动验证成功'});
                        return;
                    }
                }

                // 检查是否还有验证码存在
                const captchaExists = document.querySelector('iframe[src*="recaptcha"]');
                if (!captchaExists) {
                    clearInterval(checkInterval);
                    resolve({success: true, message: '验证码已消失，可能已完成'});
                    return;
                }

                // 超时处理
                if (checkCount >= maxChecks) {
                    clearInterval(checkInterval);
                    resolve({success: false, message: '等待超时'});
                }
            }, 1000);
        });
    }

    // 备用截图方法
    async captureUsingFallback(iframe) {
        try {
            this.updateProgress('', 'info', '使用备用截图方法...');

            // 方法1：尝试直接从iframe获取图像
            if (iframe && iframe.contentDocument) {
                const iframeDoc = iframe.contentDocument;
                const images = iframeDoc.querySelectorAll('img');

                if (images.length > 0) {
                    // 找到最大的图像（通常是验证码网格）
                    let largestImage = null;
                    let maxArea = 0;

                    for (const img of images) {
                        const area = img.naturalWidth * img.naturalHeight;
                        if (area > maxArea) {
                            maxArea = area;
                            largestImage = img;
                        }
                    }

                    if (largestImage && largestImage.complete) {
                        this.updateProgress('', 'info', '从iframe直接获取图像...');

                        // 创建canvas来转换图像
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');

                        canvas.width = largestImage.naturalWidth;
                        canvas.height = largestImage.naturalHeight;

                        ctx.drawImage(largestImage, 0, 0);
                        const dataUrl = canvas.toDataURL('image/png');

                        this.updateProgress('', 'success', '备用方法获取图像成功');
                        return {success: true, dataUrl: dataUrl};
                    }
                }
            }

            // 方法2：使用模拟截图（创建一个占位图像）
            this.updateProgress('', 'warning', '创建模拟图像用于测试...');

            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = 400;
            canvas.height = 580;

            // 创建一个简单的网格图像用于测试
            ctx.fillStyle = '#f0f0f0';
            ctx.fillRect(0, 0, 400, 580);

            ctx.fillStyle = '#333';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('验证码图像获取失败', 200, 290);
            ctx.fillText('请手动完成验证', 200, 320);

            const dataUrl = canvas.toDataURL('image/png');

            this.updateProgress('', 'warning', '使用模拟图像，AI可能无法正确识别');
            return {success: true, dataUrl: dataUrl, isPlaceholder: true};

        } catch (error) {
            this.updateProgress('', 'error', `备用截图方法失败: ${error.message}`);
            return {success: false, error: error.message};
        }
    }

    // 裁剪图像区域
    async cropImageArea(dataUrl, rect) {
        return new Promise((resolve) => {
            const img = new Image();
            img.onload = () => {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');

                // 设置画布尺寸为验证码区域尺寸
                canvas.width = rect.width;
                canvas.height = rect.height;

                // 裁剪图像
                ctx.drawImage(
                    img,
                    rect.left, rect.top, rect.width, rect.height,
                    0, 0, rect.width, rect.height
                );

                resolve(canvas.toDataURL('image/png'));
            };
            img.onerror = () => resolve(null);
            img.src = dataUrl;
        });
    }

    // 获取验证码题目
    async getCaptchaChallenge() {
        try {
            // 查找bframe iframe（包含验证码题目）
            const bframe = document.querySelector('iframe[src*="bframe"]');
            if (!bframe) {
                this.updateProgress('', 'warning', '未找到bframe iframe');
                return '选择所有符合要求的图像'; // 默认题目
            }

            try {
                // 尝试访问iframe内容
                const iframeDocument = bframe.contentDocument || bframe.contentWindow.document;

                if (iframeDocument) {
                    // 查找验证码题目文本
                    const challengeSelectors = [
                        '.rc-imageselect-desc-wrapper',
                        '.rc-imageselect-desc',
                        '.rc-imageselect-instructions',
                        '[class*="challenge"]',
                        '[class*="instruction"]'
                    ];

                    for (const selector of challengeSelectors) {
                        const element = iframeDocument.querySelector(selector);
                        if (element && element.textContent.trim()) {
                            const challenge = element.textContent.trim();
                            this.updateProgress('', 'success', `找到验证码题目: ${challenge}`);
                            return challenge;
                        }
                    }
                }
            } catch (crossOriginError) {
                this.updateProgress('', 'warning', '跨域限制，无法获取题目');
            }

            // 如果无法获取题目，返回默认值
            return '选择所有符合要求的图像';

        } catch (error) {
            this.updateProgress('', 'error', `获取验证码题目失败: ${error.message}`);
            return '选择所有符合要求的图像';
        }
    }

    async callGeminiAPI(apiKey, imageData, challenge) {
        try {
            this.updateProgress('', 'info', '正在准备发送给Gemini AI...');

            // 使用验证码题目构建更精确的prompt
            const prompt = `
            You are analyzing a reCAPTCHA image grid. The challenge instruction is: "${challenge || '选择所有符合要求的图像'}"

            Please identify all images in the grid that match the instruction and return a JSON array of their 0-indexed positions.
            The grid is typically 3x3 (positions 0-8) or 4x4 (positions 0-15).

            Return only a JSON array like [0, 2, 5] with no additional text.
            `;

            if (!imageData || !imageData.includes('data:image')) {
                this.updateProgress('', 'error', '图像数据格式无效');
                throw new Error('图像数据格式无效');
            }

            // 提取base64数据
            const base64Data = imageData.split(',')[1];
            if (!base64Data) {
                this.updateProgress('', 'error', '无法提取图像base64数据');
                throw new Error('无法提取图像base64数据');
            }

            this.updateProgress('', 'info', `图像数据: ${Math.round(base64Data.length / 1024)}KB，正在发送给AI...`);
            this.updateProgress('', 'info', '连接Gemini AI服务器...');

            // 使用与原始程序相同的API调用方式
            const requestBody = {
                contents: [{
                    parts: [
                        { text: prompt },
                        {
                            inline_data: {
                                mime_type: "image/png",
                                data: base64Data
                            }
                        }
                    ]
                }],
                generationConfig: {
                    response_mime_type: "application/json"
                }
            };

            this.updateProgress('', 'info', '正在发送图像给Gemini AI分析...');

            // 调用Gemini API
            const apiResponse = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent?key=${apiKey}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestBody)
            });

            this.updateProgress('', 'info', `AI服务器响应: ${apiResponse.status === 200 ? '成功' : '错误 ' + apiResponse.status}`);

            if (!apiResponse.ok) {
                const errorText = await apiResponse.text();
                this.updateProgress('', 'error', `API请求失败: ${apiResponse.status} - ${errorText}`);
                throw new Error(`API请求失败: ${apiResponse.status} - ${errorText}`);
            }

            const result = await apiResponse.json();
            this.updateProgress('', 'info', 'AI正在分析图像内容...');

            if (!result.candidates || !result.candidates[0] || !result.candidates[0].content) {
                this.updateProgress('', 'error', 'AI响应格式异常');
                console.error('完整API响应:', result);
                throw new Error('AI响应格式异常');
            }

            const text = result.candidates[0].content.parts[0].text;
            this.updateProgress('', 'info', `AI分析完成，正在解析位置信息...`);

            try {
                const parsedResult = JSON.parse(text);
                if (Array.isArray(parsedResult)) {
                    this.updateProgress('', 'success', `获得具体位置: [${parsedResult.join(', ')}]，共 ${parsedResult.length} 个目标`);
                    return parsedResult;
                } else {
                    this.updateProgress('', 'error', 'AI返回的位置信息格式错误');
                    throw new Error('AI响应不是数组格式');
                }
            } catch (parseError) {
                this.updateProgress('', 'error', `位置信息解析失败: ${parseError.message}`);
                console.error('解析失败的文本:', text);
                throw new Error(`JSON解析失败: ${parseError.message}`);
            }

        } catch (error) {
            this.updateProgress('', 'error', `Gemini API调用失败: ${error.message}`);
            console.error('调用Gemini API失败:', error);
            return null;
        }
    }

    async clickCaptchaImages(indexes) {
        try {
            if (!Array.isArray(indexes) || indexes.length === 0) {
                this.updateProgress('', 'error', '没有需要点击的图像');
                return {success: false, error: '没有需要点击的图像'};
            }

            this.updateProgress('', 'info', `准备点击位置: [${indexes.join(', ')}]`);

            // 验证索引范围
            const validIndexes = indexes.filter(index => index >= 0 && index < 16); // 通常是4x4网格
            if (validIndexes.length !== indexes.length) {
                this.updateProgress('', 'warning', `过滤无效位置，有效位置: [${validIndexes.join(', ')}]`);
            }

            if (validIndexes.length === 0) {
                this.updateProgress('', 'error', '所有位置都无效');
                return {success: false, error: '所有索引都无效'};
            }

            // 发送消息给注入的脚本来点击图像
            this.updateProgress('', 'info', `开始点击第 ${validIndexes[0] + 1} 个图像...`);

            window.postMessage({
                type: 'CLICK_CAPTCHA_IMAGES',
                indexes: validIndexes
            }, '*');

            // 等待点击完成，显示点击进度
            for (let i = 0; i < validIndexes.length; i++) {
                await this.sleep(500);
                this.updateProgress('', 'info', `正在点击第 ${i + 1}/${validIndexes.length} 个图像 (位置${validIndexes[i] + 1})`);
            }

            await this.sleep(1000);
            this.updateProgress('', 'success', `所有图像点击完成！共点击 ${validIndexes.length} 个目标`);
            return {success: true};

        } catch (error) {
            this.updateProgress('', 'error', `点击图像失败: ${error.message}`);
            return {success: false, error: '点击图像失败: ' + error.message};
        }
    }

    async submitCaptcha() {
        // 查找并点击提交按钮
        const submitSelectors = [
            '#recaptcha-verify-button',
            '.rc-button-default',
            '[id*="submit"]',
            '[class*="submit"]'
        ];

        for (const selector of submitSelectors) {
            const button = document.querySelector(selector);
            if (button) {
                button.click();
                break;
            }
        }
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // 创建进度显示器
    createProgressIndicator() {
        // 移除已存在的进度显示器
        this.hideProgressIndicator();

        const indicator = document.createElement('div');
        indicator.id = 'captcha-progress-indicator';
        indicator.innerHTML = `
            <div class="progress-header">
                <span class="progress-title">🤖 AI验证码破解进度</span>
                <button class="progress-close" onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
            <div class="progress-content">
                <div class="progress-step" id="current-step">准备开始...</div>
                <div class="progress-details" id="step-details"></div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill"></div>
                </div>
                <div class="progress-percentage" id="progress-percentage">0%</div>
            </div>
        `;

        indicator.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            width: 400px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            z-index: 10001;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            animation: slideIn 0.3s ease-out;
        `;

        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }

            #captcha-progress-indicator .progress-header {
                padding: 15px 20px 10px;
                border-bottom: 1px solid rgba(255,255,255,0.2);
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            #captcha-progress-indicator .progress-title {
                font-weight: 600;
                font-size: 16px;
            }

            #captcha-progress-indicator .progress-close {
                background: none;
                border: none;
                color: white;
                font-size: 20px;
                cursor: pointer;
                padding: 0;
                width: 24px;
                height: 24px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: background 0.2s;
            }

            #captcha-progress-indicator .progress-close:hover {
                background: rgba(255,255,255,0.2);
            }

            #captcha-progress-indicator .progress-content {
                padding: 20px;
            }

            #captcha-progress-indicator .progress-step {
                font-size: 14px;
                font-weight: 600;
                margin-bottom: 8px;
                min-height: 20px;
            }

            #captcha-progress-indicator .progress-details {
                font-size: 12px;
                opacity: 0.9;
                margin-bottom: 15px;
                min-height: 16px;
                line-height: 1.4;
            }

            #captcha-progress-indicator .progress-bar {
                width: 100%;
                height: 8px;
                background: rgba(255,255,255,0.2);
                border-radius: 4px;
                overflow: hidden;
                margin-bottom: 10px;
            }

            #captcha-progress-indicator .progress-fill {
                height: 100%;
                background: linear-gradient(90deg, #4CAF50, #8BC34A);
                border-radius: 4px;
                transition: width 0.3s ease;
                width: 0%;
            }

            #captcha-progress-indicator .progress-percentage {
                text-align: center;
                font-size: 12px;
                font-weight: 600;
            }

            .step-success { color: #4CAF50 !important; }
            .step-error { color: #f44336 !important; }
            .step-warning { color: #ff9800 !important; }
            .step-info { color: #2196F3 !important; }
        `;

        document.head.appendChild(style);
        document.body.appendChild(indicator);

        this.progressIndicator = indicator;
        this.currentStep = 0;
        this.totalSteps = 8;
    }

    // 更新进度
    updateProgress(stepText, type = 'info', details = '') {
        if (!this.progressIndicator) return;

        const stepElement = document.getElementById('current-step');
        const detailsElement = document.getElementById('step-details');
        const fillElement = document.getElementById('progress-fill');
        const percentageElement = document.getElementById('progress-percentage');

        if (stepElement) {
            stepElement.textContent = stepText;
            stepElement.className = `progress-step step-${type}`;
        }

        if (detailsElement && details) {
            detailsElement.textContent = details;
        }

        // 更新进度条
        if (stepText.includes('步骤')) {
            const stepMatch = stepText.match(/步骤(\d+)/);
            if (stepMatch) {
                this.currentStep = parseInt(stepMatch[1]);
                const percentage = Math.round((this.currentStep / this.totalSteps) * 100);

                if (fillElement) {
                    fillElement.style.width = `${percentage}%`;
                }

                if (percentageElement) {
                    percentageElement.textContent = `${percentage}%`;
                }
            }
        }

        // 发送详细状态给popup界面
        chrome.runtime.sendMessage({
            action: 'updateStatus',
            message: stepText,
            details: details,
            type: type,
            currentStep: this.currentStep,
            totalSteps: this.totalSteps,
            percentage: this.currentStep ? Math.round((this.currentStep / this.totalSteps) * 100) : 0
        }).catch(() => {}); // 忽略错误，popup可能未打开

        console.log(`[验证码破解] ${stepText}`, details);
    }

    // 隐藏进度显示器
    hideProgressIndicator() {
        const existing = document.getElementById('captcha-progress-indicator');
        if (existing) {
            existing.style.animation = 'slideOut 0.3s ease-in forwards';
            setTimeout(() => {
                if (existing.parentNode) {
                    existing.parentNode.removeChild(existing);
                }
            }, 300);
        }
        this.progressIndicator = null;
    }

    // 检测验证码元素
    async detectCaptchaElements() {
        try {
            // 检查reCAPTCHA复选框
            const checkbox = document.querySelector('iframe[src*="anchor"]');
            if (!checkbox) {
                this.updateProgress('', 'info', '未找到reCAPTCHA复选框');
                return false;
            }

            this.updateProgress('', 'info', '发现reCAPTCHA复选框');
            return true;
        } catch (error) {
            this.updateProgress('', 'error', `检测失败: ${error.message}`);
            return false;
        }
    }

    // 等待图像网格加载
    async waitForImageGrid(timeout = 10000) {
        const startTime = Date.now();

        while (Date.now() - startTime < timeout) {
            try {
                // 检查图像选择框架
                const bframe = document.querySelector('iframe[src*="bframe"]');
                if (bframe) {
                    this.updateProgress('', 'info', '发现图像选择框架');

                    // 等待框架内容加载
                    await this.sleep(1000);

                    // 尝试检测图像网格（通过iframe尺寸判断）
                    const rect = bframe.getBoundingClientRect();
                    if (rect.width > 100 && rect.height > 100) {
                        this.updateProgress('', 'info', `图像网格已加载 (${Math.round(rect.width)}x${Math.round(rect.height)})`);
                        return true;
                    }
                }

                await this.sleep(500);
            } catch (error) {
                this.updateProgress('', 'warning', `等待中遇到错误: ${error.message}`);
            }
        }

        this.updateProgress('', 'error', '图像网格加载超时');
        return false;
    }

    // 检查验证结果
    async checkVerificationResult() {
        try {
            await this.sleep(2000);

            // 检查是否还存在验证码
            const checkbox = document.querySelector('iframe[src*="anchor"]');
            const bframe = document.querySelector('iframe[src*="bframe"]');

            if (!checkbox && !bframe) {
                this.updateProgress('', 'success', '验证码已消失，验证成功');
                return {success: true, message: '验证成功'};
            }

            // 检查复选框状态
            if (checkbox) {
                const rect = checkbox.getBoundingClientRect();
                if (rect.width === 0 || rect.height === 0) {
                    this.updateProgress('', 'success', '复选框已隐藏，验证成功');
                    return {success: true, message: '验证成功'};
                }
            }

            // 检查是否出现新的验证码
            if (bframe) {
                const rect = bframe.getBoundingClientRect();
                if (rect.width > 100 && rect.height > 100) {
                    this.updateProgress('', 'warning', '出现新的验证码，需要重试');
                    return {success: false, message: '需要重试'};
                }
            }

            // 检查页面是否有成功指示
            const successIndicators = [
                '.recaptcha-success',
                '[data-recaptcha-success]',
                '.captcha-success'
            ];

            for (const selector of successIndicators) {
                if (document.querySelector(selector)) {
                    this.updateProgress('', 'success', '发现成功指示器');
                    return {success: true, message: '验证成功'};
                }
            }

            this.updateProgress('', 'info', '验证状态不明确');
            return {success: false, message: '状态不明确'};

        } catch (error) {
            this.updateProgress('', 'error', `检查验证结果失败: ${error.message}`);
            return {success: false, error: error.message};
        }
    }
}

// 初始化验证码解决器
const captchaSolver = new CaptchaSolver();

// 添加样式，显示插件状态
function addStatusIndicator() {
    const indicator = document.createElement('div');
    indicator.id = 'captcha-solver-indicator';
    indicator.innerHTML = '🤖 AI验证码助手已激活';
    indicator.style.cssText = `
        position: fixed;
        top: 10px;
        right: 10px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 8px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        z-index: 10000;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        backdrop-filter: blur(10px);
        opacity: 0.9;
        transition: all 0.3s ease;
        cursor: pointer;
    `;

    // 3秒后自动隐藏
    setTimeout(() => {
        if (indicator.parentNode) {
            indicator.style.opacity = '0';
            setTimeout(() => {
                if (indicator.parentNode) {
                    indicator.parentNode.removeChild(indicator);
                }
            }, 300);
        }
    }, 3000);

    document.body.appendChild(indicator);
}

// 页面加载完成后显示状态指示器
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', addStatusIndicator);
} else {
    addStatusIndicator();
}
