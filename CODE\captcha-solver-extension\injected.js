// injected.js - 注入到页面的脚本，用于跨iframe操作
(function() {
    'use strict';

    // 监听来自content script的消息
    window.addEventListener('message', function(event) {
        if (event.source !== window) return;

        switch (event.data.type) {
            case 'CLICK_RECAPTCHA_CHECKBOX':
                clickRecaptchaCheckbox();
                break;
            case 'CLICK_CAPTCHA_IMAGES':
                clickCaptchaImages(event.data.indexes);
                break;
        }
    });

    function clickRecaptchaCheckbox() {
        try {
            // 查找anchor iframe
            const anchorFrame = document.querySelector('iframe[src*="anchor"]');
            if (!anchorFrame) {
                console.log('未找到anchor iframe');
                return;
            }

            // 尝试访问iframe内容
            try {
                const iframeDoc = anchorFrame.contentDocument || anchorFrame.contentWindow.document;
                // 使用与原始程序相同的选择器
                const checkbox = iframeDoc.querySelector('#recaptcha-anchor');
                
                if (checkbox) {
                    // 模拟真实的点击事件
                    const rect = checkbox.getBoundingClientRect();
                    const centerX = rect.left + rect.width / 2;
                    const centerY = rect.top + rect.height / 2;

                    // 创建鼠标事件
                    const mouseEvents = ['mousedown', 'mouseup', 'click'];
                    mouseEvents.forEach(eventType => {
                        const event = new MouseEvent(eventType, {
                            view: iframeDoc.defaultView,
                            bubbles: true,
                            cancelable: true,
                            clientX: centerX,
                            clientY: centerY
                        });
                        checkbox.dispatchEvent(event);
                    });

                    console.log('已点击reCAPTCHA复选框');
                } else {
                    console.log('未找到复选框元素');
                }
            } catch (e) {
                // 如果无法访问iframe内容（跨域），尝试其他方法
                console.log('无法直接访问iframe，尝试其他方法');
                
                // 模拟在iframe位置的点击
                const rect = anchorFrame.getBoundingClientRect();
                const centerX = rect.left + rect.width / 2;
                const centerY = rect.top + rect.height / 2;

                const clickEvent = new MouseEvent('click', {
                    view: window,
                    bubbles: true,
                    cancelable: true,
                    clientX: centerX,
                    clientY: centerY
                });
                
                anchorFrame.dispatchEvent(clickEvent);
            }
        } catch (error) {
            console.error('点击复选框失败:', error);
        }
    }

    function clickCaptchaImages(indexes) {
        try {
            console.log(`🎯 开始点击验证码图像，索引: [${indexes.join(', ')}]`);

            // 查找bframe iframe
            const bframe = document.querySelector('iframe[src*="bframe"]');
            if (!bframe) {
                console.log('❌ 未找到bframe iframe');
                return;
            }

            console.log('✅ 找到验证码图像框架');
            const rect = bframe.getBoundingClientRect();
            console.log(`📐 框架尺寸: ${Math.round(rect.width)}x${Math.round(rect.height)}`);

            // 尝试访问iframe内容
            try {
                const iframeDoc = bframe.contentDocument || bframe.contentWindow.document;
                const imageTable = iframeDoc.querySelector('.rc-imageselect-table');

                if (imageTable) {
                    console.log('✅ 找到图像表格，使用直接点击方式');
                    const images = imageTable.querySelectorAll('.rc-imageselect-tile');
                    console.log(`📊 发现 ${images.length} 个图像块`);

                    let clickedCount = 0;
                    indexes.forEach((index, i) => {
                        if (index < images.length) {
                            const image = images[index];

                            // 添加延迟，模拟人类行为
                            setTimeout(() => {
                                // 模拟真实的点击事件
                                const rect = image.getBoundingClientRect();
                                const centerX = rect.left + rect.width / 2;
                                const centerY = rect.top + rect.height / 2;

                                const mouseEvents = ['mousedown', 'mouseup', 'click'];
                                mouseEvents.forEach(eventType => {
                                    const event = new MouseEvent(eventType, {
                                        view: iframeDoc.defaultView,
                                        bubbles: true,
                                        cancelable: true,
                                        clientX: centerX,
                                        clientY: centerY
                                    });
                                    image.dispatchEvent(event);
                                });

                                console.log(`✅ 已点击图像 ${index} (${i + 1}/${indexes.length})`);
                                clickedCount++;

                                if (clickedCount === indexes.length) {
                                    console.log(`🎉 所有图像点击完成，共点击 ${clickedCount} 个`);
                                }
                            }, i * 300); // 每个点击间隔300ms
                        } else {
                            console.log(`⚠️ 索引 ${index} 超出范围 (最大: ${images.length - 1})`);
                        }
                    });
                } else {
                    console.log('❌ 未找到图像表格，尝试坐标点击方式');
                    useCoordinateClicking();
                }
            } catch (e) {
                console.log('⚠️ 无法直接访问iframe，使用坐标点击方式');
                useCoordinateClicking();
            }

            function useCoordinateClicking() {
                // 如果无法访问iframe内容，尝试计算相对位置点击
                const rect = bframe.getBoundingClientRect();

                // 动态检测网格大小（通常是3x3或4x4）
                let gridSize = 3;
                if (Math.max(...indexes) >= 9) {
                    gridSize = 4;
                }

                console.log(`📐 使用 ${gridSize}x${gridSize} 网格布局`);

                const cellWidth = rect.width / gridSize;
                const cellHeight = rect.height / gridSize;

                indexes.forEach((index, i) => {
                    setTimeout(() => {
                        const row = Math.floor(index / gridSize);
                        const col = index % gridSize;

                        const x = rect.left + col * cellWidth + cellWidth / 2;
                        const y = rect.top + row * cellHeight + cellHeight / 2;

                        console.log(`🖱️ 点击坐标 (${Math.round(x)}, ${Math.round(y)}) - 图像 ${index}`);

                        const clickEvent = new MouseEvent('click', {
                            view: window,
                            bubbles: true,
                            cancelable: true,
                            clientX: x,
                            clientY: y
                        });

                        bframe.dispatchEvent(clickEvent);
                        console.log(`✅ 坐标点击完成 ${index} (${i + 1}/${indexes.length})`);

                        if (i === indexes.length - 1) {
                            console.log(`🎉 所有坐标点击完成，共点击 ${indexes.length} 个`);
                        }
                    }, i * 300); // 每个点击间隔300ms
                });
            }
        } catch (error) {
            console.error('💥 点击图像失败:', error);
        }
    }

    // 添加一些辅助函数
    function waitForElement(selector, timeout = 5000) {
        return new Promise((resolve, reject) => {
            const element = document.querySelector(selector);
            if (element) {
                resolve(element);
                return;
            }

            const observer = new MutationObserver((mutations, obs) => {
                const element = document.querySelector(selector);
                if (element) {
                    obs.disconnect();
                    resolve(element);
                }
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });

            setTimeout(() => {
                observer.disconnect();
                reject(new Error(`元素 ${selector} 在 ${timeout}ms 内未找到`));
            }, timeout);
        });
    }

    function sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    console.log('验证码破解注入脚本已加载');
})();
