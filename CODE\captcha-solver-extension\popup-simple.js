// popup-simple.js - 简化的弹窗逻辑
console.log('🚀 Simple Popup loading...');

document.addEventListener('DOMContentLoaded', function() {
    console.log('📋 DOM loaded');
    
    // 获取元素
    const apiKeyInput = document.getElementById('apiKey');
    const saveBtn = document.getElementById('saveBtn');
    const detectBtn = document.getElementById('detectBtn');
    const solveBtn = document.getElementById('solveBtn');
    const status = document.getElementById('status');

    // 检查元素是否存在
    console.log('🔍 Elements found:', {
        apiKeyInput: !!apiKeyInput,
        saveBtn: !!saveBtn,
        detectBtn: !!detectBtn,
        solveBtn: !!solveBtn,
        status: !!status
    });

    // 加载保存的设置
    loadSettings();

    // 绑定事件
    if (saveBtn) {
        saveBtn.addEventListener('click', saveSettings);
    }

    if (detectBtn) {
        detectBtn.addEventListener('click', detectCaptcha);
    }

    if (solveBtn) {
        solveBtn.addEventListener('click', solveCaptcha);
    }

    // 监听来自content script的消息
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        console.log('📨 Received message:', message);
        
        if (message.action === 'updateStatus') {
            showStatus(message.message, message.type);
            updateExecutionStatus(message);
        }
    });

    // 加载设置
    function loadSettings() {
        chrome.storage.sync.get(['apiKey'], function(result) {
            if (result.apiKey && apiKeyInput) {
                apiKeyInput.value = result.apiKey;
            }
        });
    }

    // 保存设置
    function saveSettings() {
        const apiKey = apiKeyInput ? apiKeyInput.value.trim() : '';
        
        if (!apiKey) {
            showStatus('请输入API密钥', 'error');
            return;
        }

        chrome.storage.sync.set({
            apiKey: apiKey
        }, function() {
            showStatus('设置保存成功！', 'success');
        });
    }

    // 检测验证码
    function detectCaptcha() {
        showStatus('正在检测验证码...', 'info');
        
        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
            if (!tabs[0]) {
                showStatus('无法获取当前标签页', 'error');
                return;
            }

            chrome.tabs.sendMessage(tabs[0].id, {
                action: 'detectCaptcha'
            }, function(response) {
                if (chrome.runtime.lastError) {
                    console.error('Runtime error:', chrome.runtime.lastError);
                    showStatus('无法连接到页面，请刷新页面重试', 'error');
                    return;
                }

                if (response && response.success) {
                    showStatus(`发现 ${response.count || 0} 个验证码！`, 'success');
                } else {
                    showStatus('未发现验证码', 'error');
                }
            });
        });
    }

    // 破解验证码
    function solveCaptcha() {
        const apiKey = apiKeyInput ? apiKeyInput.value.trim() : '';
        
        if (!apiKey) {
            showStatus('请先输入API密钥', 'error');
            return;
        }

        // 显示执行状态
        const executionStatus = document.getElementById('executionStatus');
        if (executionStatus) {
            executionStatus.classList.add('active');
            resetStepList();
        }

        showStatus('正在破解验证码...', 'info');
        solveBtn.textContent = '🔄 破解中...';
        solveBtn.disabled = true;

        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
            if (!tabs[0]) {
                solveBtn.textContent = '🚀 破解验证码';
                solveBtn.disabled = false;
                showStatus('无法获取当前标签页', 'error');
                return;
            }

            chrome.tabs.sendMessage(tabs[0].id, {
                action: 'solveCaptcha',
                apiKey: apiKey
            }, function(response) {
                solveBtn.textContent = '🚀 破解验证码';
                solveBtn.disabled = false;

                if (chrome.runtime.lastError) {
                    console.error('Runtime error:', chrome.runtime.lastError);
                    showStatus('无法连接到页面，请刷新页面重试', 'error');
                    return;
                }

                if (response && response.success) {
                    showStatus('验证码破解成功！', 'success');
                } else {
                    showStatus(response ? response.error : '破解失败', 'error');
                }
            });
        });
    }

    // 显示状态
    function showStatus(message, type) {
        if (!status) return;
        
        status.textContent = message;
        status.className = `status ${type}`;
        status.style.display = 'block';
        
        // 3秒后隐藏
        setTimeout(() => {
            status.style.display = 'none';
        }, 3000);
    }

    // 更新执行状态
    function updateExecutionStatus(message) {
        const executionStatus = document.getElementById('executionStatus');
        const currentStepText = document.getElementById('currentStepText');
        const stepPercentage = document.getElementById('stepPercentage');
        const progressFill = document.getElementById('progressFill');
        const stepDetails = document.getElementById('stepDetails');

        if (!executionStatus) return;

        // 显示执行状态区域
        if (message.message.includes('步骤') || message.message.includes('破解')) {
            executionStatus.classList.add('active');
        }

        // 更新文本
        if (currentStepText) {
            currentStepText.textContent = message.message;
        }

        // 更新百分比
        if (stepPercentage && message.percentage !== undefined) {
            stepPercentage.textContent = `${message.percentage}%`;
        }

        // 更新进度条
        if (progressFill && message.percentage !== undefined) {
            progressFill.style.width = `${message.percentage}%`;
        }

        // 更新详细信息
        if (stepDetails && message.details) {
            stepDetails.textContent = message.details;
        }

        // 更新步骤状态
        updateStepList(message.currentStep, message.type);

        // 完成后隐藏
        if (message.type === 'success' && message.message.includes('成功')) {
            setTimeout(() => {
                executionStatus.classList.remove('active');
                resetStepList();
            }, 5000);
        }
    }

    // 更新步骤列表
    function updateStepList(currentStep, type) {
        if (!currentStep) return;

        for (let i = 1; i <= 8; i++) {
            const stepElement = document.getElementById(`step${i}`);
            if (!stepElement) continue;

            const iconElement = stepElement.querySelector('.step-icon');
            
            if (i < currentStep) {
                stepElement.className = 'step-item completed';
                iconElement.textContent = '✅';
            } else if (i === currentStep) {
                if (type === 'error') {
                    stepElement.className = 'step-item error';
                    iconElement.textContent = '❌';
                } else if (type === 'success') {
                    stepElement.className = 'step-item completed';
                    iconElement.textContent = '✅';
                } else {
                    stepElement.className = 'step-item current';
                    iconElement.textContent = '🔄';
                }
            } else {
                stepElement.className = 'step-item pending';
                iconElement.textContent = '⏳';
            }
        }
    }

    // 重置步骤列表
    function resetStepList() {
        for (let i = 1; i <= 8; i++) {
            const stepElement = document.getElementById(`step${i}`);
            if (stepElement) {
                stepElement.className = 'step-item pending';
                const iconElement = stepElement.querySelector('.step-icon');
                if (iconElement) {
                    iconElement.textContent = '⏳';
                }
            }
        }

        // 重置其他元素
        const currentStepText = document.getElementById('currentStepText');
        const stepPercentage = document.getElementById('stepPercentage');
        const progressFill = document.getElementById('progressFill');
        const stepDetails = document.getElementById('stepDetails');

        if (currentStepText) currentStepText.textContent = '准备开始...';
        if (stepPercentage) stepPercentage.textContent = '0%';
        if (progressFill) progressFill.style.width = '0%';
        if (stepDetails) stepDetails.textContent = '等待开始执行...';
    }
});

console.log('✅ Simple Popup script loaded');
