<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            width: 300px;
            height: 200px;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: 0;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 15px;
        }
        
        .btn {
            width: 100%;
            padding: 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            background: #4CAF50;
            color: white;
            margin-bottom: 10px;
        }
        
        .btn:hover {
            background: #45a049;
        }
        
        .status {
            text-align: center;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            font-size: 12px;
            background: rgba(33, 150, 243, 0.3);
            color: #bbdefb;
        }
    </style>
</head>
<body>
    <div class="header">
        <h2>🤖 测试弹窗</h2>
    </div>
    
    <div class="test-section">
        <button class="btn" id="testBtn">🧪 测试按钮</button>
        <button class="btn" id="consoleBtn">📝 控制台日志</button>
    </div>
    
    <div class="status" id="status">
        弹窗已加载成功！
    </div>
    
    <script>
        console.log('🚀 Test popup loaded');
        
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📋 Test DOM loaded');
            
            const testBtn = document.getElementById('testBtn');
            const consoleBtn = document.getElementById('consoleBtn');
            const status = document.getElementById('status');
            
            testBtn.addEventListener('click', function() {
                console.log('🧪 Test button clicked');
                status.textContent = '测试按钮已点击！';
                status.style.background = 'rgba(76, 175, 80, 0.3)';
            });
            
            consoleBtn.addEventListener('click', function() {
                console.log('📝 Console button clicked');
                console.log('Chrome extension APIs available:', {
                    storage: !!chrome.storage,
                    tabs: !!chrome.tabs,
                    runtime: !!chrome.runtime
                });
                status.textContent = '已输出控制台信息';
                status.style.background = 'rgba(255, 193, 7, 0.3)';
            });
            
            console.log('✅ Test popup initialized');
        });
    </script>
</body>
</html>
