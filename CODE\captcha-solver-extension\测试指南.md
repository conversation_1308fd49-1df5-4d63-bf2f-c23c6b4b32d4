# 🧪 浏览器插件测试指南

## 📋 测试步骤

### 1. 安装插件
1. 打开 Chrome 浏览器
2. 访问 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择 `captcha-solver-extension` 文件夹
6. 确认插件安装成功

### 2. 测试弹窗
1. 在浏览器工具栏找到插件图标 🤖
2. 点击图标，应该弹出测试窗口
3. 如果没有弹出，检查以下内容：

#### 🔍 故障排除

**A. 检查扩展是否正确加载**
- 在 `chrome://extensions/` 页面查看插件状态
- 确认插件已启用（开关是蓝色的）
- 查看是否有错误信息

**B. 检查控制台错误**
1. 右键点击插件图标
2. 选择"检查弹出式窗口"
3. 在开发者工具中查看 Console 标签
4. 查看是否有错误信息

**C. 检查权限**
- 确认插件有必要的权限
- 检查 manifest.json 是否正确

### 3. 测试功能
如果弹窗正常显示：
1. 点击"🧪 测试按钮" - 应该显示成功消息
2. 点击"📝 控制台日志" - 检查控制台输出
3. 观察状态变化

### 4. 常见问题

**Q: 点击插件图标没有反应？**
A: 
- 检查 manifest.json 中的 default_popup 路径
- 确认 test-popup.html 文件存在
- 查看扩展详情页面是否有错误

**Q: 弹窗显示空白？**
A:
- 检查 HTML 文件语法
- 查看控制台是否有 JavaScript 错误
- 确认 CSS 样式正确

**Q: 按钮点击无效？**
A:
- 检查 JavaScript 事件监听器
- 查看控制台错误信息
- 确认元素 ID 正确

### 5. 调试信息

在控制台中应该看到以下日志：
```
🚀 Test popup loaded
📋 Test DOM loaded
✅ Test popup initialized
```

点击按钮后应该看到：
```
🧪 Test button clicked
📝 Console button clicked
Chrome extension APIs available: {storage: true, tabs: true, runtime: true}
```

### 6. 切换回正式版本

测试完成后，如果要使用正式版本：
1. 修改 manifest.json
2. 将 `"default_popup": "test-popup.html"` 改为 `"default_popup": "popup.html"`
3. 在扩展页面点击"重新加载"按钮

## 🎯 预期结果

- ✅ 插件图标出现在工具栏
- ✅ 点击图标弹出测试窗口
- ✅ 窗口显示渐变背景和按钮
- ✅ 按钮点击有响应
- ✅ 控制台有正确的日志输出

如果以上都正常，说明插件基础功能工作正常，可以继续开发验证码破解功能。
