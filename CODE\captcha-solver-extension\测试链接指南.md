# 🧪 验证码破解插件测试指南

## 🎯 推荐测试链接

### 1. 官方演示页面（推荐新手）

#### Google reCAPTCHA 官方演示
- **链接**: https://www.google.com/recaptcha/api2/demo
- **优点**: 
  - ✅ 有明确的"验证成功"提示
  - ✅ 标准的reCAPTCHA实现
  - ✅ 无需注册账户
- **成功标志**: 页面显示绿色的"验证成功"消息

#### reCAPTCHA 测试站点
- **链接**: https://recaptcha-demo.appspot.com/
- **优点**:
  - ✅ 显示详细的验证结果
  - ✅ 有状态指示器
  - ✅ 可重复测试
- **成功标志**: 状态变为"Verified"

### 2. 专业测试页面（推荐调试）

#### 2captcha 演示页面
- **链接**: https://2captcha.com/demo/recaptcha-v2
- **优点**:
  - ✅ 专业验证码测试环境
  - ✅ 详细的状态显示
  - ✅ 适合调试和分析
- **成功标志**: 显示"CAPTCHA solved successfully"

#### NopeCHA 测试页面
- **链接**: https://nopecha.com/demo/recaptcha
- **优点**:
  - ✅ 简单直观的界面
  - ✅ 清晰的成功/失败反馈
  - ✅ 快速测试
- **成功标志**: 显示绿色的成功提示

### 3. 真实应用测试（推荐高级用户）

#### Google 账户注册
- **链接**: https://accounts.google.com/signup
- **优点**:
  - ✅ 真实的使用场景
  - ✅ 验证成功后可继续流程
  - ✅ 最接近实际使用
- **成功标志**: 验证码消失，可以继续填写表单

#### Discord 注册
- **链接**: https://discord.com/register
- **优点**:
  - ✅ 现代化的验证码实现
  - ✅ 明确的状态反馈
- **成功标志**: 验证码区域显示绿色勾号

## 🔍 如何判断测试成功

### 页面级别的成功标志

1. **验证码消失**
   - reCAPTCHA框架完全消失
   - 或显示绿色勾号

2. **成功消息**
   - "验证成功" / "Verification successful"
   - "CAPTCHA solved" / "验证通过"

3. **页面状态变化**
   - 可以继续下一步操作
   - 表单变为可提交状态

### 插件级别的成功标志

1. **插件弹窗显示**
   ```
   🎉 验证码破解成功！
   ```

2. **控制台日志**
   ```javascript
   [验证码破解] 🎉 验证码破解成功！
   [验证码破解] reCAPTCHA验证通过，任务完成！
   ```

3. **步骤完成**
   - 所有8个步骤都显示✅
   - 最后显示成功状态

## ⚠️ 常见的失败情况

### 1. 验证码重新加载
- **现象**: 图像网格刷新，出现新的验证码
- **原因**: AI识别错误，点击了错误的图像
- **解决**: 重新尝试，或检查API密钥

### 2. 验证码卡住不动
- **现象**: 点击后没有任何反应
- **原因**: 点击位置不准确或网络问题
- **解决**: 刷新页面重试

### 3. 显示"请重试"
- **现象**: 页面显示"请重试"或类似消息
- **原因**: 验证失败，需要重新验证
- **解决**: 点击重试或刷新页面

## 🧪 测试流程建议

### 新手测试流程
```
1. 访问 Google reCAPTCHA 官方演示
2. 点击插件图标
3. 输入API密钥
4. 点击"破解验证码"
5. 观察8个步骤的执行
6. 查看是否显示"验证成功"
```

### 进阶测试流程
```
1. 测试多个不同的验证码类型
2. 记录成功率和失败原因
3. 在不同网站上测试兼容性
4. 测试网络较慢时的表现
```

## 📊 测试记录模板

```
测试日期: ____
测试网站: ____
API密钥: 有效/无效
测试结果: 成功/失败
失败原因: ____
重试次数: ____
备注: ____
```

## 🔧 调试技巧

### 1. 查看详细日志
```
按F12 → Console标签 → 查看详细执行日志
```

### 2. 检查网络请求
```
按F12 → Network标签 → 查看API调用是否成功
```

### 3. 测试API密钥
```javascript
// 在控制台测试API密钥是否有效
fetch('https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent?key=YOUR_API_KEY', {
    method: 'POST',
    headers: {'Content-Type': 'application/json'},
    body: JSON.stringify({contents: [{parts: [{text: 'test'}]}]})
}).then(r => console.log('API状态:', r.status));
```

## 🎯 成功率优化建议

1. **使用稳定的网络环境**
2. **确保API密钥有足够配额**
3. **在页面完全加载后再使用插件**
4. **避免在高峰时段测试**
5. **选择图像清晰的验证码进行测试**

## 📞 如果测试失败

1. **检查基础环境**
   - Chrome版本是否支持
   - 插件是否正确安装
   - API密钥是否有效

2. **尝试不同测试页面**
   - 从简单的演示页面开始
   - 逐步测试复杂的真实页面

3. **查看错误日志**
   - 控制台错误信息
   - 插件状态提示
   - 网络请求状态

推荐从Google官方演示页面开始测试，这个页面最稳定且有明确的成功反馈！
