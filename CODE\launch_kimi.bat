@echo off
echo Starting Claude Code with Moonshot API...

REM 设置代理（如果需要，可以删除这两行）
set HTTP_PROXY=http://127.0.0.1:7897
set HTTPS_PROXY=http://127.0.0.1:7897

REM 启动 Claude Code with Moonshot API
wsl bash -c "cd /mnt/d/360MoveData/Users/<USER>/Desktop/Gemini_CLI && export ANTHROPIC_AUTH_TOKEN='sk-iooDE3jek5xXt7OlLSI9mEYS4AoLQzOu8KKVyWewsAiQgfSO' && export ANTHROPIC_BASE_URL='https://api.moonshot.cn' && claude"

pause
