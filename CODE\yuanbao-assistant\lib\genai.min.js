/**
 * 谷歌 Gemini API 简化客户端
 * 此文件替换了从CDN加载的@google/genai库
 * 提供了与原始库类似的接口
 */

(function(global) {
  'use strict';

  // 定义GoogleGenAI主类
  class GoogleGenAI {
    constructor(options) {
      if (typeof options === 'string') {
        this.apiKey = options;
      } else {
        this.apiKey = options?.apiKey || '';
      }
    }

    // 获取生成式模型实例
    getGenerativeModel(options) {
      return new GenerativeModel(options.model, this.apiKey);
    }
  }

  // 生成式模型类
  class GenerativeModel {
    constructor(model, apiKey) {
      this.model = model;
      this.apiKey = apiKey;
    }

    // 生成内容方法
    async generateContent(options) {
      // 确保模型ID不包含前缀
      let modelId = this.model;
      if (modelId.startsWith('models/')) {
        modelId = modelId.substring(7);
        console.log('Gemini API: 删除模型ID前缀，清理后的ID:', modelId);
      }
      
      const apiUrl = `https://generativelanguage.googleapis.com/v1/models/${modelId}:generateContent?key=${this.apiKey}`;
      console.log('Gemini API: 发送请求到:', apiUrl.replace(this.apiKey, '[隐藏]'));
      
      try {
        const response = await fetch(apiUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            contents: options.contents || []
          })
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error?.message || `API错误 (${response.status})`);
        }

        const data = await response.json();
        
        // 返回与原始API类似的响应格式
        return {
          response: {
            text: () => {
              if (data.candidates && data.candidates[0] && data.candidates[0].content) {
                return data.candidates[0].content.parts[0].text;
              }
              return '';
            }
          }
        };
      } catch (error) {
        console.error('Gemini API 调用失败:', error);
        throw error;
      }
    }
  }

  // 将类暴露给全局作用域
  global.GoogleGenAI = GoogleGenAI;

})(typeof self !== 'undefined' ? self : this); 