<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>元宝助手 - 设置</title>
  <link rel="stylesheet" href="options.css">
</head>
<body>
  <div class="container">
    <header>
      <div class="text-logo">元</div>
      <h1>元宝助手<span class="version">1.0</span></h1>
      <p class="subtitle">配置AI模型访问及其他选项</p>
    </header>
    
    <main>
      <section class="card">
        <h2>🔄 OpenRouter API设置</h2>
        <p class="description">
          配置OpenRouter API密钥，用于访问多种AI模型，密钥将安全地存储在您的浏览器中
        </p>
        
        <div class="form-group">
          <label for="geminiApiKey">OpenRouter API密钥</label>
          <div class="input-container">
            <input type="password" id="geminiApiKey" class="api-key-input" placeholder="输入您的OpenRouter API密钥">
            <button class="toggle-visibility" data-for="geminiApiKey">👁️</button>
          </div>
          <p class="help-text">
            在 <a href="https://openrouter.ai/keys" target="_blank">OpenRouter控制台</a> 获取API密钥
          </p>
          <div class="api-test-container">
            <button id="testOpenRouterApiBtn" class="secondary-btn">测试OpenRouter连接</button>
            <div id="openRouterApiTestResult" class="api-test-result" style="display: none;"></div>
          </div>
        </div>
      </section>
      
      <section class="card">
        <h2>🌐 Gemini 原生API设置</h2>
        <p class="description">
          配置Google Gemini原生API密钥，直接访问Gemini模型，无需中间服务商
        </p>
        
        <div class="form-group">
          <label for="nativeApiKey">Gemini API密钥</label>
          <div class="input-container">
            <input type="password" id="nativeApiKey" class="api-key-input" placeholder="输入您的Gemini API密钥">
            <button class="toggle-visibility" data-for="nativeApiKey">👁️</button>
          </div>
          <p class="help-text">
            在 <a href="https://makersuite.google.com/app/apikey" target="_blank">Google AI Studio</a> 获取API密钥
          </p>
          <div class="api-test-container">
            <button id="testNativeApiBtn" class="secondary-btn">测试Gemini原生连接</button>
            <div id="nativeApiTestResult" class="api-test-result" style="display: none;"></div>
          </div>
        </div>
      </section>
      
      <section class="card">
        <h2>⚙️ 基本设置</h2>
        
        <div class="form-group">
          <label for="defaultMode">默认提取模式</label>
          <select id="defaultMode" class="select-input">
            <option value="full">完整回答</option>
            <option value="thinking">仅思考部分</option>
          </select>
          <p class="help-text">
            选择默认从元宝回答中提取的内容
          </p>
        </div>
        
        <div class="form-group">
          <label for="defaultModel">默认使用模型</label>
          <select id="defaultModel" class="select-input">
            <option value="gemini">Gemini 2.5 Pro</option>
          </select>
          <p class="help-text">
            默认使用的AI模型
          </p>
        </div>
        
        <div class="form-group">
          <label for="defaultApiType">默认API类型</label>
          <select id="defaultApiType" class="select-input">
            <option value="openrouter">OpenRouter（多模型集成）</option>
            <option value="native">Gemini 原生 API</option>
          </select>
          <p class="help-text">
            选择默认使用的API类型
          </p>
        </div>
        
        <div class="form-group">
          <label for="defaultPrompt">默认提示词</label>
          <textarea id="defaultPrompt" class="textarea-input" rows="4" placeholder="输入默认提示词模板"></textarea>
          <p class="help-text">
            设置默认的提示词模板
          </p>
        </div>
      </section>
      
      <section class="card">
        <h2>🧩 高级设置</h2>
        
        <div class="form-group checkbox-group">
          <input type="checkbox" id="autoDetect" class="checkbox-input">
          <label for="autoDetect">自动检测元宝回答</label>
          <p class="help-text">
            当检测到元宝回答时自动启动元宝助手
          </p>
        </div>
        
        <div class="form-group checkbox-group">
          <input type="checkbox" id="saveHistory" class="checkbox-input">
          <label for="saveHistory">保存优化历史</label>
          <p class="help-text">
            保存最近的20条优化记录
          </p>
        </div>
        
        <div class="form-group">
          <div class="button-group">
            <button id="clearHistoryBtn" class="secondary-btn">清除历史记录</button>
            <button id="exportHistoryBtn" class="secondary-btn">导出历史记录</button>
          </div>
          <p class="help-text">
            清除或导出所有保存的优化历史记录
          </p>
        </div>

        <div class="form-group checkbox-group">
          <input type="checkbox" id="debugMode" class="checkbox-input">
          <label for="debugMode">调试模式</label>
          <p class="help-text">
            启用调试日志和开发者选项
          </p>
        </div>
      </section>
    </main>
    
    <div class="actions">
      <button id="saveBtn" class="primary-btn">保存设置</button>
      <button id="resetBtn" class="secondary-btn">恢复默认设置</button>
    </div>
    
    <div id="statusMessage" class="status-message"></div>
    
    <footer>
      <p>元宝助手 v1.0.0 | <a href="https://openrouter.ai/keys" target="_blank">获取OpenRouter API密钥</a> | <a href="https://makersuite.google.com/app/apikey" target="_blank">获取Gemini API密钥</a></p>
    </footer>
  </div>
  
  <script src="options.js"></script>
</body>
</html> 