/**
 * 元宝助手 - 设置页面脚本
 * 处理用户设置的保存和加载
 */

document.addEventListener('DOMContentLoaded', function() {
  // 获取DOM元素引用
  const geminiApiKeyInput = document.getElementById('geminiApiKey');
  const nativeApiKeyInput = document.getElementById('nativeApiKey');
  const defaultModeSelect = document.getElementById('defaultMode');
  const defaultModelSelect = document.getElementById('defaultModel');
  const defaultApiTypeSelect = document.getElementById('defaultApiType');
  const defaultPromptInput = document.getElementById('defaultPrompt');
  const autoDetectCheckbox = document.getElementById('autoDetect');
  const saveHistoryCheckbox = document.getElementById('saveHistory');
  const debugModeCheckbox = document.getElementById('debugMode');
  const clearHistoryBtn = document.getElementById('clearHistoryBtn');
  const exportHistoryBtn = document.getElementById('exportHistoryBtn');
  const saveBtn = document.getElementById('saveBtn');
  const resetBtn = document.getElementById('resetBtn');
  const statusMessage = document.getElementById('statusMessage');
  const toggleVisibilityBtns = document.querySelectorAll('.toggle-visibility');
  
  // 默认设置
  const defaultSettings = {
    geminiApiKey: '',
    nativeApiKey: '',
    defaultMode: 'full',
    defaultModel: 'gemini',
    defaultApiType: 'openrouter',
    defaultPrompt: '请根据提供内容逻辑框架生成回答，要更具体、更细化、富有条理。请注意，不是单纯修改，可以改变文风文体，补充不足。',
    autoDetect: true,
    saveHistory: true,
    debugMode: false
  };
  
  // 加载已保存的设置
  loadSettings();
  
  // 处理保存按钮点击
  saveBtn.addEventListener('click', saveSettings);
  
  // 处理重置按钮点击
  resetBtn.addEventListener('click', resetSettings);
  
  // 处理清除历史按钮点击
  clearHistoryBtn.addEventListener('click', clearHistory);
  
  // 处理导出历史按钮点击
  exportHistoryBtn.addEventListener('click', exportHistory);
  
  // 处理密码可见性切换
  toggleVisibilityBtns.forEach(btn => {
    btn.addEventListener('click', function() {
      const inputId = this.getAttribute('data-for');
      const input = document.getElementById(inputId);
      if (input.type === 'password') {
        input.type = 'text';
        this.textContent = '🔒';
      } else {
        input.type = 'password';
        this.textContent = '👁️';
      }
    });
  });
  
  // 处理OpenRouter API测试按钮
  const testOpenRouterApiBtn = document.getElementById('testOpenRouterApiBtn');
  const openRouterApiTestResult = document.getElementById('openRouterApiTestResult');
  
  testOpenRouterApiBtn.addEventListener('click', function() {
    const apiKey = geminiApiKeyInput.value.trim();
    if (!apiKey) {
      showApiTestResult('error', 'OpenRouter API密钥不能为空', openRouterApiTestResult);
      return;
    }
    
    testOpenRouterApiConnection(apiKey);
  });
  
  // 处理原生API测试按钮
  const testNativeApiBtn = document.getElementById('testNativeApiBtn');
  const nativeApiTestResult = document.getElementById('nativeApiTestResult');
  
  testNativeApiBtn.addEventListener('click', function() {
    const apiKey = nativeApiKeyInput.value.trim();
    if (!apiKey) {
      showApiTestResult('error', 'Gemini API密钥不能为空', nativeApiTestResult);
      return;
    }
    
    testNativeApiConnection(apiKey);
  });
  
  /**
   * 加载已保存的设置
   */
  function loadSettings() {
    chrome.storage.sync.get(defaultSettings, function(items) {
      // 应用加载的设置到DOM元素
      geminiApiKeyInput.value = items.geminiApiKey || '';
      nativeApiKeyInput.value = items.nativeApiKey || '';
      defaultModeSelect.value = items.defaultMode;
      defaultModelSelect.value = items.defaultModel;
      defaultApiTypeSelect.value = items.defaultApiType || 'openrouter';
      defaultPromptInput.value = items.defaultPrompt;
      autoDetectCheckbox.checked = items.autoDetect;
      saveHistoryCheckbox.checked = items.saveHistory;
      debugModeCheckbox.checked = items.debugMode;
      
      console.log('元宝助手: 设置已加载');
    });
  }
  
  /**
   * 保存设置
   */
  function saveSettings() {
    // 收集当前设置
    const settings = {
      geminiApiKey: geminiApiKeyInput.value.trim(),
      nativeApiKey: nativeApiKeyInput.value.trim(),
      defaultMode: defaultModeSelect.value,
      defaultModel: defaultModelSelect.value,
      defaultApiType: defaultApiTypeSelect.value,
      defaultPrompt: defaultPromptInput.value.trim(),
      autoDetect: autoDetectCheckbox.checked,
      saveHistory: saveHistoryCheckbox.checked,
      debugMode: debugModeCheckbox.checked
    };
    
    // 保存设置到存储
    chrome.storage.sync.set(settings, function() {
      // 验证API密钥设置
      if (settings.defaultApiType === 'openrouter' && !settings.geminiApiKey) {
        showStatus('使用OpenRouter需要设置OpenRouter API密钥', 'error');
        return;
      }
      
      if (settings.defaultApiType === 'native' && !settings.nativeApiKey) {
        showStatus('使用原生API需要设置Gemini API密钥', 'error');
        return;
      }
      
      // 同步设置到背景脚本
      chrome.runtime.sendMessage({
        action: 'settingsUpdated',
        settings: settings
      });
      
      console.log('元宝助手: 设置已保存');
      showStatus('设置已成功保存！', 'success');
    });
  }
  
  /**
   * 重置设置为默认值
   */
  function resetSettings() {
    if (confirm('确定要恢复所有设置为默认值吗？这将清除您的所有自定义设置。')) {
      // 将DOM元素重置为默认值
      geminiApiKeyInput.value = defaultSettings.geminiApiKey;
      nativeApiKeyInput.value = defaultSettings.nativeApiKey;
      defaultModeSelect.value = defaultSettings.defaultMode;
      defaultModelSelect.value = defaultSettings.defaultModel;
      defaultApiTypeSelect.value = defaultSettings.defaultApiType;
      defaultPromptInput.value = defaultSettings.defaultPrompt;
      autoDetectCheckbox.checked = defaultSettings.autoDetect;
      saveHistoryCheckbox.checked = defaultSettings.saveHistory;
      debugModeCheckbox.checked = defaultSettings.debugMode;
      
      // 保存默认设置到存储
      chrome.storage.sync.set(defaultSettings, function() {
        console.log('元宝助手: 设置已重置为默认值');
        showStatus('所有设置已重置为默认值。', 'success');
      });
    }
  }
  
  /**
   * 清除存储的历史记录
   */
  function clearHistory() {
    if (confirm('确定要清除所有保存的历史记录吗？此操作无法撤销。')) {
      // 同时从sync和local存储中清除历史记录
      Promise.all([
        chrome.storage.sync.remove('savedResults'),
        chrome.storage.local.remove('savedResults')
      ]).then(() => {
        console.log('元宝助手: 所有历史记录已清除（同步和本地存储）');
        showStatus('所有历史记录已清除（同步和本地存储）。', 'success');
      }).catch(error => {
        console.error('元宝助手: 清除历史记录失败', error);
        showStatus('清除历史记录失败: ' + error.message, 'error');
      });
    }
  }
  
  /**
   * 导出历史记录
   */
  function exportHistory() {
    // 同时从sync和local存储获取历史记录
    Promise.all([
      chrome.storage.sync.get({savedResults: []}),
      chrome.storage.local.get({savedResults: []})
    ]).then(([syncData, localData]) => {
      const syncResults = syncData.savedResults || [];
      const localResults = localData.savedResults || [];
      
      console.log('元宝助手: 准备导出历史记录', {
        syncCount: syncResults.length,
        localCount: localResults.length
      });
      
      // 合并记录（如果有相同时间戳的记录，以sync为准）
      let allResults = [...syncResults];
      
      // 添加不在sync中的local记录
      localResults.forEach(localItem => {
        // 检查是否已存在相同时间戳的记录
        const exists = allResults.some(item => item.timestamp === localItem.timestamp);
        if (!exists) {
          allResults.push(localItem);
        }
      });
      
      // 按时间戳排序（最新的在前）
      allResults.sort((a, b) => b.timestamp - a.timestamp);
      
      const savedResults = allResults;
      
      if (savedResults.length === 0) {
        showStatus('没有历史记录可供导出', 'error');
        return;
      }
      
      // 创建导出选项对话框
      const exportDialog = document.createElement('div');
      exportDialog.className = 'export-dialog';
      exportDialog.innerHTML = `
        <div class="export-dialog-content">
          <h3>导出历史记录</h3>
          <p>请选择导出格式:</p>
          <div class="export-options">
            <button id="exportJson" class="secondary-btn">JSON格式</button>
            <button id="exportTxt" class="secondary-btn">TXT格式</button>
          </div>
          <button id="cancelExport" class="text-btn">取消</button>
        </div>
      `;
      
      // 添加样式
      const dialogStyle = document.createElement('style');
      dialogStyle.textContent = `
        .export-dialog {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 1000;
        }
        .export-dialog-content {
          background: white;
          padding: 20px;
          border-radius: 8px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          width: 350px;
          text-align: center;
        }
        .export-options {
          display: flex;
          gap: 10px;
          margin: 20px 0;
          justify-content: center;
        }
        .text-btn {
          background: none;
          border: none;
          color: #666;
          cursor: pointer;
          padding: 8px;
        }
        .text-btn:hover {
          text-decoration: underline;
        }
      `;
      
      document.body.appendChild(dialogStyle);
      document.body.appendChild(exportDialog);
      
      // 添加事件监听器
      document.getElementById('exportJson').addEventListener('click', function() {
        exportAsJson(savedResults);
        document.body.removeChild(exportDialog);
        document.body.removeChild(dialogStyle);
      });
      
      document.getElementById('exportTxt').addEventListener('click', function() {
        exportAsTxt(savedResults);
        document.body.removeChild(exportDialog);
        document.body.removeChild(dialogStyle);
      });
      
      document.getElementById('cancelExport').addEventListener('click', function() {
        document.body.removeChild(exportDialog);
        document.body.removeChild(dialogStyle);
      });
    }).catch(error => {
      console.error('元宝助手: 获取历史记录失败', error);
      showStatus('获取历史记录失败: ' + error.message, 'error');
    });
  }
  
  /**
   * 导出为JSON格式
   * @param {Array} savedResults - 保存的历史记录
   */
  function exportAsJson(savedResults) {
    // 准备导出数据
    const exportData = {
      timestamp: new Date().toISOString(),
      version: '1.0',
      source: '元宝助手历史记录',
      results: savedResults
    };
    
    // 转换为JSON字符串，并美化格式
    const jsonString = JSON.stringify(exportData, null, 2);
    
    // 创建Blob对象
    const blob = new Blob([jsonString], {type: 'application/json'});
    
    // 创建下载链接
    const url = URL.createObjectURL(blob);
    
    // 创建临时下载链接并点击
    const downloadLink = document.createElement('a');
    downloadLink.href = url;
    downloadLink.download = `元宝助手历史记录_${formatDate(new Date())}.json`;
    document.body.appendChild(downloadLink);
    downloadLink.click();
    
    // 清理
    setTimeout(() => {
      document.body.removeChild(downloadLink);
      URL.revokeObjectURL(url);
    }, 100);
    
    showStatus(`成功导出 ${savedResults.length} 条历史记录为JSON格式`, 'success');
  }
  
  /**
   * 导出为TXT格式
   * @param {Array} savedResults - 保存的历史记录
   */
  function exportAsTxt(savedResults) {
    // 创建TXT内容
    let txtContent = '==== 元宝助手历史记录 ====\n\n';
    txtContent += `导出时间：${new Date().toLocaleString()}\n\n`;
    
    // 添加每条历史记录
    savedResults.forEach((item, index) => {
      const date = new Date(item.timestamp).toLocaleString();
      
      txtContent += `===== 记录 ${index + 1} =====\n`;
      txtContent += `时间：${date}\n`;
      txtContent += `模型：${item.model || '未知模型'}\n`;
      txtContent += `提示词：${item.prompt}\n\n`;
      txtContent += `原始内容：${item.original}\n\n`;
      txtContent += `优化结果：\n${item.optimized}\n\n`;
      txtContent += `${'='.repeat(30)}\n\n`;
    });
    
    // 将内容转换为Blob对象
    const blob = new Blob([txtContent], {type: 'text/plain;charset=utf-8'});
    
    // 创建下载链接
    const url = URL.createObjectURL(blob);
    
    // 创建临时下载链接并点击
    const downloadLink = document.createElement('a');
    downloadLink.href = url;
    downloadLink.download = `元宝助手历史记录_${formatDate(new Date())}.txt`;
    document.body.appendChild(downloadLink);
    downloadLink.click();
    
    // 清理
    setTimeout(() => {
      document.body.removeChild(downloadLink);
      URL.revokeObjectURL(url);
    }, 100);
    
    showStatus(`成功导出 ${savedResults.length} 条历史记录为TXT格式`, 'success');
  }
  
  /**
   * 格式化日期为文件名友好格式
   * @param {Date} date - 日期对象
   * @returns {string} 格式化的日期字符串
   */
  function formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    return `${year}${month}${day}_${hours}${minutes}`;
  }
  
  /**
   * 显示状态消息
   * @param {string} message - 要显示的消息
   * @param {string} type - 消息类型 ('success' 或 'error')
   */
  function showStatus(message, type) {
    statusMessage.textContent = message;
    statusMessage.className = 'status-message ' + type;
    statusMessage.style.display = 'block';
    
    // 5秒后自动隐藏状态消息
    setTimeout(() => {
      statusMessage.style.display = 'none';
    }, 5000);
  }
  
  /**
   * 测试OpenRouter API连接
   */
  async function testOpenRouterApiConnection(apiKey) {
    // 显示测试中状态
    showApiTestResult('loading', '正在测试OpenRouter API连接...', openRouterApiTestResult);
    
    try {
      // OpenRouter API端点
      const apiUrl = 'https://openrouter.ai/api/v1/models';
      
      // 发送API请求
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'HTTP-Referer': 'https://github.com/yuanbao-assistant',
          'X-Title': 'YuanbaoAssistant'
        }
      });
      
      if (!response.ok) {
        throw new Error(`API错误: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      
      // 检查响应格式
      if (!Array.isArray(data.data)) {
        throw new Error('API响应格式无效');
      }
      
      // 计算可用模型数量
      const modelCount = data.data.length;
      
      // 显示成功结果
      showApiTestResult('success', 
        `OpenRouter连接成功！可用模型: ${modelCount}个`, 
        openRouterApiTestResult
      );
    } catch (error) {
      console.error('元宝助手: OpenRouter API测试失败', error);
      showApiTestResult('error', `连接失败: ${error.message}`, openRouterApiTestResult);
    }
  }
  
  /**
   * 测试原生Gemini API连接
   */
  async function testNativeApiConnection(apiKey) {
    // 显示测试中状态
    showApiTestResult('loading', '正在测试Gemini原生API连接...', nativeApiTestResult);
    
    try {
      // 使用最简单的模型进行测试
      const model = 'gemini-2.0-flash';
      // Gemini API端点
      const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${model}?key=${apiKey}`;
      
      // 发送API请求
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        throw new Error(`API错误: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      
      // 检查响应格式
      if (!data.name || !data.name.includes('gemini')) {
        throw new Error('API响应格式无效');
      }
      
      // 显示成功结果
      showApiTestResult('success', 
        `Gemini原生API连接成功！已确认: ${data.displayName || data.name}`, 
        nativeApiTestResult
      );
    } catch (error) {
      console.error('元宝助手: Gemini原生API测试失败', error);
      showApiTestResult('error', `连接失败: ${error.message}`, nativeApiTestResult);
    }
  }
  
  /**
   * 显示API测试结果
   */
  function showApiTestResult(type, message, resultElement) {
    resultElement.innerHTML = message;
    resultElement.className = 'api-test-result ' + type;
    resultElement.style.display = 'block';
    
    // 10秒后自动隐藏结果
    setTimeout(() => {
      resultElement.style.display = 'none';
    }, 10000);
  }
}); 