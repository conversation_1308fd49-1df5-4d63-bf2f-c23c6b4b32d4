/**
 * 元宝助手 - 弹出窗口脚本
 * 处理用户界面交互和与背景脚本、内容脚本的通信
 */

document.addEventListener('DOMContentLoaded', function() {
  // DOM元素引用
  const statusElement = document.getElementById('status');
  const promptElement = document.getElementById('prompt');
  const submitButton = document.getElementById('submitBtn');
  const resultElement = document.getElementById('result');
  const resultContainer = document.getElementById('resultContainer');
  const copyButton = document.getElementById('copyBtn');
  const modeToggle = document.getElementById('modeToggle');
  const modelSelect = document.getElementById('modelSelect');
  const nativeModelSelect = document.getElementById('nativeModelSelect');
  const apiTypeSelect = document.getElementById('apiTypeSelect');
  const openrouterModelContainer = document.getElementById('openrouterModelContainer');
  const nativeModelContainer = document.getElementById('nativeModelContainer');
  const statusMessage = document.getElementById('statusMessage');
  
  // 获取手动检测按钮（确保获取现有按钮）
  const checkButton = document.getElementById('checkBtn');
  console.log('检测按钮状态:', checkButton ? '已找到' : '未找到');
  
  // 历史记录相关元素
  const historyBtn = document.getElementById('historyBtn');
  const historyPanel = document.getElementById('historyPanel');
  const closeHistoryBtn = document.getElementById('closeHistoryBtn');
  const historyList = document.getElementById('historyList');
  const exportHistoryFromPopupBtn = document.getElementById('exportHistoryFromPopupBtn');
  
  // 监听来自background.js的进度更新消息
  chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
    if (request.action === 'updateProgressStatus') {
      // 处理真实进度更新
      log('收到进度更新', request);
      // 确保modelName存在，避免undefined错误
      const modelName = request.modelName || null;
      updateProgressStatus(request.status, request.message, modelName);
      return true;
    }
  });
  
  // 监听API状态更新事件
  chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
    if (request.action === 'apiStatusUpdate') {
      debugLog('STATUS', `收到API状态更新: ${request.status}`, request);
      
      // 显示API状态容器
      const apiStatusContainer = document.getElementById('apiStatusContainer');
      if (request.status && request.status !== 'waiting' && request.status !== 'detected') {
        apiStatusContainer.style.display = 'block';
      }
      
      // 更新底部状态栏以匹配API状态
      if (request.status) {
        const statusMessage = document.getElementById('statusMessage');
        const statusElement = document.getElementById('status');
        
        if (statusMessage) {
          const currentText = statusMessage.textContent;
          switch (request.status) {
            case 'preparing':
              statusMessage.textContent = "正在准备优化请求...";
              break;
            case 'sending':
              statusMessage.textContent = "正在发送优化请求...";
              break;
            case 'processing':
              statusMessage.textContent = `正在通过${request.model || 'AI'} 优化..`;
              if (statusElement) {
                statusElement.textContent = `正在通过${request.model || 'AI'} 优化...`;
              }
              break;
            case 'receiving':
              statusMessage.textContent = "正在接收优化结果..";
              break;
            case 'finalizing':
              statusMessage.textContent = "优化即将完成..";
              break;
            case 'completed':
              statusMessage.textContent = "优化已完成";
              if (statusElement) {
                statusElement.textContent = "优化完成!";
              }
              debugLog('STATUS', `状态文本从"${currentText}"更新为"优化已完成"`);
              break;
            case 'error':
              statusMessage.textContent = "优化失败";
              if (statusElement) {
                statusElement.textContent = "处理出错，请重试";
              }
              debugLog('ERROR', `状态文本从"${currentText}"更新为"优化失败"`);
              break;
          }
        }
      }
      
      // 更新进度状态
      updateProgressStatus(
        request.status || 'loading',
        request.message || '处理中...',
        request.model || null
      );
      
      // 更新API状态显示
      showApiStatus(
        request.status === 'completed' ? 'success' : 
        request.status === 'error' ? 'error' : 'loading', 
        request.message || '处理中...'
      );
      
      // 如果状态是已完成，确保结果显示
      if (request.status === 'completed') {
        setTimeout(ensureResultDisplay, 100);
      }
      
      // 发送响应
      sendResponse({received: true});
      return true;
    }
    // 其他消息处理逻辑不变
    return false;
  });
  
  // 状态变量
  let currentStatus = 'waiting';
  let optimizedResult = '';
  let extractionMode = modeToggle.checked ? 'full' : 'thinking';
  let selectedModel = modelSelect.value;
  let selectedNativeModel = nativeModelSelect.value;
  let selectedApiType = apiTypeSelect.value;
  let detectionAttempts = 0;
  
  const loadingMessages = [
    "正在等待响应",
    "请稍候",
    "处理中"
  ];
  
  let currentMessageIndex = 0;
  let loadingInterval;
  
  function updateLoadingMessage() {
    // 只有在没有收到真实进度更新时才使用简单循环动画
    if (!document.querySelector('.api-status').classList.contains('preparing') &&
        !document.querySelector('.api-status').classList.contains('sending') &&
        !document.querySelector('.api-status').classList.contains('processing') &&
        !document.querySelector('.api-status').classList.contains('receiving') &&
        !document.querySelector('.api-status').classList.contains('finalizing') &&
        !document.querySelector('.api-status').classList.contains('completed') &&
        !document.querySelector('.api-status').classList.contains('error')) {
          
      const loadingMessage = document.querySelector('.loading-message');
      const loadingSubstatus = document.querySelector('.loading-substatus');
      
      if (loadingMessage && loadingSubstatus) {
        loadingMessage.textContent = loadingMessages[currentMessageIndex];
        loadingSubstatus.textContent = '正在处理中...';
        currentMessageIndex = (currentMessageIndex + 1) % loadingMessages.length;
      }
    }
  }
  
  function startLoadingAnimation() {
    log('开始加载动画');
    currentMessageIndex = 0;
    if (loadingInterval) {
      clearInterval(loadingInterval);
    }
    
    const apiStatus = document.getElementById('apiStatus');
    if (apiStatus) {
      apiStatus.className = 'api-status loading';
    }
    
    loadingInterval = setInterval(updateLoadingMessage, 2000);
    updateLoadingMessage();
  }
  
  function stopLoadingAnimation() {
    log('停止加载动画');
    if (loadingInterval) {
      clearInterval(loadingInterval);
      loadingInterval = null;
    }
  }
  
  // 初始化: 获取当前状态并更新UI
  initializePopup();
  
  // 在控制台和调试区域输出日志
  function log(message, data = null) {
    const timestamp = new Date().toLocaleTimeString();
    let logMessage = `[${timestamp}] ${message}`;
    
    if (data) {
      console.log(logMessage, data);
      if (typeof data === 'object') {
        logMessage += ' ' + JSON.stringify(data);
      } else {
        logMessage += ' ' + data;
      }
    } else {
      console.log(logMessage);
    }
    
    const debugOutput = document.getElementById('debugOutput');
    if (debugOutput) {
      debugOutput.innerHTML = logMessage + '\n' + debugOutput.innerHTML;
      
      // 限制日志数量
      if (debugOutput.innerHTML.split('\n').length > 50) {
        const lines = debugOutput.innerHTML.split('\n');
        debugOutput.innerHTML = lines.slice(0, 50).join('\n');
      }
    }
    
    // 存储日志
    chrome.storage.local.get({debugLogs: []}, function(items) {
      let logs = items.debugLogs || [];
      logs.unshift({
        time: Date.now(),
        message: logMessage
      });
      
      // 最多保存100条日志
      if (logs.length > 100) {
        logs = logs.slice(0, 100);
      }
      
      chrome.storage.local.set({debugLogs: logs});
    });
  }
  
  // 加载调试日志
  function loadDebugLogs() {
    chrome.storage.local.get({debugLogs: []}, function(items) {
      const logs = items.debugLogs || [];
      if (logs.length === 0) {
        debugOutput.textContent = '没有日志记录';
        return;
      }
      
      debugOutput.innerHTML = logs.map(log => log.message).join('\n');
    });
  }
  
  /**
   * 初始化弹出窗口
   * 从背景脚本获取当前状态并加载用户设置
   */
  function initializePopup() {
    log('初始化弹出窗口');
    // 从存储中获取用户设置
    chrome.storage.sync.get({
      extractionMode: 'full',
      selectedModel: 'gemini',
      selectedNativeModel: 'gemini-2.5-pro-exp-03-25',
      defaultApiType: 'openrouter',
      defaultPrompt: '请根据提供内容逻辑框架生成回答，要更具体、更细化、富有条理。请注意，不是单纯修改，可以改变文风文体，补充不足。',
      customModelId: ''
    }, function(items) {
      // 应用存储的设置
      extractionMode = items.extractionMode;
      modeToggle.checked = extractionMode === 'full';
      selectedModel = items.selectedModel;
      selectedNativeModel = items.selectedNativeModel || 'gemini-2.5-pro-exp-03-25';
      selectedApiType = items.defaultApiType || 'openrouter';
      
      // 设置下拉菜单值
      modelSelect.value = selectedModel;
      nativeModelSelect.value = selectedNativeModel;
      apiTypeSelect.value = selectedApiType;
      
      // 根据API类型显示对应的模型选择区域
      updateModelContainerVisibility();
      
      // 总是从存储加载最新的提示语
      promptElement.value = items.defaultPrompt;
      
      // 处理自定义模型选项
      if (selectedModel === 'custom') {
        document.getElementById('customModelContainer').style.display = 'block';
        document.getElementById('customModelId').value = items.customModelId || '';
      } else {
        document.getElementById('customModelContainer').style.display = 'none';
      }
      
      // 获取当前标签页ID
      chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
        if (tabs.length > 0) {
          const tabId = tabs[0].id;
          log('当前标签页ID:', tabId);
          
          // 从背景脚本获取当前状态
          chrome.runtime.sendMessage({action: 'getStatus', tabId: tabId}, function(response) {
            log('收到状态响应', response);
            
            // 如果无法获取状态，可能是背景脚本未初始化
            if (chrome.runtime.lastError) {
              log('获取状态失败', chrome.runtime.lastError);
              return;
            }
            
            if (response && response.status) {
              // 更新状态和UI
              updateStatus(response.status);
              
              // 如果存在结果，显示结果
              if (response.result) {
                optimizedResult = response.result;
                resultElement.textContent = optimizedResult;
                resultContainer.style.display = 'block';
              }
            }
          });
        }
      });
    });
    
    // 监听API类型切换
    apiTypeSelect.addEventListener('change', function() {
      selectedApiType = this.value;
      updateModelContainerVisibility();
      
      // 保存设置
      chrome.storage.sync.set({defaultApiType: selectedApiType});
    });
    
    // 监听模型选择
    modelSelect.addEventListener('change', function() {
      selectedModel = this.value;
      
      // 处理自定义模型选项
      if (selectedModel === 'custom') {
        document.getElementById('customModelContainer').style.display = 'block';
      } else {
        document.getElementById('customModelContainer').style.display = 'none';
      }
      
      // 保存设置
      chrome.storage.sync.set({selectedModel: selectedModel});
    });
    
    // 监听原生模型选择
    nativeModelSelect.addEventListener('change', function() {
      selectedNativeModel = this.value;
      
      // 保存设置
      chrome.storage.sync.set({selectedNativeModel: selectedNativeModel});
    });
    
    // 设置模式切换事件处理
    modeToggle.addEventListener('change', function() {
      extractionMode = this.checked ? 'full' : 'thinking';
      // 保存用户偏好
      chrome.storage.sync.set({extractionMode: extractionMode});
    });
    
    // 如果有手动检测按钮，设置点击事件
    if (checkButton) {
      checkButton.addEventListener('click', function() {
        console.log('检测按钮被点击');
        statusMessage.textContent = "正在检测页面...";
        detectionAttempts++;
        checkButton.disabled = true;
        checkButton.textContent = "检测中...";
        
        log('开始手动检测，尝试次数：' + detectionAttempts);
        
        chrome.tabs.query({ active: true, currentWindow: true }, function(tabs) {
          if (tabs.length === 0) {
            statusMessage.textContent = "无法获取当前页面";
            checkButton.disabled = false;
            checkButton.textContent = "手动检测";
            log('无法获取当前标签页');
            return;
          }
          
          // 向内容脚本发送手动检测消息
          chrome.tabs.sendMessage(tabs[0].id, { 
            action: 'checkForAnswer'
          }, function(response) {
            // 添加更多调试信息，检查response是否为undefined
            console.log('收到响应:', response);
            if (!response) {
              console.error('响应为空，可能内容脚本未加载或未正确响应');
            }
            
            log('手动检测响应', response);
            checkButton.disabled = false;
            checkButton.textContent = "手动检测";
            
            if (chrome.runtime.lastError) {
              log('检测出错', chrome.runtime.lastError);
              console.error('Chrome运行时错误:', chrome.runtime.lastError);
              
              // 当发生错误时，尝试重新注入内容脚本
              log('尝试重新注入内容脚本...');
              chrome.runtime.sendMessage({
                action: 'injectContentScript',
                tabId: tabs[0].id
              }, function(injectResponse) {
                log('注入脚本响应:', injectResponse);
                if (injectResponse && injectResponse.success) {
                  // 脚本重新注入成功，等待一会儿再次尝试检测
                  setTimeout(function() {
                    log('脚本已重新注入，重新尝试检测');
                    statusMessage.textContent = "正在重新检测...";
                    
                    // 重新发送检测请求
                    chrome.tabs.sendMessage(tabs[0].id, { 
                      action: 'checkForAnswer'
                    }, function(retryResponse) {
                      log('重试检测响应', retryResponse);
                      checkButton.disabled = false;
                      checkButton.textContent = "手动检测";
                      
                      if (retryResponse && retryResponse.hasAnswer) {
                        // 添加延迟以确保background.js有时间处理检测文本
                        setTimeout(function() {
                          // 获取最新状态，包括检测到的文本
                          chrome.runtime.sendMessage({
                            action: 'getStatus',
                            tabId: tabs[0].id
                          }, function(statusResponse) {
                            if (statusResponse && statusResponse.status === 'detected') {
                              updateStatus('detected');
                              statusMessage.textContent = "找到元宝回答！";
                              // 启用提交按钮
                              submitButton.disabled = false;
                            } else {
                              statusMessage.innerHTML = "检测成功但无法获取文本，请重试";
                            }
                          });
                        }, 500);
                      } else {
                        statusMessage.innerHTML = "在页面上未找到元宝回答，请确保元宝已生成回答<br><span style='color:#ff6b6b;font-size:11px;'>可能需要刷新页面后再试</span>";
                      }
                    });
                  }, 1000);
                } else {
                  // 显示更具体的错误信息，引导用户刷新页面
                  statusMessage.innerHTML = "无法加载内容脚本，请刷新页面后重试<br><span style='color:#ff6b6b;font-size:11px;'>或尝试更新扩展</span>";
                }
              });
            } else if (response && response.hasAnswer) {
              // 添加延迟以确保background.js有时间处理检测文本
              setTimeout(function() {
                // 获取最新状态，包括检测到的文本
                chrome.runtime.sendMessage({
                  action: 'getStatus',
                  tabId: tabs[0].id
                }, function(statusResponse) {
                  if (statusResponse && statusResponse.status === 'detected') {
                    updateStatus('detected');
                    statusMessage.textContent = "找到元宝回答！";
                    // 启用提交按钮
                    submitButton.disabled = false;
                    // 如果状态中存在detectedText，记录长度
                    if (statusResponse.detectedText) {
                      log('检测到文本长度:', statusResponse.detectedText.length);
                    }
                  } else {
                    statusMessage.innerHTML = "检测成功但无法获取文本，请重试";
                  }
                });
              }, 500);
            } else {
              statusMessage.innerHTML = "在页面上未找到元宝回答，请确保元宝已生成回答<br><span style='color:#ff6b6b;font-size:11px;'>可能需要刷新页面后再试</span>";
            }
          });
        });
      });
    }
    
    // 添加提交按钮点击处理
    submitButton.addEventListener('click', handleSubmit);
    
    // 添加复制按钮点击处理
    copyButton.addEventListener('click', function() {
      copyToClipboard(optimizedResult);
    });
    
    // 历史记录按钮点击事件
    historyBtn.addEventListener('click', function() {
      historyPanel.style.display = 'block';
      loadHistoryItems();
    });
    
    // 关闭历史记录面板
    closeHistoryBtn.addEventListener('click', function() {
      historyPanel.style.display = 'none';
    });
    
    // 从弹出窗口导出历史记录
    exportHistoryFromPopupBtn.addEventListener('click', function() {
      exportHistory();
    });
  }
  
  /**
   * 更新模型选择区域的可见性
   */
  function updateModelContainerVisibility() {
    if (selectedApiType === 'native') {
      openrouterModelContainer.style.display = 'none';
      nativeModelContainer.style.display = 'block';
    } else {
      openrouterModelContainer.style.display = 'block';
      nativeModelContainer.style.display = 'none';
    }
  }
  
  /**
   * 获取当前标签页的状态
   */
  function getCurrentState() {
    chrome.tabs.query({ active: true, currentWindow: true }, function(tabs) {
      if (tabs.length === 0) {
        log('无法获取当前标签页');
        return;
      }
      
      const currentTab = tabs[0];
      log('当前标签页', { id: currentTab.id, url: currentTab.url });
      
      // 尝试获取活动页面的状态
      chrome.runtime.sendMessage({ 
        action: 'getStatus',
        tabId: currentTab.id
      }, function(response) {
        log('获取状态响应', response);
        if (response && response.status) {
          updateStatus(response.status);
          
          // 如果已经有结果，显示结果
          if (response.result) {
            optimizedResult = response.result;
            resultElement.textContent = optimizedResult;
            resultContainer.style.display = 'block';
          }
        } else {
          updateStatus('waiting');
        }
      });
    });
  }
  
  /**
   * 更新UI状态
   * @param {string} status - 新状态
   */
  function updateStatus(status, message = '') {
    currentStatus = status;
    log('更新状态', status);
    
    // 更新状态样式类
    statusElement.className = `status ${status}`;
    
    // 根据状态更新UI文本和按钮状态
    switch(status) {
      case 'waiting':
        statusElement.textContent = "等待元宝回答...";
        statusMessage.textContent = "请在元宝网站上获取回答";
        submitButton.disabled = true;
        document.getElementById('apiStatusContainer').style.display = 'none';
        break;
        
      case 'detected':
        statusElement.textContent = "检测到元宝回答!";
        statusMessage.textContent = "已准备好优化回答";
        submitButton.disabled = false;
        document.getElementById('apiStatusContainer').style.display = 'none';
        break;
        
      case 'processing':
        statusElement.textContent = "正在优化...";
        statusMessage.textContent = "处理中...";
        submitButton.disabled = true;
        document.getElementById('apiStatusContainer').style.display = 'block';
        showApiStatus('loading', '正在调用API，请稍候...');
        break;
        
      case 'completed':
        statusElement.textContent = "优化完成!";
        statusMessage.textContent = "优化已完成";
        submitButton.disabled = false;
        resultContainer.style.display = "block";
        break;
        
      case 'error':
        statusElement.textContent = "处理出错，请重试";
        statusMessage.textContent = "发生错误";
        submitButton.disabled = false;
        break;
    }
  }
  
  // ===== 事件监听器 =====
  
  // 模式切换
  modeToggle.addEventListener('change', function() {
    extractionMode = this.checked ? 'full' : 'thinking';
    log('切换提取模式', extractionMode);
    
    // 保存设置
    chrome.storage.sync.set({ extractionMode: extractionMode });
    
    // 通知内容脚本
    chrome.tabs.query({ active: true, currentWindow: true }, function(tabs) {
      if (tabs.length === 0) return;
      
      chrome.tabs.sendMessage(tabs[0].id, { 
        action: 'setExtractionMode', 
        mode: extractionMode 
      });
    });
  });
  
  // 模型选择
  modelSelect.addEventListener('change', function() {
    selectedModel = this.value;
    log('切换模型', selectedModel);
    
    // 处理自定义模型选项
    const customModelContainer = document.getElementById('customModelContainer');
    const customModelId = document.getElementById('customModelId');
    
    if (selectedModel === 'custom') {
      // 显示自定义模型输入框
      customModelContainer.style.display = 'block';
      // 如果有之前保存的自定义模型ID，则加载
      chrome.storage.sync.get({customModelId: ''}, function(items) {
        customModelId.value = items.customModelId || '';
      });
    } else {
      // 隐藏自定义模型输入框
      customModelContainer.style.display = 'none';
    }
    
    // 保存设置
    chrome.storage.sync.set({ selectedModel: selectedModel });
  });
  
  // 自定义模型ID输入处理
  const customModelId = document.getElementById('customModelId');
  if (customModelId) {
    customModelId.addEventListener('change', function() {
      // 保存自定义模型ID
      chrome.storage.sync.set({ customModelId: this.value });
      log('保存自定义模型ID', this.value);
    });
  }
  
  /**
   * 显示API调用状态
   * @param {string} state - 状态 (waiting, loading, success, error)
   * @param {string} message - 状态消息
   * @param {object} details - 详细信息 (可选)
   */
  function showApiStatus(state, message, details = null) {
    const apiStatusContainer = document.getElementById('apiStatusContainer');
    const apiStatus = document.getElementById('apiStatus');
    const apiErrorDetails = document.getElementById('apiErrorDetails');
    const retryBtn = document.getElementById('retryBtn');
    const statusMessageElement = apiStatus.querySelector('.status-message');
    const loadingMessage = statusMessageElement.querySelector('.loading-message');
    
    // 显示状态容器
    if (state !== 'waiting' && state !== 'detected') {
      apiStatusContainer.style.display = 'block';
    }
    
    // 确保API状态元素可见
    apiStatus.style.display = 'flex';
    
    // 不使用showApiStatus设置自定义进度状态类，这会干扰真实进度显示
    // 只设置加载动画
    if (state === 'loading') {
      if (loadingMessage) {
        loadingMessage.textContent = message;
      }
      
      // 如果没有其他进度状态被设置，则启动加载动画
      if (!apiStatus.classList.contains('preparing') && 
          !apiStatus.classList.contains('sending') && 
          !apiStatus.classList.contains('processing') && 
          !apiStatus.classList.contains('receiving') && 
          !apiStatus.classList.contains('finalizing') && 
          !apiStatus.classList.contains('completed') && 
          !apiStatus.classList.contains('error')) {
            
        apiStatus.className = `api-status ${state}`;
        startLoadingAnimation();
      }
    } else if (state === 'success' || state === 'error') {
      stopLoadingAnimation();
      
      if (loadingMessage) {
        loadingMessage.textContent = message;
      }
      
      // 只有在没有其他进度状态被设置时才添加成功/错误状态类
      if (!apiStatus.classList.contains('completed') && !apiStatus.classList.contains('error')) {
        apiStatus.className = `api-status ${state}`;
      }
    }
    
    // 错误详情处理
    if (state === 'error' && details) {
      apiErrorDetails.style.display = 'block';
      
      let errorText = '';
      if (details.code) {
        errorText += `错误代码: ${details.code}\n`;
      }
      errorText += `错误信息: ${details.message || '未知错误'}\n`;
      
      if (details.details) {
        if (details.details.statusCode) {
          errorText += `状态码: ${details.details.statusCode}\n`;
        }
        
        if (details.code === 'API_ERROR' && details.details.responseBody) {
          const responseBody = details.details.responseBody;
          if (responseBody.error) {
            errorText += `API错误: ${responseBody.error.message || responseBody.error.type || JSON.stringify(responseBody.error)}\n`;
          }
        }
      }
      
      apiErrorDetails.textContent = errorText;
      retryBtn.style.display = 'block';
    } else {
      apiErrorDetails.style.display = 'none';
      retryBtn.style.display = 'none';
    }
    
    // 记录到调试日志
    log(`API状态: ${state}`, details);
  }
  
  /**
   * 处理提交按钮点击
   */
  function handleSubmit() {
    // 禁用提交按钮防止重复点击
    submitButton.disabled = true;
    
    // 显示处理状态
    statusMessage.textContent = '正在处理...';
    const apiStatusContainer = document.getElementById('apiStatusContainer');
    apiStatusContainer.style.display = 'block';
    showApiStatus('loading', '正在准备处理请求...');
    
    // 更新主状态显示
    statusElement.textContent = "正在优化...";
    
    // 延迟一小段时间以显示加载动画
    setTimeout(() => {
      chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
        if (tabs.length === 0) {
          showApiStatus('error', '无法获取当前标签页');
          submitButton.disabled = false;
          return;
        }
        
        const tabId = tabs[0].id;
        
        // 获取检测到的文本
        chrome.runtime.sendMessage({action: 'getStatus', tabId: tabId}, function(response) {
          if (chrome.runtime.lastError || !response) {
            showApiStatus('error', '无法获取当前状态');
            submitButton.disabled = false;
            return;
          }
          
          log('获取到状态响应', response);
          
          // 验证答案文本存在
          if (!response.detectedText) {
            // 尝试重新检测一次
            log('未找到检测文本，尝试重新检测');
            tryDetectAnswer(tabId, function(success) {
              if (success) {
                log('重新检测成功，继续提交');
                // 重新检测成功，继续提交
                handleSubmit();
              } else {
                showApiStatus('error', '没有检测到元宝回答，请先使用手动检测按钮');
                submitButton.disabled = false;
              }
            });
            return;
          }
          
          // 准备提交参数
          let model = '';
          let modelDisplayName = '';
          if (selectedApiType === 'native') {
            model = selectedNativeModel; // 使用原生模型ID
            // 获取显示名称
            const nativeSelectElem = document.getElementById('nativeModelSelect');
            if (nativeSelectElem) {
              modelDisplayName = nativeSelectElem.options[nativeSelectElem.selectedIndex].text;
            }
          } else {
            // 使用OpenRouter模型ID
            model = selectedModel === 'custom' 
              ? document.getElementById('customModelId').value
              : selectedModel;
            
            // 获取显示名称
            if (selectedModel === 'custom') {
              modelDisplayName = "自定义模型";
            } else {
              const selectElem = document.getElementById('modelSelect');
              if (selectElem) {
                modelDisplayName = selectElem.options[selectElem.selectedIndex].text;
              }
            }
          }
          
          // 获取用户提示词
          const prompt = promptElement.value;
          
          // 如果提示词未设置，提示用户
          if (!prompt) {
            showApiStatus('error', '请输入提示词');
            submitButton.disabled = false;
            return;
          }
          
          // 更新状态显示模型名称
          statusElement.textContent = `正在通过${modelDisplayName || 'AI'}优化...`;
          statusMessage.textContent = `正在通过${modelDisplayName || 'AI'}优化..`;
          
          // 提交优化请求
          log('提交优化请求', {
            tabId: tabId,
            textLength: response.detectedText.length,
            model: model,
            apiType: selectedApiType,
            modelDisplayName: modelDisplayName
          });
          
          chrome.runtime.sendMessage({
            action: 'optimize',
            tabId: tabId,
            answer: response.detectedText,
            prompt: prompt,
            model: model,
            apiType: selectedApiType
          }, function(response) {
            if (chrome.runtime.lastError) {
              showApiStatus('error', '请求处理失败: ' + chrome.runtime.lastError.message);
              submitButton.disabled = false;
              return;
            }
            
            // 处理响应
            if (response.success) {
              // 显示优化结果
              optimizedResult = response.result;
              resultElement.textContent = optimizedResult;
              resultContainer.style.display = 'block';
              
              // 更新状态
              statusMessage.textContent = '优化完成';
              showApiStatus('success', '处理成功');
              
              // 在新内容添加后，确保滚动到新的部分
              resultElement.scrollIntoView({behavior: 'smooth'});
            } else {
              // 显示错误
              showApiStatus('error', '处理失败: ' + (response.error || '未知错误'));
            }
            
            // 重新启用提交按钮
            submitButton.disabled = false;
          });
        });
      });
    }, 500);
  }
  
  /**
   * 尝试检测元宝回答
   * @param {number} tabId - 标签页ID
   * @param {function} callback - 回调函数，参数为是否检测成功
   */
  function tryDetectAnswer(tabId, callback) {
    log('正在尝试检测元宝回答');
    showApiStatus('loading', '正在检测页面...');
    
    // 向内容脚本发送检测请求
    chrome.tabs.sendMessage(tabId, { 
      action: 'checkForAnswer'
    }, function(response) {
      if (chrome.runtime.lastError || !response) {
        log('检测请求失败', chrome.runtime.lastError);
        
        // 尝试重新注入内容脚本
        chrome.runtime.sendMessage({
          action: 'injectContentScript',
          tabId: tabId
        }, function(injectResponse) {
          if (injectResponse && injectResponse.success) {
            log('重新注入脚本成功，重新尝试检测');
            
            // 延迟一下再尝试
            setTimeout(() => {
              chrome.tabs.sendMessage(tabId, { 
                action: 'checkForAnswer'
              }, function(retryResponse) {
                const success = retryResponse && retryResponse.hasAnswer;
                log('重新检测结果', success);
                
                // 再次获取状态，确认文本已存储
                if (success) {
                  setTimeout(() => {
                    chrome.runtime.sendMessage({action: 'getStatus', tabId: tabId}, function(statusResponse) {
                      callback(statusResponse && statusResponse.detectedText);
                    });
                  }, 500);
                } else {
                  callback(false);
                }
              });
            }, 1000);
          } else {
            log('重新注入脚本失败');
            callback(false);
          }
        });
        return;
      }
      
      const success = response && response.hasAnswer;
      log('检测结果', success);
      
      // 如果检测成功，我们给后台一点时间处理文本
      if (success) {
        setTimeout(() => {
          chrome.runtime.sendMessage({action: 'getStatus', tabId: tabId}, function(statusResponse) {
            callback(statusResponse && statusResponse.detectedText);
          });
        }, 500);
      } else {
        callback(false);
      }
    });
  }
  
  // 复制按钮点击事件
  copyButton.addEventListener('click', function() {
    if (!optimizedResult) return;
    
    navigator.clipboard.writeText(optimizedResult)
      .then(() => {
        log('已复制优化结果到剪贴板');
        
        // 添加复制成功视觉效果
        copyButton.classList.add('copy-success');
        
        // 显示成功提示
        statusMessage.textContent = "已复制到剪贴板!";
        
        // 1.5秒后移除成功样式
        setTimeout(() => {
          copyButton.classList.remove('copy-success');
          statusMessage.textContent = "优化已完成";
        }, 1500);
      })
      .catch(err => {
        log('复制失败', err);
        statusMessage.textContent = "复制失败: " + err.message;
      });
  });
  
  // 添加重试按钮事件监听
  document.getElementById('retryBtn').addEventListener('click', function() {
    // 重新触发提交操作
    submitButton.click();
  });
  
  // 处理真实进度状态更新
  function updateProgressStatus(status, message, modelName) {
    log('更新进度状态', {status, message, modelName});
    
    // 停止现有动画
    stopLoadingAnimation();
    
    const loadingMessage = document.querySelector('.loading-message');
    const loadingSubstatus = document.querySelector('.loading-substatus');
    const statusMessage = document.getElementById('statusMessage'); // 获取底部状态消息元素
    const apiStatusContainer = document.getElementById('apiStatusContainer');
    
    // 确保API状态容器显示
    if (status !== 'waiting' && status !== 'detected') {
      apiStatusContainer.style.display = 'block';
    }
    
    // 更新显示的消息，添加模型名称
    if (loadingMessage) {
      // 如果有模型名称，显示在状态信息中
      if (modelName && status === 'processing') {
        loadingMessage.textContent = `${modelName} 正在处理内容...`;
      } else if (modelName && status === 'receiving') {
        loadingMessage.textContent = `正在接收 ${modelName} 的响应...`;
      } else {
        loadingMessage.textContent = message;
      }
    }
    
    // 对于不同的状态显示不同的样式
    const apiStatus = document.getElementById('apiStatus');
    if (apiStatus) {
      // 移除现有状态类
      apiStatus.className = 'api-status'; // 首先清除所有类
      // 添加新状态类
      apiStatus.classList.add(status);
    }
    
    // 如果处理完成，停止加载动画并更新底部状态信息
    if (status === 'completed' || status === 'error') {
      if (loadingSubstatus) {
        if (status === 'completed') {
          loadingSubstatus.textContent = '✓ 成功';
          loadingSubstatus.style.color = '#4CAF50';
          // 确保底部状态消息也更新
          if (statusMessage) {
            statusMessage.textContent = "优化已完成";
          }
        } else {
          loadingSubstatus.textContent = '✗ 失败';
          loadingSubstatus.style.color = '#F44336';
          // 错误状态也更新底部消息
          if (statusMessage) {
            statusMessage.textContent = "优化失败";
          }
        }
      }
    } else {
      if (loadingSubstatus) {
        // 在子状态中显示更多信息
        if (modelName && (status === 'processing' || status === 'receiving')) {
          loadingSubstatus.textContent = '请耐心等待，AI生成需要一些时间...';
        } else {
          loadingSubstatus.textContent = '处理中...';
        }
        loadingSubstatus.style.color = '';
      }
      
      // 更新底部状态消息以反映当前进度
      if (statusMessage) {
        if (status === 'processing' && modelName) {
          statusMessage.textContent = "正在通过" + (modelName || "AI") + "优化..";
          // 同时更新主状态
          const statusElement = document.getElementById('status');
          if (statusElement) {
            statusElement.textContent = "正在通过" + (modelName || "AI") + "优化...";
          }
        } else if (status === 'receiving') {
          statusMessage.textContent = "正在接收优化结果..";
        } else {
          statusMessage.textContent = message || "处理中..";
        }
      }
    }
  }
  
  // 添加调试日志控制
  const debugSection = document.createElement('div');
  debugSection.className = 'debug-section';
  debugSection.style.cssText = 'margin-top: 16px; border-top: 1px solid #eee; padding-top: 10px; font-size: 12px;';
  
  const debugToggle = document.createElement('div');
  debugToggle.style.cssText = 'display: flex; align-items: center; margin-bottom: 8px; cursor: pointer;';
  debugToggle.innerHTML = '<span style="margin-right: 5px;">👨‍💻</span> <span>显示调试信息</span>';
  
  const debugOutput = document.createElement('div');
  debugOutput.id = 'debugOutput';
  debugOutput.style.cssText = 'display: none; max-height: 100px; overflow-y: auto; background: #f5f5f5; padding: 8px; border-radius: 4px; font-family: monospace; white-space: pre-wrap;';
  
  debugSection.appendChild(debugToggle);
  debugSection.appendChild(debugOutput);
  document.querySelector('.container').appendChild(debugSection);
  
  // 调试日志显示切换
  debugToggle.addEventListener('click', function() {
    if (debugOutput.style.display === 'none') {
      debugOutput.style.display = 'block';
      loadDebugLogs();
    } else {
      debugOutput.style.display = 'none';
    }
  });
  
  // 历史记录按钮事件监听
  historyBtn.addEventListener('click', function() {
    historyPanel.style.display = 'flex';
    loadHistoryRecords();
  });
  
  // 关闭历史记录面板
  closeHistoryBtn.addEventListener('click', function() {
    historyPanel.style.display = 'none';
  });
  
  // 从弹出窗口导出历史记录
  exportHistoryFromPopupBtn.addEventListener('click', function() {
    showExportDialog();
  });
  
  /**
   * 显示导出选项对话框
   */
  function showExportDialog() {
    // 创建导出选项对话框
    const exportDialog = document.createElement('div');
    exportDialog.className = 'export-dialog';
    exportDialog.innerHTML = `
      <div class="export-dialog-content">
        <h3>导出历史记录</h3>
        <p>请选择导出格式:</p>
        <div class="export-options">
          <button id="exportJsonBtn" class="secondary-btn">JSON格式</button>
          <button id="exportTxtBtn" class="secondary-btn">TXT格式</button>
        </div>
        <button id="cancelExportBtn" class="text-btn">取消</button>
      </div>
    `;
    
    // 添加样式
    const dialogStyle = document.createElement('style');
    dialogStyle.textContent = `
      .export-dialog {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 2000;
      }
      .export-dialog-content {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        width: 300px;
        text-align: center;
      }
      .export-options {
        display: flex;
        gap: 10px;
        margin: 20px 0;
        justify-content: center;
      }
      .text-btn {
        background: none;
        border: none;
        color: #666;
        cursor: pointer;
        padding: 8px;
      }
      .text-btn:hover {
        text-decoration: underline;
      }
    `;
    
    document.body.appendChild(dialogStyle);
    document.body.appendChild(exportDialog);
    
    // 添加事件监听器
    document.getElementById('exportJsonBtn').addEventListener('click', function() {
      exportHistoryAsJson();
      document.body.removeChild(exportDialog);
      document.body.removeChild(dialogStyle);
    });
    
    document.getElementById('exportTxtBtn').addEventListener('click', function() {
      exportHistoryAsTxt();
      document.body.removeChild(exportDialog);
      document.body.removeChild(dialogStyle);
    });
    
    document.getElementById('cancelExportBtn').addEventListener('click', function() {
      document.body.removeChild(exportDialog);
      document.body.removeChild(dialogStyle);
    });
  }
  
  /**
   * 导出历史记录为JSON格式
   */
  function exportHistoryAsJson() {
    // 同时从sync和local存储获取历史记录
    Promise.all([
      chrome.storage.sync.get({savedResults: []}),
      chrome.storage.local.get({savedResults: []})
    ]).then(([syncData, localData]) => {
      const syncResults = syncData.savedResults || [];
      const localResults = localData.savedResults || [];
      
      // 合并记录（如果有相同时间戳的记录，以sync为准）
      let allResults = [...syncResults];
      
      // 添加不在sync中的local记录
      localResults.forEach(localItem => {
        // 检查是否已存在相同时间戳的记录
        const exists = allResults.some(item => item.timestamp === localItem.timestamp);
        if (!exists) {
          allResults.push(localItem);
        }
      });
      
      // 按时间戳排序（最新的在前）
      allResults.sort((a, b) => b.timestamp - a.timestamp);
      
      const savedResults = allResults;
      
      if (savedResults.length === 0) {
        showToast('没有历史记录可供导出');
        return;
      }
      
      // 准备导出数据
      const exportData = {
        timestamp: new Date().toISOString(),
        version: '1.0',
        source: '元宝助手历史记录',
        results: savedResults
      };
      
      // 转换为JSON字符串，并美化格式
      const jsonString = JSON.stringify(exportData, null, 2);
      
      // 创建Blob对象
      const blob = new Blob([jsonString], {type: 'application/json'});
      
      // 创建下载链接
      const url = URL.createObjectURL(blob);
      
      // 创建临时下载链接并点击
      const downloadLink = document.createElement('a');
      downloadLink.href = url;
      downloadLink.download = `元宝助手历史记录_${formatDate(new Date())}.json`;
      document.body.appendChild(downloadLink);
      downloadLink.click();
      
      // 清理
      setTimeout(() => {
        document.body.removeChild(downloadLink);
        URL.revokeObjectURL(url);
      }, 100);
      
      showToast(`成功导出 ${savedResults.length} 条历史记录为JSON格式`);
    }).catch(error => {
      console.error('导出历史记录失败', error);
      showToast('导出历史记录失败: ' + error.message);
    });
  }
  
  /**
   * 导出历史记录为TXT格式
   */
  function exportHistoryAsTxt() {
    // 同时从sync和local存储获取历史记录
    Promise.all([
      chrome.storage.sync.get({savedResults: []}),
      chrome.storage.local.get({savedResults: []})
    ]).then(([syncData, localData]) => {
      const syncResults = syncData.savedResults || [];
      const localResults = localData.savedResults || [];
      
      // 合并记录（如果有相同时间戳的记录，以sync为准）
      let allResults = [...syncResults];
      
      // 添加不在sync中的local记录
      localResults.forEach(localItem => {
        // 检查是否已存在相同时间戳的记录
        const exists = allResults.some(item => item.timestamp === localItem.timestamp);
        if (!exists) {
          allResults.push(localItem);
        }
      });
      
      // 按时间戳排序（最新的在前）
      allResults.sort((a, b) => b.timestamp - a.timestamp);
      
      const savedResults = allResults;
      
      if (savedResults.length === 0) {
        showToast('没有历史记录可供导出');
        return;
      }
      
      // 创建TXT内容
      let txtContent = '==== 元宝助手历史记录 ====\n\n';
      txtContent += `导出时间：${new Date().toLocaleString()}\n\n`;
      
      // 添加每条历史记录
      savedResults.forEach((item, index) => {
        const date = new Date(item.timestamp).toLocaleString();
        const source = syncResults.some(syncItem => syncItem.timestamp === item.timestamp) 
          ? '同步存储' 
          : '本地存储';
        
        txtContent += `===== 记录 ${index + 1} (${source}) =====\n`;
        txtContent += `时间：${date}\n`;
        txtContent += `模型：${item.model || '未知模型'}\n`;
        txtContent += `提示词：${item.prompt}\n\n`;
        txtContent += `原始内容：${item.original}\n\n`;
        txtContent += `优化结果：\n${item.optimized}\n\n`;
        txtContent += `${'='.repeat(30)}\n\n`;
      });
      
      // 将内容转换为Blob对象
      const blob = new Blob([txtContent], {type: 'text/plain;charset=utf-8'});
      
      // 创建下载链接
      const url = URL.createObjectURL(blob);
      
      // 创建临时下载链接并点击
      const downloadLink = document.createElement('a');
      downloadLink.href = url;
      downloadLink.download = `元宝助手历史记录_${formatDate(new Date())}.txt`;
      document.body.appendChild(downloadLink);
      downloadLink.click();
      
      // 清理
      setTimeout(() => {
        document.body.removeChild(downloadLink);
        URL.revokeObjectURL(url);
      }, 100);
      
      showToast(`成功导出 ${savedResults.length} 条历史记录为TXT格式`);
    }).catch(error => {
      console.error('导出历史记录失败', error);
      showToast('导出历史记录失败: ' + error.message);
    });
  }
  
  /**
   * 格式化日期为文件名友好格式
   * @param {Date} date - 日期对象
   * @returns {string} 格式化的日期字符串
   */
  function formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    return `${year}${month}${day}_${hours}${minutes}`;
  }
  
  /**
   * 显示临时提示消息
   * @param {string} message - 要显示的消息
   */
  function showToast(message) {
    const toast = document.createElement('div');
    toast.className = 'toast';
    toast.textContent = message;
    
    // 添加样式
    const style = document.createElement('style');
    style.textContent = `
      .toast {
        position: fixed;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 10px 20px;
        border-radius: 4px;
        font-size: 14px;
        z-index: 2500;
        animation: fadeIn 0.3s, fadeOut 0.3s 2.7s;
      }
      @keyframes fadeIn {
        from { opacity: 0; bottom: 0; }
        to { opacity: 1; bottom: 20px; }
      }
      @keyframes fadeOut {
        from { opacity: 1; }
        to { opacity: 0; }
      }
    `;
    
    document.body.appendChild(style);
    document.body.appendChild(toast);
    
    // 3秒后移除
    setTimeout(() => {
      document.body.removeChild(toast);
      document.body.removeChild(style);
    }, 3000);
  }
  
  /**
   * 加载历史记录数据
   */
  function loadHistoryRecords() {
    historyList.innerHTML = '<div class="loading-history">加载中...</div>';
    
    // 先尝试从sync存储获取
    chrome.storage.sync.get({savedResults: []}, function(syncItems) {
      // 再从local存储获取
      chrome.storage.local.get({savedResults: []}, function(localItems) {
        // 合并两个来源的结果，并按时间戳排序
        const syncResults = syncItems.savedResults || [];
        const localResults = localItems.savedResults || [];
        
        log('从sync存储加载了 ' + syncResults.length + ' 条历史记录');
        log('从local存储加载了 ' + localResults.length + ' 条历史记录');
        
        // 合并记录（如果有相同时间戳的记录，以sync为准）
        let allResults = [...syncResults];
        
        // 添加不在sync中的local记录
        localResults.forEach(localItem => {
          // 检查是否已存在相同时间戳的记录
          const exists = allResults.some(item => item.timestamp === localItem.timestamp);
          if (!exists) {
            allResults.push(localItem);
          }
        });
        
        // 按时间戳排序（最新的在前）
        allResults.sort((a, b) => b.timestamp - a.timestamp);
        
        const savedResults = allResults;
        
        if (savedResults.length === 0) {
          historyList.innerHTML = '<div class="no-history">暂无历史记录</div>';
          return;
        }
        
        // 清空列表并添加历史记录项
        historyList.innerHTML = '';
        
        savedResults.forEach((item, index) => {
          const historyItem = document.createElement('div');
          historyItem.className = 'history-item';
          historyItem.dataset.index = index;
          
          // 格式化日期
          const date = new Date(item.timestamp);
          const formattedDate = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
          
          // 添加存储来源标记
          const sourceTag = syncResults.some(syncItem => syncItem.timestamp === item.timestamp) 
            ? '<span class="history-source sync">同步</span>' 
            : '<span class="history-source local">本地</span>';
          
          historyItem.innerHTML = `
            <div class="history-item-header">
              <span class="history-item-model">${item.model}</span>
              <span class="history-item-date">${formattedDate}</span>
              ${sourceTag}
            </div>
            <div class="history-item-content">${item.optimized.substring(0, 150)}${item.optimized.length > 150 ? '...' : ''}</div>
            <div class="history-item-prompt">${item.prompt.substring(0, 100)}${item.prompt.length > 100 ? '...' : ''}</div>
          `;
          
          // 点击历史记录项查看详情
          historyItem.addEventListener('click', function() {
            loadHistoryDetail(index, savedResults);
          });
          
          historyList.appendChild(historyItem);
        });
      });
    });
  }
  
  /**
   * 加载历史记录详情
   * @param {number} index - 历史记录索引
   * @param {Array} [preloadedRecords] - 预加载的历史记录数组
   */
  function loadHistoryDetail(index, preloadedRecords) {
    if (preloadedRecords && index >= 0 && index < preloadedRecords.length) {
      // 如果提供了预加载的记录，直接使用
      displayHistoryItem(preloadedRecords[index]);
      return;
    }
    
    // 否则从存储中获取记录
    // 先尝试从sync存储获取
    chrome.storage.sync.get({savedResults: []}, function(syncItems) {
      // 再从local存储获取
      chrome.storage.local.get({savedResults: []}, function(localItems) {
        // 合并两个来源的结果，并按时间戳排序
        const syncResults = syncItems.savedResults || [];
        const localResults = localItems.savedResults || [];
        
        // 合并记录（如果有相同时间戳的记录，以sync为准）
        let allResults = [...syncResults];
        
        // 添加不在sync中的local记录
        localResults.forEach(localItem => {
          // 检查是否已存在相同时间戳的记录
          const exists = allResults.some(item => item.timestamp === localItem.timestamp);
          if (!exists) {
            allResults.push(localItem);
          }
        });
        
        // 按时间戳排序（最新的在前）
        allResults.sort((a, b) => b.timestamp - a.timestamp);
        
        const savedResults = allResults;
        
        if (index >= 0 && index < savedResults.length) {
          displayHistoryItem(savedResults[index]);
        } else {
          log('无法加载历史记录详情，索引超出范围', {index, total: savedResults.length});
        }
      });
    });
  }
  
  /**
   * 显示历史记录详情到界面上
   * @param {Object} item - 历史记录项
   */
  function displayHistoryItem(item) {
    // 更新UI
    resultElement.innerHTML = item.optimized;
    resultContainer.style.display = 'block';
    
    // 关闭历史记录面板
    historyPanel.style.display = 'none';
    
    // 显示当前查看的是历史记录
    statusMessage.textContent = `查看历史记录 (${new Date(item.timestamp).toLocaleString()})`;
    
    // 可选: 同步提示词到输入框
    promptElement.value = item.prompt;
    
    // 可选: 如果模型在下拉列表中，选择它
    const modelOptions = Array.from(modelSelect.options);
    const modelOption = modelOptions.find(option => 
      option.textContent.includes(item.model) || 
      (option.value === 'custom' && item.model !== 'Gemini 2.5 Pro' && 
       item.model !== 'Gemini 2.0 Flash Thinking' && 
       item.model !== 'Gemini 2.0 Flash' &&
       item.model !== 'Claude 3.7 Sonnet' &&
       item.model !== 'Claude 3.5 Sonnet')
    );
    
    if (modelOption) {
      modelSelect.value = modelOption.value;
      // 如果是自定义模型
      if (modelOption.value === 'custom') {
        const customModelContainer = document.getElementById('customModelContainer');
        const customModelIdInput = document.getElementById('customModelId');
        if (customModelContainer && customModelIdInput) {
          customModelContainer.style.display = 'block';
          customModelIdInput.value = item.model;
        }
      } else {
        const customModelContainer = document.getElementById('customModelContainer');
        if (customModelContainer) {
          customModelContainer.style.display = 'none';
        }
      }
    }
  }
  
  // 添加一个函数来检查优化结果的显示
  function ensureResultDisplay() {
    // 检查当前状态和结果容器
    if (currentStatus === 'completed' && optimizedResult) {
      // 确保结果容器显示
      resultContainer.style.display = "block";
      // 确保状态信息正确
      statusMessage.textContent = "优化已完成";
      // 确保API状态显示成功
      const apiStatus = document.getElementById('apiStatus');
      if (apiStatus && !apiStatus.classList.contains('completed')) {
        apiStatus.className = 'api-status completed';
      }
    }
  }
  
  // 在DOMContentLoaded事件的末尾添加检查
  setTimeout(ensureResultDisplay, 500);

  /**
   * 添加详细调试日志
   * @param {string} type - 日志类型
   * @param {string} message - 日志消息
   * @param {object} data - 相关数据
   */
  function debugLog(type, message, data = null) {
    const now = new Date();
    const timestamp = now.toLocaleTimeString() + '.' + now.getMilliseconds().toString().padStart(3, '0');
    const fullMessage = `[${timestamp}] [${type}] ${message}`;
    
    if (data) {
      console.log(fullMessage, data);
      log(message, data);
    } else {
      console.log(fullMessage);
      log(message);
    }
    
    // 将日志添加到调试输出区域
    const debugOutput = document.getElementById('debugOutput');
    if (debugOutput) {
      const logEntry = document.createElement('div');
      logEntry.className = `debug-log-entry debug-${type.toLowerCase()}`;
      logEntry.textContent = fullMessage;
      debugOutput.appendChild(logEntry);
      
      // 保持滚动到最新日志
      debugOutput.scrollTop = debugOutput.scrollHeight;
      
      // 限制日志条目数量
      while (debugOutput.children.length > 50) {
        debugOutput.removeChild(debugOutput.firstChild);
      }
    }
  }
}); 