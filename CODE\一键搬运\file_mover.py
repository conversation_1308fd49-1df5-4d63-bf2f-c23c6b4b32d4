#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件快速搬运工具
功能：智能扫描文件夹，批量选择文件，一键搬运
特性：
- 智能新文件检测
- 复选框批量选择
- 一键移动/复制
- 便携式设计
"""

import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import os
import json
import shutil
from datetime import datetime
from pathlib import Path

class FileMoverGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("文件快速搬运工具 v1.0")
        self.root.geometry("900x700")
        
        # 路径变量
        self.source_folder = tk.StringVar()
        self.target_folder = tk.StringVar()
        
        # 便携式设计
        self.script_dir = os.path.dirname(os.path.abspath(__file__))
        self.cache_file = os.path.join(self.script_dir, "file_cache.json")
        self.settings_file = os.path.join(self.script_dir, "mover_settings.json")
        self.file_cache = {}  # 用于检测新文件

        # 文件列表和选择状态
        self.file_list = []
        self.selection_vars = {}

        # 操作模式
        self.operation_mode = tk.StringVar(value="copy")  # copy 或 move

        self.load_file_cache()
        self.load_settings()
        self.setup_ui()
    
    def load_file_cache(self):
        """加载文件缓存"""
        try:
            if os.path.exists(self.cache_file):
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    self.file_cache = json.load(f)
        except Exception as e:
            print(f"加载文件缓存失败: {e}")
            self.file_cache = {}

    def save_file_cache(self):
        """保存文件缓存"""
        try:
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(self.file_cache, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存文件缓存失败: {e}")

    def load_settings(self):
        """加载设置"""
        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    self.source_folder.set(settings.get('source_folder', ''))
                    self.target_folder.set(settings.get('target_folder', ''))
                    self.operation_mode.set(settings.get('operation_mode', 'copy'))
        except Exception as e:
            print(f"加载设置失败: {e}")

    def save_settings(self):
        """保存设置"""
        try:
            settings = {
                'source_folder': self.source_folder.get(),
                'target_folder': self.target_folder.get(),
                'operation_mode': self.operation_mode.get()
            }
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存设置失败: {e}")
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 操作模式选择
        mode_frame = ttk.LabelFrame(main_frame, text="操作模式", padding="5")
        mode_frame.grid(row=0, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)

        ttk.Radiobutton(mode_frame, text="📋 复制模式 (保留原文件)", variable=self.operation_mode, value="copy").pack(side=tk.LEFT, padx=10)
        ttk.Radiobutton(mode_frame, text="✂️ 搬运模式 (移动文件)", variable=self.operation_mode, value="move").pack(side=tk.LEFT, padx=10)

        # 源文件夹选择
        ttk.Label(main_frame, text="源文件夹:").grid(row=1, column=0, sticky=tk.W, pady=5)
        source_frame = ttk.Frame(main_frame)
        source_frame.grid(row=1, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        source_frame.columnconfigure(0, weight=1)

        ttk.Entry(source_frame, textvariable=self.source_folder, width=60).grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))
        ttk.Button(source_frame, text="选择文件夹", command=self.select_source_folder).grid(row=0, column=1)
        ttk.Button(source_frame, text="刷新扫描", command=self.scan_files).grid(row=0, column=2, padx=(5, 0))

        # 目标文件夹选择
        ttk.Label(main_frame, text="目标文件夹:").grid(row=2, column=0, sticky=tk.W, pady=5)
        target_frame = ttk.Frame(main_frame)
        target_frame.grid(row=2, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        target_frame.columnconfigure(0, weight=1)

        ttk.Entry(target_frame, textvariable=self.target_folder, width=60).grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))
        ttk.Button(target_frame, text="选择文件夹", command=self.select_target_folder).grid(row=0, column=1)
        
        # 文件列表框架
        list_frame = ttk.LabelFrame(main_frame, text="文件列表", padding="5")
        list_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=10)
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)
        
        # 创建Treeview
        columns = ('选择', '文件名', '类型', '大小', '修改时间', '状态')
        self.tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)
        
        # 设置列标题和宽度
        self.tree.heading('选择', text='选择')
        self.tree.heading('文件名', text='文件名')
        self.tree.heading('类型', text='类型')
        self.tree.heading('大小', text='大小')
        self.tree.heading('修改时间', text='修改时间')
        self.tree.heading('状态', text='状态')
        
        self.tree.column('选择', width=50, anchor='center')
        self.tree.column('文件名', width=300)
        self.tree.column('类型', width=80, anchor='center')
        self.tree.column('大小', width=100, anchor='center')
        self.tree.column('修改时间', width=150, anchor='center')
        self.tree.column('状态', width=80, anchor='center')
        
        # 滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 绑定事件
        self.tree.bind('<Double-1>', self.toggle_selection)  # 双击切换选择
        self.tree.bind('<Button-1>', self.on_click)  # 单击选择行
        
        # 操作按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=3, pady=10)

        ttk.Button(button_frame, text="全选", command=self.select_all).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消全选", command=self.deselect_all).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="🚀 执行操作", command=self.execute_operation, style="Accent.TButton").pack(side=tk.LEFT, padx=20)
        ttk.Button(button_frame, text="💾 保存设置", command=self.save_settings).pack(side=tk.LEFT, padx=5)

        # 状态栏
        self.status_var = tk.StringVar(value="就绪 - 请选择操作模式和文件夹")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
    
    def select_source_folder(self):
        """选择源文件夹"""
        folder = filedialog.askdirectory(title="选择源文件夹", initialdir=self.source_folder.get())
        if folder:
            self.source_folder.set(folder)
            self.save_settings()  # 自动保存设置
            self.scan_files()

    def select_target_folder(self):
        """选择目标文件夹"""
        folder = filedialog.askdirectory(title="选择目标文件夹", initialdir=self.target_folder.get())
        if folder:
            self.target_folder.set(folder)
            self.save_settings()  # 自动保存设置
    
    def scan_files(self):
        """扫描文件"""
        source = self.source_folder.get()
        if not source or not os.path.exists(source):
            messagebox.showwarning("警告", "请先选择有效的源文件夹")
            return
        
        self.status_var.set("正在扫描文件...")
        self.root.update()
        
        # 清空现有列表
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        self.file_list = []
        self.selection_vars = {}
        
        try:
            # 扫描文件和文件夹
            for item in os.listdir(source):
                item_path = os.path.join(source, item)
                
                if os.path.isfile(item_path):
                    file_info = self.get_file_info(item_path)
                    self.file_list.append(file_info)
                elif os.path.isdir(item_path):
                    folder_info = self.get_folder_info(item_path)
                    self.file_list.append(folder_info)
            
            # 更新界面
            self.update_file_list()
            self.update_file_cache()
            
            self.status_var.set(f"扫描完成，找到 {len(self.file_list)} 个项目")
            
        except Exception as e:
            messagebox.showerror("错误", f"扫描文件时出错: {e}")
            self.status_var.set("扫描失败")
    
    def get_file_info(self, file_path):
        """获取文件信息"""
        stat = os.stat(file_path)
        size = stat.st_size
        mtime = datetime.fromtimestamp(stat.st_mtime)
        
        # 检查是否为新文件
        is_new = file_path not in self.file_cache
        
        return {
            'path': file_path,
            'name': os.path.basename(file_path),
            'type': '文件',
            'size': self.format_size(size),
            'mtime': mtime.strftime('%Y-%m-%d %H:%M'),
            'is_new': is_new,
            'selected': False
        }
    
    def get_folder_info(self, folder_path):
        """获取文件夹信息"""
        stat = os.stat(folder_path)
        mtime = datetime.fromtimestamp(stat.st_mtime)
        
        # 计算文件夹大小（简化版，只计算直接子文件）
        total_size = 0
        try:
            for item in os.listdir(folder_path):
                item_path = os.path.join(folder_path, item)
                if os.path.isfile(item_path):
                    total_size += os.path.getsize(item_path)
        except:
            pass
        
        # 检查是否为新文件夹
        is_new = folder_path not in self.file_cache
        
        return {
            'path': folder_path,
            'name': os.path.basename(folder_path),
            'type': '文件夹',
            'size': self.format_size(total_size),
            'mtime': mtime.strftime('%Y-%m-%d %H:%M'),
            'is_new': is_new,
            'selected': False
        }
    
    def format_size(self, size):
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024:
                return f"{size:.1f}{unit}"
            size /= 1024
        return f"{size:.1f}TB"
    
    def update_file_list(self):
        """更新文件列表显示"""
        for file_info in self.file_list:
            status = "新" if file_info['is_new'] else ""
            checkbox = "☑" if file_info['selected'] else "☐"
            
            item_id = self.tree.insert('', 'end', values=(
                checkbox,
                file_info['name'],
                file_info['type'],
                file_info['size'],
                file_info['mtime'],
                status
            ))
            
            # 新文件用不同颜色标记
            if file_info['is_new']:
                self.tree.set(item_id, '状态', '新')
                # 可以添加标签来改变颜色
    
    def toggle_selection(self, event):
        """切换选择状态"""
        # 获取点击的项
        item = self.tree.identify_row(event.y)
        if not item:
            return

        # 选中该项
        self.tree.selection_set(item)

        # 获取行索引
        row_index = self.tree.index(item)
        if 0 <= row_index < len(self.file_list):
            # 切换选择状态
            self.file_list[row_index]['selected'] = not self.file_list[row_index]['selected']

            # 更新显示
            checkbox = "☑" if self.file_list[row_index]['selected'] else "☐"
            self.tree.set(item, '选择', checkbox)

    def on_click(self, event):
        """单击事件处理"""
        # 获取点击的项
        item = self.tree.identify_row(event.y)
        if item:
            # 选中该项
            self.tree.selection_set(item)

            # 检查是否点击在选择列
            column = self.tree.identify_column(event.x)
            if column == '#1':  # 选择列
                self.toggle_selection(event)

    def select_all(self):
        """全选"""
        for i, file_info in enumerate(self.file_list):
            file_info['selected'] = True
            item = self.tree.get_children()[i]
            self.tree.set(item, '选择', "☑")
    
    def deselect_all(self):
        """取消全选"""
        for i, file_info in enumerate(self.file_list):
            file_info['selected'] = False
            item = self.tree.get_children()[i]
            self.tree.set(item, '选择', "☐")
    
    def execute_operation(self):
        """执行操作（根据选择的模式）"""
        source_folder = self.source_folder.get()
        target_folder = self.target_folder.get()

        if not source_folder or not target_folder:
            messagebox.showwarning("警告", "请先选择源文件夹和目标文件夹")
            return

        if not os.path.exists(target_folder):
            messagebox.showwarning("警告", "目标文件夹不存在")
            return

        # 获取选中的文件
        selected_files = [f for f in self.file_list if f['selected']]
        if not selected_files:
            messagebox.showwarning("警告", "请先选择要操作的文件")
            return

        # 根据模式确定操作类型
        is_move = self.operation_mode.get() == "move"
        action = "搬运" if is_move else "复制"
        mode_icon = "✂️" if is_move else "📋"

        # 确认操作
        if not messagebox.askyesno("确认操作", f"{mode_icon} 确定要{action} {len(selected_files)} 个项目吗？\n\n操作模式: {action}模式\n源文件夹: {source_folder}\n目标文件夹: {target_folder}"):
            return

        # 执行操作
        self.status_var.set(f"正在{action}文件...")
        self.root.update()

        success_count = 0
        error_count = 0
        error_details = []

        for file_info in selected_files:
            try:
                source_path = file_info['path']
                target_path = os.path.join(target_folder, file_info['name'])

                # 检查目标文件是否已存在
                if os.path.exists(target_path):
                    if not messagebox.askyesno("文件冲突", f"目标位置已存在文件: {file_info['name']}\n是否覆盖？"):
                        continue

                if is_move:
                    shutil.move(source_path, target_path)
                else:
                    if os.path.isdir(source_path):
                        if os.path.exists(target_path):
                            shutil.rmtree(target_path)
                        shutil.copytree(source_path, target_path)
                    else:
                        shutil.copy2(source_path, target_path)

                success_count += 1

            except Exception as e:
                error_msg = f"{file_info['name']}: {str(e)}"
                error_details.append(error_msg)
                print(f"操作失败 {error_msg}")
                error_count += 1

        # 显示结果
        if error_count == 0:
            messagebox.showinfo("操作完成", f"🎉 成功{action} {success_count} 个项目")
            self.status_var.set(f"操作完成 - 成功{action} {success_count} 个项目")
        else:
            error_summary = "\n".join(error_details[:5])  # 只显示前5个错误
            if len(error_details) > 5:
                error_summary += f"\n... 还有 {len(error_details) - 5} 个错误"
            messagebox.showwarning("部分完成", f"成功{action} {success_count} 个项目\n失败 {error_count} 个项目\n\n错误详情:\n{error_summary}")
            self.status_var.set(f"部分完成 - 成功{action} {success_count} 个，失败 {error_count} 个")

        # 刷新文件列表
        if is_move:  # 搬运模式需要刷新源文件夹
            self.scan_files()

        # 保存设置
        self.save_settings()
    
    def update_file_cache(self):
        """更新文件缓存"""
        for file_info in self.file_list:
            self.file_cache[file_info['path']] = file_info['mtime']
        self.save_file_cache()

def main():
    root = tk.Tk()
    app = FileMoverGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
