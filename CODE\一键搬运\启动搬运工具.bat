@echo off
cd /d "%~dp0"

echo ========================================
echo    File Mover Tool v1.0
echo    文件快速搬运工具
echo ========================================
echo.
echo 功能特性:
echo   📋 复制模式 - 保留原文件
echo   ✂️ 搬运模式 - 移动文件
echo   💾 自动保存路径设置
echo   🔍 智能新文件检测
echo.
echo Starting tool...
echo.

REM Try different Python commands
python file_mover.py
if %errorlevel% neq 0 (
    echo Python command failed, trying py...
    py file_mover.py
    if %errorlevel% neq 0 (
        echo py command failed, trying python3...
        python3 file_mover.py
        if %errorlevel% neq 0 (
            echo.
            echo ERROR: Python not found or failed to start!
            echo Please make sure Python is installed and in PATH.
            echo.
            echo You can also run the tool directly:
            echo   python file_mover.py
            echo.
        )
    )
)

echo.
echo Tool closed.
pause
