// Manifest V3 API compatibility layer
// This handles the differences between V2 and V3 APIs

// Check if we're in V3 and need to adapt APIs
if (typeof chrome !== 'undefined' && chrome.scripting) {
  // Override browser.tabs.executeScript to use chrome.scripting.executeScript
  if (browser && browser.tabs) {
    const originalExecuteScript = browser.tabs.executeScript;
    
    browser.tabs.executeScript = async function(tabId, details) {
      try {
        if (details.code) {
          // Execute code
          const results = await chrome.scripting.executeScript({
            target: { tabId: tabId },
            func: new Function(details.code)
          });
          return results.map(r => r.result);
        } else if (details.file) {
          // Execute file
          const results = await chrome.scripting.executeScript({
            target: { tabId: tabId },
            files: [details.file]
          });
          return results.map(r => r.result);
        }
      } catch (error) {
        console.error('Script execution failed:', error);
        throw error;
      }
    };
  }
}

// Handle chrome.tabs.update vs browser.tabs.update
if (typeof chrome !== 'undefined' && chrome.tabs && chrome.tabs.update) {
  browser.tabs.update = chrome.tabs.update;
}