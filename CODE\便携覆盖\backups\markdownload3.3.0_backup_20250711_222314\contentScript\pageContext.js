// MarkDownload Page Context Script
console.log('MarkDownload: Page context script loaded');

function addLatexToMathJax3()
{
    try {
        if (!MathJax?.startup?.document?.math)
            return

        for (math of MathJax.startup.document.math)
        {
            if (math && math.typesetRoot && typeof math.typesetRoot.setAttribute === 'function' && math.math) {
                math.typesetRoot.setAttribute("markdownload-latex", math.math)
            }
        }
    } catch (error) {
        console.warn('MarkdownLoad: Error in addLatexToMathJax3:', error);
    }
}

// Wrap in try-catch to prevent errors from affecting other extensions
try {
    addLatexToMathJax3();
} catch (error) {
    console.warn('MarkdownLoad: Failed to execute addLatexToMathJax3:', error);
}
