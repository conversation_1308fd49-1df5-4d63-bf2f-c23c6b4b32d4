
<!DOCTYPE html>

<html>
  <head>
    <meta charset="utf-8">
    <link rel="stylesheet" href="lib/codemirror.css">
    <link rel="stylesheet" href="lib/xq-dark.css">
    <link rel="stylesheet" href="lib/xq-light.css">
    <link rel="stylesheet" href="popup.css">
  </head>

<body>
  <div id="container">
    
    <a href="#" class="button toggle" id="downloadImages"><i class="check"></i>Download Images</a>
    <a href="/options/options.html" id="options" target="_blank">open options</a>
    <div class="row" id="clipOption">
      <a href="#" class="button toggle" id="selected"><i class="check"></i>Selected Text</a>
      <a href="#" class="button toggle" id="document"><i class="check"></i>Entire Document</a>
    </div>
    <a href="#" class="button toggle" id="includeTemplate"><i class="check"></i>Include front/back template</a>
    <input type="text" id="title"></input>
    <textarea id="md"></textarea>
    <a href="#" class="button" id="download">Download</a>
    <a href="#" class="button" id="downloadSelection">Download selected</a>
  </div>
  <div id="spinner"></div>
  <script type="application/javascript" src="../browser-polyfill.min.js"></script>
  <script type="application/javascript" src="lib/codemirror.js"></script>
  <script type="application/javascript" src="lib/modes/markdown/markdown.js"></script>
  <script type="application/javascript" src="popup.js"></script>
</body>

</html>