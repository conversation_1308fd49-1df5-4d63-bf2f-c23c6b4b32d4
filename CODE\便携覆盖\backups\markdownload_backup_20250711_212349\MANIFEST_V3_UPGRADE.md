# MarkDownload Manifest V3 升级说明

## 升级内容

这个版本已经从 Manifest V2 升级到 Manifest V3，以兼容现代浏览器的要求。

### 主要变化

1. **manifest.json 更新**：
   - `manifest_version` 从 2 改为 3
   - `browser_action` 改为 `action`
   - `background.scripts` 改为 `background.service_worker`
   - 添加了 `host_permissions` 分离权限
   - 添加了 `scripting` 权限
   - `_execute_browser_action` 改为 `_execute_action`

2. **Service Worker**：
   - 创建了新的 `background/service-worker.js` 文件
   - 使用 `importScripts` 加载所有必要的脚本

3. **API 兼容性**：
   - 在 `background.js` 中添加了兼容层函数 `executeScript`
   - 更新了 `popup.js` 中的脚本注入逻辑
   - 支持新的 `browser.scripting` API

4. **Content Scripts**：
   - 在 manifest 中明确声明了 content scripts
   - 保持向后兼容性

### 安装说明

1. 在 Chrome/Edge 浏览器中打开扩展管理页面
2. 启用"开发者模式"
3. 点击"加载已解压的扩展程序"
4. 选择 markdownload 文件夹

### 功能验证

升级后的插件应该保持所有原有功能：
- 网页转 Markdown
- 快捷键支持
- 图片下载
- Obsidian 集成
- 右键菜单

### 兼容性

- ✅ Chrome 88+
- ✅ Edge 88+
- ✅ Firefox (通过 browser-polyfill)
- ✅ 其他基于 Chromium 的浏览器

### 故障排除

如果遇到问题：
1. 检查浏览器控制台是否有错误信息
2. 确认所有权限已正确授予
3. 尝试重新加载扩展程序
