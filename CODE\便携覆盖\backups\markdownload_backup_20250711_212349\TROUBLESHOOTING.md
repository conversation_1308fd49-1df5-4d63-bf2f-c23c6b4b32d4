# MarkDownload 故障排除指南

## 当前状态
插件已升级到 Manifest V3，但可能存在兼容性问题。

## 测试步骤

### 1. 基本测试
1. 重新加载扩展程序：
   - 打开 `chrome://extensions/`
   - 找到 MarkDownload 扩展
   - 点击"重新加载"按钮

2. 检查扩展图标：
   - 扩展图标应该出现在浏览器工具栏中
   - 点击图标应该打开测试弹窗

3. 测试弹窗功能：
   - 点击"Test Extension"按钮
   - 应该显示当前标签页标题
   - 点击"Download as Markdown"按钮
   - 应该显示"Download request sent!"

### 2. 检查控制台错误
1. 打开开发者工具 (F12)
2. 切换到 Console 标签
3. 查看是否有错误信息

### 3. 检查 Service Worker
1. 在 `chrome://extensions/` 页面
2. 找到 MarkDownload 扩展
3. 点击"service worker"链接
4. 查看控制台是否有错误

## 常见问题

### 问题1：扩展图标不显示
**解决方案：**
- 检查 manifest.json 语法是否正确
- 确认图标文件存在
- 重新加载扩展

### 问题2：点击图标没有反应
**解决方案：**
- 检查 popup HTML 文件是否存在
- 查看浏览器控制台错误
- 尝试使用测试弹窗

### 问题3：Service Worker 错误
**解决方案：**
- 当前使用简化的 service worker
- 检查 importScripts 路径是否正确
- 查看 service worker 控制台

## 恢复完整功能

如果测试版本工作正常，可以恢复完整功能：

1. 将 manifest.json 中的 popup 改回：
   ```json
   "default_popup": "popup/popup.html"
   ```

2. 将 service worker 改回：
   ```json
   "service_worker": "background/service-worker.js"
   ```

3. 重新加载扩展并测试

## 联系支持
如果问题持续存在，请提供：
- 浏览器版本
- 控制台错误信息
- Service Worker 错误信息
