# MarkDownload 使用指南

## ✅ 正确的测试方法

### 1. 导航到可访问的网页
扩展程序**无法**在以下页面工作：
- `chrome://` 页面（如 chrome://extensions/）
- `edge://` 页面
- `about:` 页面
- 浏览器内部页面

### 2. 推荐的测试网站
请在以下类型的网站测试扩展：
- **Wikipedia**: https://en.wikipedia.org/wiki/Markdown
- **新闻网站**: https://www.bbc.com/news
- **技术博客**: https://github.blog/
- **文档网站**: https://docs.github.com/
- **任何普通的网页**

### 3. 测试步骤
1. 打开上述任一网站
2. 点击浏览器工具栏中的 MarkDownload 图标
3. 在弹窗中点击"Test Extension"
4. 应该显示：`✅ Current tab: [网页标题]`
5. 点击"Download as Markdown"测试下载功能

## 🔧 功能说明

### 基本功能
- **整页转换**: 将整个网页转换为 Markdown 格式
- **选择转换**: 只转换选中的文本
- **图片下载**: 可选择是否下载网页中的图片
- **模板支持**: 支持前置和后置模板

### 快捷键
- `Alt+Shift+M`: 打开扩展弹窗
- `Alt+Shift+D`: 直接下载当前页面为 Markdown
- `Alt+Shift+C`: 复制当前页面为 Markdown 到剪贴板
- `Alt+Shift+L`: 复制当前页面链接为 Markdown 格式

### 右键菜单
在网页上右键点击，可以看到 MarkDownload 相关选项：
- 下载为 Markdown
- 复制为 Markdown
- 复制链接为 Markdown 格式

## ❌ 常见错误及解决方案

### 错误: "Cannot access a chrome:// URL"
**原因**: 尝试在浏览器内部页面使用扩展
**解决**: 导航到普通网页再使用扩展

### 错误: "Error clipping the page"
**可能原因**:
1. 网页阻止了脚本注入
2. 网页使用了复杂的安全策略
3. 网络连接问题

**解决方案**:
1. 刷新页面后重试
2. 尝试其他网站
3. 检查浏览器控制台错误信息

### 扩展图标不显示
**解决方案**:
1. 确认扩展已启用
2. 重新加载扩展
3. 重启浏览器

## 🚀 恢复完整功能

当测试版本工作正常后，可以恢复完整功能：

1. 修改 `manifest.json`:
   ```json
   "default_popup": "popup/popup.html"
   "service_worker": "background/service-worker.js"
   ```

2. 重新加载扩展

3. 在普通网页测试所有功能
