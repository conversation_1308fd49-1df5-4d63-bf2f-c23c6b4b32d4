<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            width: 300px;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .button {
            display: block;
            width: 100%;
            padding: 10px;
            margin: 5px 0;
            background: #4CAF50;
            color: white;
            text-decoration: none;
            text-align: center;
            border-radius: 4px;
        }
        .button:hover {
            background: #45a049;
        }
        #status {
            margin: 10px 0;
            padding: 10px;
            background: #f0f0f0;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h3>MarkDownload Test</h3>
    <div id="status">Loading...</div>
    <div style="background: #e7f3ff; padding: 10px; margin: 10px 0; border-radius: 4px; font-size: 12px;">
        <strong>💡 提示：</strong>要测试扩展功能，请先导航到一个普通网页（如 Wikipedia、新闻网站等），然后再点击扩展图标。浏览器内部页面（chrome://、edge:// 等）无法被扩展访问。
    </div>
    <a href="#" class="button" id="testBtn">Test Extension</a>
    <a href="#" class="button" id="downloadBtn">Download as Markdown</a>
    
    <script src="../browser-polyfill.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const status = document.getElementById('status');
            const testBtn = document.getElementById('testBtn');
            const downloadBtn = document.getElementById('downloadBtn');
            
            status.textContent = 'Extension loaded successfully!';
            
            // Check if current tab is accessible
            async function checkTabAccess() {
                try {
                    const tabs = await browser.tabs.query({active: true, currentWindow: true});
                    const tab = tabs[0];

                    if (tab.url.startsWith('chrome://') ||
                        tab.url.startsWith('chrome-extension://') ||
                        tab.url.startsWith('edge://') ||
                        tab.url.startsWith('about:')) {
                        return {
                            accessible: false,
                            tab: tab,
                            reason: 'Browser internal page - extensions cannot access this page'
                        };
                    }

                    return {
                        accessible: true,
                        tab: tab
                    };
                } catch (error) {
                    return {
                        accessible: false,
                        reason: error.message
                    };
                }
            }

            testBtn.addEventListener('click', async function(e) {
                e.preventDefault();
                const result = await checkTabAccess();

                if (result.accessible) {
                    status.textContent = `✅ Current tab: ${result.tab.title}`;
                    status.style.background = '#d4edda';
                    status.style.color = '#155724';
                } else {
                    status.textContent = `❌ ${result.reason}`;
                    status.style.background = '#f8d7da';
                    status.style.color = '#721c24';
                }
            });

            downloadBtn.addEventListener('click', async function(e) {
                e.preventDefault();
                const result = await checkTabAccess();

                if (!result.accessible) {
                    status.textContent = `❌ Cannot download: ${result.reason}`;
                    status.style.background = '#f8d7da';
                    status.style.color = '#721c24';
                    return;
                }

                try {
                    // Send message to background script
                    browser.runtime.sendMessage({
                        type: 'test_download',
                        tab: result.tab
                    });

                    status.textContent = '✅ Download request sent!';
                    status.style.background = '#d4edda';
                    status.style.color = '#155724';
                } catch (error) {
                    status.textContent = `❌ Error: ${error.message}`;
                    status.style.background = '#f8d7da';
                    status.style.color = '#721c24';
                }
            });
        });
    </script>
</body>
</html>
