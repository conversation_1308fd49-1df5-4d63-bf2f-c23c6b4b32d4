// Simple service worker for testing
console.log('MarkDownload simple service worker starting...');

// Basic installation
chrome.runtime.onInstalled.addListener(() => {
  console.log('MarkDownload extension installed');
});

// Basic message listener
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('Service worker received message:', message);
  
  if (message.type === 'test') {
    console.log('Test message received');
    sendResponse({success: true, message: 'Service worker is working'});
  }
  
  return true; // Keep message channel open for async response
});

console.log('MarkDownload simple service worker loaded successfully');
