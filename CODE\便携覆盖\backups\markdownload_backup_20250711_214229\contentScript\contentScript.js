function notifyExtension() {
    // send a message that the content should be clipped
    browser.runtime.sendMessage({ type: "clip", dom: content});
}

function getHTMLOfDocument() {
    // if the document doesn't have a "base" element make one
    // this allows the DOM parser in future steps to fix relative uris
    let baseEl = document.createElement('base');

    // check for a existing base elements - but only if document.head exists
    if (document.head) {
        let baseEls = document.head.getElementsByTagName('base');
        if (baseEls.length > 0) {
            baseEl = baseEls[0];
        }
        // if we don't find one, append this new one.
        else {
            try {
                document.head.append(baseEl);
            } catch (e) {
                console.warn('Could not append base element:', e);
            }
        }
    }

    // if the base element doesn't have a href, use the current location
    if (baseEl && !baseEl.getAttribute('href')) {
        try {
            baseEl.setAttribute('href', window.location.href);
        } catch (e) {
            console.warn('Could not set base href:', e);
        }
    }
    
    // remove the hidden content from the page - but only if document.body exists
    if (document.body) {
        try {
            removeHiddenNodes(document.body);
        } catch (e) {
            console.warn('Could not remove hidden nodes:', e);
        }
    }

    // get the content of the page as a string
    return document.documentElement ? document.documentElement.outerHTML : '<html><body>Error: Could not access document</body></html>';
}

// code taken from here: https://www.reddit.com/r/javascript/comments/27bcao/anyone_have_a_method_for_finding_all_the_hidden/
function removeHiddenNodes(root) {
    let nodeIterator, node,i = 0;

    nodeIterator = document.createNodeIterator(root, NodeFilter.SHOW_ELEMENT, function(node) {
      let nodeName = node.nodeName.toLowerCase();
      if (nodeName === "script" || nodeName === "style" || nodeName === "noscript" || nodeName === "math") {
        return NodeFilter.FILTER_REJECT;
      }
      if (node.offsetParent === void 0) {
        return NodeFilter.FILTER_ACCEPT;
      }
      let computedStyle = window.getComputedStyle(node, null);
      if (computedStyle.getPropertyValue("visibility") === "hidden" || computedStyle.getPropertyValue("display") === "none") {
        return NodeFilter.FILTER_ACCEPT;
      }
    });

    while ((node = nodeIterator.nextNode()) && ++i) {
      if (node.parentNode instanceof HTMLElement) {
        node.parentNode.removeChild(node);
      }
    }
    return root
  }
  

// code taken from here: https://stackoverflow.com/a/5084044/304786
function getHTMLOfSelection() {
    var range;
    if (document.selection && document.selection.createRange) {
        range = document.selection.createRange();
        return range.htmlText;
    } else if (window.getSelection) {
        var selection = window.getSelection();
        if (selection.rangeCount > 0) {
            let content = '';
            for (let i = 0; i < selection.rangeCount; i++) {
                range = selection.getRangeAt(0);
                var clonedSelection = range.cloneContents();
                var div = document.createElement('div');
                div.appendChild(clonedSelection);
                content += div.innerHTML;
            }
            return content;
        } else {
            return '';
        }
    } else {
        return '';
    }
}

function getSelectionAndDom() {
    try {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            return {
                selection: '',
                dom: '<html><body>Document still loading...</body></html>',
                error: 'Document not ready'
            };
        }

        return {
            selection: getHTMLOfSelection(),
            dom: getHTMLOfDocument()
        }
    } catch (error) {
        console.error('Error in getSelectionAndDom:', error);
        return {
            selection: '',
            dom: document.documentElement ? document.documentElement.outerHTML : '<html><body>Error: Could not access document</body></html>',
            error: error.message
        }
    }
}

// Ensure the script only runs when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
        console.log('MarkDownload content script loaded after DOMContentLoaded');
    });
} else {
    console.log('MarkDownload content script loaded - DOM already ready');
}

// This function must be called in a visible page, such as a browserAction popup
// or a content script. Calling it in a background page has no effect!
function copyToClipboard(text) {
    navigator.clipboard.writeText(text);
}

function downloadMarkdown(filename, text) {
    let datauri = `data:text/markdown;base64,${text}`;
    var link = document.createElement('a');
    link.download = filename;
    link.href = datauri;
    link.click();
}

function downloadImage(filename, url) {

    /* Link with a download attribute? CORS says no.
    var link = document.createElement('a');
    link.download = filename.substring(0, filename.lastIndexOf('.'));
    link.href = url;
    console.log(link);
    link.click();
    */

    /* Try via xhr? Blocked by CORS.
    var xhr = new XMLHttpRequest();
    xhr.open('GET', url, true);
    xhr.responseType = 'blob';
    xhr.onload = () => {
        console.log('onload!')
        var file = new Blob([xhr.response], {type: 'application/octet-stream'});
        var link = document.createElement('a');
        link.download = filename;//.substring(0, filename.lastIndexOf('.'));
        link.href = window.URL.createObjectURL(file);
        console.log(link);
        link.click();
    }
    xhr.send();
    */

    /* draw on canvas? Inscure operation
    let img = new Image();
    img.src = url;
    img.onload = () => {
        let canvas = document.createElement("canvas");
        let ctx = canvas.getContext("2d");
        canvas.width = img.width;
        canvas.height = img.height;
        ctx.drawImage(img, 0, 0);

        var link = document.createElement('a');
        const ext = filename.substring(filename.lastIndexOf('.'));
        link.download = filename;
        link.href = canvas.toDataURL(`image/png`);
        console.log(link);
        link.click();
    }
    */
}

(function loadPageContextScript(){
    var s = document.createElement('script');
    s.src = browser.runtime.getURL('contentScript/pageContext.js');
    (document.head||document.documentElement).appendChild(s);
})()
