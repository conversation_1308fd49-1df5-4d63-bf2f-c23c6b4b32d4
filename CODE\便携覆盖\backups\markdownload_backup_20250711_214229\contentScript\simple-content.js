// Simple content script for testing
console.log('MarkDownload simple content script loaded');

// Simple function to test
function simpleGetPageInfo() {
    console.log('simpleGetPageInfo called');
    return {
        title: document.title || 'No title',
        url: window.location.href,
        readyState: document.readyState,
        hasHead: !!document.head,
        hasBody: !!document.body
    };
}

// Make function globally available
window.simpleGetPageInfo = simpleGetPageInfo;

// Test DOM access
try {
    console.log('MarkDownload: Page title:', document.title);
    console.log('MarkDownload: Document ready state:', document.readyState);
    console.log('MarkDownload: Has head:', !!document.head);
    console.log('MarkDownload: Has body:', !!document.body);
} catch (error) {
    console.error('MarkDownload: Error accessing DOM:', error);
}

console.log('MarkDownload simple content script setup complete');
