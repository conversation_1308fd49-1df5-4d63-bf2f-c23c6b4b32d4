{"theme": "GitHub", "autoAccept": false, "sandbox": false, "contextFileName": "GEMINI.md", "fileFiltering": {"respectGitIgnore": true, "enableRecursiveFileSearch": true}, "mcpServers": {"basic-memory": {"command": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\basic-memory.exe", "args": ["mcp"], "env": {"PYTHONIOENCODING": "utf-8", "PYTHONUNBUFFERED": "1", "BASIC_MEMORY_HOME": "C:\\Users\\<USER>\\basic-memory"}, "cwd": "C:\\Users\\<USER>"}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "tavily-mcp": {"command": "npx", "args": ["-y", "tavily-mcp"], "env": {"TAVILY_API_KEY": "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"}}, "firecrawl-mcp": {"command": "npx", "args": ["-y", "firecrawl-mcp"], "env": {"FIRECRAWL_API_KEY": "fc-5605c0518e3c4116992b39c375898205"}}, "playwright": {"command": "npx", "args": ["@playwright/mcp@latest"]}, "quickchart-server": {"command": "npx", "args": ["-y", "@gongrzhe/quickchart-mcp-server"]}, "MiniMax": {"command": "uvx", "args": ["minimax-mcp"], "env": {"MINIMAX_GROUP_ID": "191676883242111081", "MINIMAX_API_KEY": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************.JbSIaS6KtmKQlEvy9gqrcNgDgUWq2ItHe3zJVLkrS1bBVDda6ugn9joMevOiPU9TnhlKJOfLeCe3nyYqaeWLhlrxXf__ubIT1ukYKE8zTndfrtm5BXy5Q_KVNC3xh9Sz609Zc24rUEWdUMTkXgRcmVg4qkSr_UxANJXoJo1R7GHodPezvvlIxcyyw7hG02dOmC1ZzoW9gn_kZSHDKMRj2p_FPctGZs9eYQSORns2-YDSzSC1BnqmJkNDAbj3xSQzQlBbzfNQLCDTTXYtMLOnMOLV2_zzlWfCDt2qI9dy3UHpl7LUF5jAMWjK0WkHQfUnc8MyNdDP22vouiGX9lIOCw", "MINIMAX_MCP_BASE_PATH": "D:/360MoveData/Users/<USER>/Desktop/minimax_output", "MINIMAX_API_HOST": "https://api.minimax.chat", "MINIMAX_API_RESOURCE_MODE": "local"}}, "edgeone-pages-mcp-server": {"command": "npx", "args": ["edgeone-pages-mcp"]}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "amap-maps": {"command": "npx", "args": ["-y", "@amap/amap-maps-mcp-server"], "env": {"AMAP_MAPS_API_KEY": "c00d06c57e7ccc6f09bc829ba044969e"}}, "12306-mcp": {"type": "sse", "url": "https://mcp.api-inference.modelscope.net/7566d7a07e5248/sse"}}, "telemetry": {"enabled": false, "logPrompts": false}, "usageStatisticsEnabled": false, "hideTips": false, "preferredEditor": "vscode", "selectedAuthType": "oauth-personal"}