body {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    margin: 0;
    background-color: #f0f0f0;
    flex-direction: column;
    font-family: Arial, sans-serif;
}

.game-container {
    display: flex;
    gap: 20px;
    align-items: flex-start;
}

.game-info {
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    min-width: 200px;
}

.difficulty-section {
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 2px solid #eee;
}

.difficulty-section h3 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 16px;
}

.difficulty-buttons {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.difficulty-btn {
    flex: 1;
    padding: 8px 12px;
    border: 2px solid #007bff;
    background-color: #fff;
    color: #007bff;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.difficulty-btn:hover {
    background-color: #e3f2fd;
}

.difficulty-btn.active {
    background-color: #007bff;
    color: white;
}

.difficulty-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.start-btn {
    width: 100%;
    padding: 10px;
    background-color: #28a745;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.start-btn:hover {
    background-color: #218838;
}

.info-item {
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.info-item label {
    font-weight: bold;
    color: #333;
}

.info-item span {
    font-size: 18px;
    color: #007bff;
}

.controls {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.controls p {
    margin: 5px 0;
    font-size: 12px;
    color: #666;
}

#game-board {
    display: grid;
    grid-template-columns: repeat(10, 30px);
    grid-template-rows: repeat(20, 30px);
    border: 2px solid #333;
    background-color: #fff;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.block {
    width: 30px;
    height: 30px;
    background-color: #ccc;
    border: 1px solid #fff;
    box-sizing: border-box;
}

.color-1 { background-color: cyan; }
.color-2 { background-color: blue; }
.color-3 { background-color: orange; }
.color-4 { background-color: yellow; }
.color-5 { background-color: green; }
.color-6 { background-color: purple; }
.color-7 { background-color: red; }

/* Game Over Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    animation: fadeIn 0.3s ease-in-out;
}

.modal.show {
    display: flex;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: #fff;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    text-align: center;
    min-width: 350px;
    animation: slideIn 0.3s ease-out;
}

.modal-content h2 {
    margin: 0 0 20px 0;
    color: #333;
    font-size: 24px;
}

.game-over-stats {
    margin: 20px 0;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 10px 0;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-label {
    font-weight: bold;
    color: #555;
}

.stat-item span:last-child {
    color: #007bff;
    font-weight: bold;
}

.new-record {
    background-color: #fff3cd;
    border: 2px solid #ffc107;
    border-radius: 6px;
    padding: 10px !important;
    margin: 15px 0 !important;
}

.new-record .stat-label {
    color: #856404;
    font-size: 18px;
}

.modal-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 20px;
}

.modal-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
}

.modal-btn.primary {
    background-color: #28a745;
    color: white;
}

.modal-btn.primary:hover {
    background-color: #218838;
    transform: translateY(-2px);
}

.modal-btn.secondary {
    background-color: #6c757d;
    color: white;
}

.modal-btn.secondary:hover {
    background-color: #545b62;
    transform: translateY(-2px);
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { 
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to { 
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}