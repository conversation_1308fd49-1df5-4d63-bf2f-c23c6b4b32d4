#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件复制/覆盖工具 - 多版本备份增强版
功能：智能文件复制和覆盖，支持多版本备份和恢复
特性：
- 智能模式：目标文件存在时覆盖，不存在时复制
- 带时间戳的多版本备份
- 持久化备份历史记录
- 备份管理界面
- 支持从历史版本恢复
- 自动创建目标目录
"""

import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import shutil
import os
import json
from datetime import datetime
from pathlib import Path

class FileCopierGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("文件覆盖工具 - 多版本备份增强版")
        self.root.geometry("800x600")

        # 文件操作变量
        self.source_file = tk.StringVar()
        self.target_file = tk.StringVar()

        # 文件夹操作变量
        self.source_folder = tk.StringVar()
        self.target_folder = tk.StringVar()

        # 备份管理 - 便携式设计
        self.script_dir = os.path.dirname(os.path.abspath(__file__))  # 脚本所在目录
        self.backup_dir = os.path.join(self.script_dir, "backups")  # 备份文件夹（相对于脚本位置）
        self.backup_history_file = os.path.join(self.script_dir, "backup_history.json")  # 备份历史记录文件
        self.backup_history = []  # 备份历史列表
        self.custom_backup_dir = None  # 自定义备份目录

        # 初始化
        self.init_backup_system()
        self.load_backup_history()
        self.setup_ui()

    def init_backup_system(self):
        """初始化备份系统"""
        # 创建默认备份文件夹
        if not os.path.exists(self.backup_dir):
            os.makedirs(self.backup_dir)

        # 加载自定义备份路径设置（如果存在）
        self.load_settings()

    def load_backup_history(self):
        """加载备份历史记录"""
        try:
            if os.path.exists(self.backup_history_file):
                with open(self.backup_history_file, 'r', encoding='utf-8') as f:
                    self.backup_history = json.load(f)
            else:
                self.backup_history = []
        except Exception as e:
            self.backup_history = []
            print(f"加载备份历史失败: {e}")

    def save_backup_history(self):
        """保存备份历史记录"""
        try:
            with open(self.backup_history_file, 'w', encoding='utf-8') as f:
                json.dump(self.backup_history, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存备份历史失败: {e}")

    def load_settings(self):
        """加载设置"""
        settings_file = os.path.join(self.script_dir, "settings.json")
        try:
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    self.custom_backup_dir = settings.get("custom_backup_dir")
        except Exception as e:
            print(f"加载设置失败: {e}")

    def save_settings(self):
        """保存设置"""
        settings_file = os.path.join(self.script_dir, "settings.json")
        try:
            settings = {
                "custom_backup_dir": self.custom_backup_dir
            }
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存设置失败: {e}")

    def add_backup_record(self, source_file, target_file, backup_file):
        """添加备份记录"""
        record = {
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "source_file": source_file,
            "target_file": target_file,
            "backup_file": backup_file,
            "backup_size": os.path.getsize(backup_file) if os.path.exists(backup_file) else 0
        }
        self.backup_history.append(record)
        self.save_backup_history()

    def generate_backup_filename(self, target_file):
        """生成带时间戳的备份文件名"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_path = Path(target_file)
        backup_name = f"{file_path.stem}_backup_{timestamp}{file_path.suffix}"
        current_backup_dir = self.get_current_backup_dir()

        # 确保备份目录存在
        if not os.path.exists(current_backup_dir):
            os.makedirs(current_backup_dir)

        return os.path.join(current_backup_dir, backup_name)

    def setup_ui(self):
        # 创建笔记本控件（标签页）
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 主操作标签页
        main_frame = ttk.Frame(notebook, padding="10")
        notebook.add(main_frame, text="文件操作")

        # 备份管理标签页
        backup_frame = ttk.Frame(notebook, padding="10")
        notebook.add(backup_frame, text="备份管理")

        # 设置标签页
        settings_frame = ttk.Frame(notebook, padding="10")
        notebook.add(settings_frame, text="设置")

        self.setup_main_tab(main_frame)
        self.setup_backup_tab(backup_frame)
        self.setup_settings_tab(settings_frame)

        self.log("文件覆盖工具已启动 - 多版本备份增强版")

    def setup_main_tab(self, parent):
        """设置主操作标签页"""
        # 文件操作区域
        file_frame = ttk.LabelFrame(parent, text="📄 文件操作", padding="10")
        file_frame.grid(row=0, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))

        # 源文件选择
        ttk.Label(file_frame, text="源文件 (A):").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Entry(file_frame, textvariable=self.source_file, width=50).grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(file_frame, text="浏览", command=self.select_source_file).grid(row=0, column=2, pady=5)

        # 目标路径选择
        ttk.Label(file_frame, text="目标路径 (B):").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Entry(file_frame, textvariable=self.target_file, width=50).grid(row=1, column=1, padx=5, pady=5)

        # 目标路径按钮组
        target_button_frame = ttk.Frame(file_frame)
        target_button_frame.grid(row=1, column=2, pady=5)
        ttk.Button(target_button_frame, text="选择文件", command=self.select_target_file).pack(side=tk.TOP, pady=1)
        ttk.Button(target_button_frame, text="另存为", command=self.save_as_target).pack(side=tk.TOP, pady=1)

        # 文件操作按钮
        file_button_frame = ttk.Frame(file_frame)
        file_button_frame.grid(row=2, column=0, columnspan=3, pady=10)
        ttk.Button(file_button_frame, text="执行文件操作", command=self.copy_file).pack(side=tk.LEFT, padx=5)
        ttk.Button(file_button_frame, text="清空文件选择", command=self.clear_file_selection).pack(side=tk.LEFT, padx=5)

        # 文件夹操作区域
        folder_frame = ttk.LabelFrame(parent, text="📁 文件夹操作", padding="10")
        folder_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))

        # 源文件夹选择
        ttk.Label(folder_frame, text="源文件夹 (A):").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Entry(folder_frame, textvariable=self.source_folder, width=50).grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(folder_frame, text="浏览", command=self.select_source_folder).grid(row=0, column=2, pady=5)

        # 目标文件夹选择
        ttk.Label(folder_frame, text="目标文件夹 (B):").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Entry(folder_frame, textvariable=self.target_folder, width=50).grid(row=1, column=1, padx=5, pady=5)

        # 目标文件夹按钮组
        target_folder_button_frame = ttk.Frame(folder_frame)
        target_folder_button_frame.grid(row=1, column=2, pady=5)
        ttk.Button(target_folder_button_frame, text="选择文件夹", command=self.select_target_folder).pack(side=tk.TOP, pady=1)
        ttk.Button(target_folder_button_frame, text="新建文件夹", command=self.create_target_folder).pack(side=tk.TOP, pady=1)

        # 文件夹操作按钮
        folder_button_frame = ttk.Frame(folder_frame)
        folder_button_frame.grid(row=2, column=0, columnspan=3, pady=10)
        ttk.Button(folder_button_frame, text="执行文件夹操作", command=self.copy_folder).pack(side=tk.LEFT, padx=5)
        ttk.Button(folder_button_frame, text="清空文件夹选择", command=self.clear_folder_selection).pack(side=tk.LEFT, padx=5)

        # 通用操作按钮
        common_button_frame = ttk.Frame(parent)
        common_button_frame.grid(row=2, column=0, columnspan=3, pady=10)
        ttk.Button(common_button_frame, text="快速恢复", command=self.quick_restore).pack(side=tk.LEFT, padx=5)
        ttk.Button(common_button_frame, text="清空所有选择", command=self.clear_all_selections).pack(side=tk.LEFT, padx=5)

        # 状态显示
        ttk.Label(parent, text="操作日志:").grid(row=3, column=0, sticky=tk.W, pady=(10,5))
        self.status_text = tk.Text(parent, height=12, width=70)
        self.status_text.grid(row=4, column=0, columnspan=3, pady=5)

        # 滚动条
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=self.status_text.yview)
        scrollbar.grid(row=4, column=3, sticky=(tk.N, tk.S))
        self.status_text.configure(yscrollcommand=scrollbar.set)

        # 配置网格权重
        parent.columnconfigure(1, weight=1)
        parent.rowconfigure(4, weight=1)
        file_frame.columnconfigure(1, weight=1)
        folder_frame.columnconfigure(1, weight=1)

    def setup_backup_tab(self, parent):
        """设置备份管理标签页"""
        # 备份列表标题
        ttk.Label(parent, text="备份历史记录:", font=("", 10, "bold")).grid(row=0, column=0, sticky=tk.W, pady=(0,10))

        # 备份列表框架
        list_frame = ttk.Frame(parent)
        list_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)

        # 备份列表
        columns = ("时间", "类型", "源文件", "目标文件", "备份文件", "大小")
        self.backup_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=15)

        # 设置列标题和宽度
        self.backup_tree.heading("时间", text="备份时间")
        self.backup_tree.heading("类型", text="类型")
        self.backup_tree.heading("源文件", text="源文件")
        self.backup_tree.heading("目标文件", text="目标文件")
        self.backup_tree.heading("备份文件", text="备份文件")
        self.backup_tree.heading("大小", text="大小")

        self.backup_tree.column("时间", width=130)
        self.backup_tree.column("类型", width=60)
        self.backup_tree.column("源文件", width=180)
        self.backup_tree.column("目标文件", width=180)
        self.backup_tree.column("备份文件", width=180)
        self.backup_tree.column("大小", width=80)

        self.backup_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 滚动条
        backup_scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.backup_tree.yview)
        backup_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.backup_tree.configure(yscrollcommand=backup_scrollbar.set)

        # 备份管理按钮
        backup_button_frame = ttk.Frame(parent)
        backup_button_frame.grid(row=2, column=0, columnspan=3, pady=20)

        ttk.Button(backup_button_frame, text="刷新列表", command=self.refresh_backup_list).pack(side=tk.LEFT, padx=5)
        ttk.Button(backup_button_frame, text="恢复选中", command=self.restore_selected_backup).pack(side=tk.LEFT, padx=5)
        ttk.Button(backup_button_frame, text="删除选中", command=self.delete_selected_backup).pack(side=tk.LEFT, padx=5)
        ttk.Button(backup_button_frame, text="清理所有", command=self.clear_all_backups).pack(side=tk.LEFT, padx=5)

        # 配置网格权重
        parent.columnconfigure(0, weight=1)
        parent.rowconfigure(1, weight=1)

        # 初始加载备份列表
        self.refresh_backup_list()

    def setup_settings_tab(self, parent):
        """设置标签页"""
        # 备份路径设置
        path_frame = ttk.LabelFrame(parent, text="备份路径设置", padding="10")
        path_frame.pack(fill=tk.X, pady=(0, 20))

        # 当前备份路径显示
        ttk.Label(path_frame, text="当前备份路径:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.current_backup_path = tk.StringVar(value=self.get_current_backup_dir())
        ttk.Label(path_frame, textvariable=self.current_backup_path, foreground="blue").grid(row=0, column=1, sticky=tk.W, padx=(10,0), pady=5)

        # 路径选择选项
        self.backup_path_mode = tk.StringVar(value="custom" if self.custom_backup_dir else "default")
        ttk.Radiobutton(path_frame, text="默认路径（脚本目录下的backups文件夹）",
                       variable=self.backup_path_mode, value="default",
                       command=self.update_backup_path).grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=5)

        ttk.Radiobutton(path_frame, text="自定义路径:",
                       variable=self.backup_path_mode, value="custom",
                       command=self.update_backup_path).grid(row=2, column=0, sticky=tk.W, pady=5)

        # 自定义路径输入
        custom_path_frame = ttk.Frame(path_frame)
        custom_path_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)

        self.custom_backup_path = tk.StringVar(value=self.custom_backup_dir or "")
        ttk.Entry(custom_path_frame, textvariable=self.custom_backup_path, width=50).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(custom_path_frame, text="浏览", command=self.select_custom_backup_dir).pack(side=tk.LEFT, padx=(5,0))

        # 应用按钮
        ttk.Button(path_frame, text="应用设置", command=self.apply_backup_settings).grid(row=4, column=0, pady=10)

        # 工具信息
        info_frame = ttk.LabelFrame(parent, text="工具信息", padding="10")
        info_frame.pack(fill=tk.X, pady=(0, 20))

        info_text = f"""便携式文件复制/覆盖工具
版本: 2.0 多版本备份增强版
脚本位置: {self.script_dir}
默认备份位置: {os.path.join(self.script_dir, 'backups')}

特性:
• 智能复制/覆盖模式
• 带时间戳的多版本备份
• 便携式设计，可放置在任意位置
• 自定义备份路径
• 持久化备份历史记录"""

        ttk.Label(info_frame, text=info_text, justify=tk.LEFT).pack(anchor=tk.W)

        # 配置网格权重
        path_frame.columnconfigure(1, weight=1)
        custom_path_frame.columnconfigure(0, weight=1)

    def get_current_backup_dir(self):
        """获取当前备份目录"""
        return self.custom_backup_dir if self.custom_backup_dir else self.backup_dir

    def select_custom_backup_dir(self):
        """选择自定义备份目录"""
        directory = filedialog.askdirectory(title="选择备份文件夹")
        if directory:
            self.custom_backup_path.set(directory)

    def update_backup_path(self):
        """更新备份路径显示"""
        if self.backup_path_mode.get() == "default":
            self.current_backup_path.set(self.backup_dir)
        else:
            custom_path = self.custom_backup_path.get()
            if custom_path:
                self.current_backup_path.set(custom_path)
            else:
                self.current_backup_path.set("请选择自定义路径")

    def apply_backup_settings(self):
        """应用备份设置"""
        if self.backup_path_mode.get() == "default":
            self.custom_backup_dir = None
            new_backup_dir = self.backup_dir
        else:
            custom_path = self.custom_backup_path.get()
            if not custom_path:
                messagebox.showerror("错误", "请选择自定义备份路径")
                return
            if not os.path.exists(custom_path):
                try:
                    os.makedirs(custom_path)
                except Exception as e:
                    messagebox.showerror("错误", f"无法创建备份目录: {str(e)}")
                    return
            self.custom_backup_dir = custom_path
            new_backup_dir = custom_path

        # 保存设置
        self.save_settings()

        # 更新显示
        self.current_backup_path.set(new_backup_dir)

        # 刷新备份列表
        self.refresh_backup_list()

        self.log(f"✅ 备份路径已更新: {new_backup_dir}")
        messagebox.showinfo("成功", f"备份路径已更新为:\n{new_backup_dir}")

    def select_source_file(self):
        """选择源文件"""
        filename = filedialog.askopenfilename(
            title="选择源文件",
            filetypes=[("所有文件", "*.*")]
        )
        if filename:
            self.source_file.set(filename)
            self.log(f"已选择源文件: {filename}")
            
    def select_target_file(self):
        """选择已存在的目标文件（覆盖模式）"""
        filename = filedialog.askopenfilename(
            title="选择要覆盖的目标文件",
            filetypes=[("所有文件", "*.*")]
        )
        if filename:
            self.target_file.set(filename)
            self.log(f"已选择目标文件（覆盖模式）: {filename}")

    def save_as_target(self):
        """另存为新文件（复制模式）"""
        filename = filedialog.asksaveasfilename(
            title="另存为新文件",
            filetypes=[("所有文件", "*.*")]
        )
        if filename:
            self.target_file.set(filename)
            self.log(f"已设置目标路径（复制模式）: {filename}")

    def select_source_folder(self):
        """选择源文件夹"""
        folder = filedialog.askdirectory(title="选择源文件夹")
        if folder:
            self.source_folder.set(folder)
            self.log(f"已选择源文件夹: {folder}")

    def select_target_folder(self):
        """选择已存在的目标文件夹（覆盖模式）"""
        folder = filedialog.askdirectory(title="选择要覆盖的目标文件夹")
        if folder:
            self.target_folder.set(folder)
            self.log(f"已选择目标文件夹（覆盖模式）: {folder}")

    def create_target_folder(self):
        """创建新的目标文件夹（复制模式）"""
        folder = filedialog.askdirectory(title="选择父目录")
        if folder:
            # 弹出对话框让用户输入新文件夹名称
            from tkinter import simpledialog
            folder_name = simpledialog.askstring("新建文件夹", "请输入新文件夹名称:")
            if folder_name:
                new_folder = os.path.join(folder, folder_name)
                self.target_folder.set(new_folder)
                self.log(f"已设置目标文件夹（复制模式）: {new_folder}")
            
    def copy_file(self):
        """执行文件复制或覆盖"""
        source = self.source_file.get()
        target = self.target_file.get()

        # 验证输入
        if not source or not target:
            messagebox.showerror("错误", "请先选择源文件和目标路径")
            return

        if not os.path.exists(source):
            messagebox.showerror("错误", f"源文件不存在: {source}")
            return

        # 检查目标文件是否存在，决定是复制还是覆盖
        target_exists = os.path.exists(target)

        if target_exists:
            # 覆盖模式
            operation = "覆盖"
            confirm_msg = f"目标文件已存在，确定要覆盖吗?\n\n源文件: {source}\n目标文件: {target}\n\n⚠️ 原文件将被备份"
        else:
            # 复制模式
            operation = "复制"
            # 检查目标目录是否存在，不存在则创建
            target_dir = os.path.dirname(target)
            if target_dir and not os.path.exists(target_dir):
                try:
                    os.makedirs(target_dir)
                    self.log(f"已创建目录: {target_dir}")
                except Exception as e:
                    messagebox.showerror("错误", f"无法创建目录: {target_dir}\n错误: {str(e)}")
                    return
            confirm_msg = f"确定要复制文件到新位置吗?\n\n源文件: {source}\n目标位置: {target}"

        # 确认操作
        if not messagebox.askyesno("确认", confirm_msg):
            return

        try:
            backup_path = None

            if target_exists:
                # 覆盖模式：先备份原文件
                backup_path = self.generate_backup_filename(target)
                shutil.copy2(target, backup_path)
                self.log(f"已备份原文件: {backup_path}")

            # 执行复制/覆盖
            shutil.copy2(source, target)

            # 记录操作
            if target_exists:
                self.add_backup_record(source, target, backup_path)
                self.log(f"✅ {operation}成功!")
                self.log(f"   源文件: {source}")
                self.log(f"   目标文件: {target}")
                self.log(f"   备份文件: {backup_path}")
                messagebox.showinfo("成功", f"文件{operation}完成!\n备份已保存")
            else:
                self.log(f"✅ {operation}成功!")
                self.log(f"   源文件: {source}")
                self.log(f"   目标文件: {target}")
                messagebox.showinfo("成功", f"文件{operation}完成!")

        except Exception as e:
            messagebox.showerror("错误", f"{operation}失败: {str(e)}")
            self.log(f"❌ {operation}失败: {str(e)}")

    def copy_folder(self):
        """执行文件夹复制或覆盖"""
        source = self.source_folder.get()
        target = self.target_folder.get()

        # 验证输入
        if not source or not target:
            messagebox.showerror("错误", "请先选择源文件夹和目标路径")
            return

        if not os.path.exists(source):
            messagebox.showerror("错误", f"源文件夹不存在: {source}")
            return

        if not os.path.isdir(source):
            messagebox.showerror("错误", f"源路径不是文件夹: {source}")
            return

        # 检查目标文件夹是否存在，决定是复制还是覆盖
        target_exists = os.path.exists(target)

        if target_exists:
            if not os.path.isdir(target):
                messagebox.showerror("错误", f"目标路径存在但不是文件夹: {target}")
                return
            # 覆盖模式
            operation = "覆盖"
            confirm_msg = f"目标文件夹已存在，确定要覆盖吗?\n\n源文件夹: {source}\n目标文件夹: {target}\n\n⚠️ 原文件夹将被备份"
        else:
            # 复制模式
            operation = "复制"
            # 检查目标父目录是否存在，不存在则创建
            target_parent = os.path.dirname(target)
            if target_parent and not os.path.exists(target_parent):
                try:
                    os.makedirs(target_parent)
                    self.log(f"已创建父目录: {target_parent}")
                except Exception as e:
                    messagebox.showerror("错误", f"无法创建父目录: {target_parent}\n错误: {str(e)}")
                    return
            confirm_msg = f"确定要复制文件夹到新位置吗?\n\n源文件夹: {source}\n目标位置: {target}"

        # 确认操作
        if not messagebox.askyesno("确认", confirm_msg):
            return

        try:
            backup_path = None

            if target_exists:
                # 覆盖模式：先备份原文件夹
                backup_path = self.generate_folder_backup_name(target)
                shutil.copytree(target, backup_path)
                self.log(f"已备份原文件夹: {backup_path}")

                # 删除原目标文件夹（确保完全删除）
                self.log(f"正在删除目标文件夹: {target}")
                try:
                    shutil.rmtree(target)
                except Exception as e:
                    self.log(f"普通删除失败，尝试强制删除: {e}")
                    self.force_remove_directory(target)

                # 验证删除是否成功
                if os.path.exists(target):
                    raise Exception(f"无法完全删除目标文件夹: {target}")
                self.log("目标文件夹删除成功")

            # 执行复制
            self.log(f"开始复制文件夹: {source} → {target}")
            shutil.copytree(source, target)
            self.log("文件夹复制完成")

            # 验证复制结果（可选：在覆盖模式下验证文件数量）
            if target_exists:
                source_count = self.count_files_in_folder(source)
                target_count = self.count_files_in_folder(target)
                self.log(f"验证结果 - 源文件夹: {source_count}个文件, 目标文件夹: {target_count}个文件")
                if source_count != target_count:
                    self.log(f"⚠️ 警告: 文件数量不匹配！可能存在复制问题")

            # 记录操作
            if target_exists:
                self.add_backup_record(source, target, backup_path)
                self.log(f"✅ 文件夹{operation}成功!")
                self.log(f"   源文件夹: {source}")
                self.log(f"   目标文件夹: {target}")
                self.log(f"   备份文件夹: {backup_path}")
                messagebox.showinfo("成功", f"文件夹{operation}完成!\n备份已保存")
            else:
                self.log(f"✅ 文件夹{operation}成功!")
                self.log(f"   源文件夹: {source}")
                self.log(f"   目标文件夹: {target}")
                messagebox.showinfo("成功", f"文件夹{operation}完成!")

        except Exception as e:
            messagebox.showerror("错误", f"文件夹{operation}失败: {str(e)}")
            self.log(f"❌ 文件夹{operation}失败: {str(e)}")

    def generate_folder_backup_name(self, target_folder):
        """生成文件夹备份名称"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        folder_path = Path(target_folder)
        backup_name = f"{folder_path.name}_backup_{timestamp}"
        current_backup_dir = self.get_current_backup_dir()

        # 确保备份目录存在
        if not os.path.exists(current_backup_dir):
            os.makedirs(current_backup_dir)

        return os.path.join(current_backup_dir, backup_name)

    def count_files_in_folder(self, folder_path):
        """计算文件夹中的文件数量（递归）"""
        if not os.path.exists(folder_path) or not os.path.isdir(folder_path):
            return 0

        file_count = 0
        try:
            for root, dirs, files in os.walk(folder_path):
                file_count += len(files)
        except Exception as e:
            self.log(f"计算文件数量失败: {e}")
            return -1

        return file_count

    def quick_restore(self):
        """快速恢复最近的备份"""
        if not self.backup_history:
            messagebox.showwarning("警告", "没有可恢复的备份记录")
            return

        # 获取最近的备份记录
        latest_backup = self.backup_history[-1]
        backup_file = latest_backup["backup_file"]
        target_file = latest_backup["target_file"]

        if not os.path.exists(backup_file):
            messagebox.showerror("错误", f"备份文件不存在: {backup_file}")
            return

        if not messagebox.askyesno("确认", f"确定要恢复最近的备份吗?\n\n目标文件: {target_file}\n备份时间: {latest_backup['timestamp']}"):
            return

        try:
            # 智能判断是文件还是文件夹
            if os.path.isdir(backup_file):
                # 文件夹恢复
                self.restore_folder(backup_file, target_file)
            else:
                # 文件恢复
                self.restore_file_item(backup_file, target_file)

            self.log(f"✅ 快速恢复成功!")
            self.log(f"   已将 {backup_file} 恢复到 {target_file}")
            messagebox.showinfo("成功", "恢复完成!")

        except Exception as e:
            messagebox.showerror("错误", f"恢复失败: {str(e)}")
            self.log(f"❌ 恢复失败: {str(e)}")

    def restore_file(self):
        """恢复文件"""
        if not self.last_backup:
            messagebox.showwarning("警告", "没有可恢复的备份文件")
            return
            
        if not os.path.exists(self.last_backup):
            messagebox.showerror("错误", f"备份文件不存在: {self.last_backup}")
            return
            
        target = self.target_file.get()
        if not target:
            messagebox.showerror("错误", "请先选择目标文件")
            return
            
        if not messagebox.askyesno("确认", f"确定要恢复文件吗?\n\n将恢复: {target}"):
            return
            
        try:
            shutil.copy2(self.last_backup, target)
            self.log(f"✅ 恢复成功!")
            self.log(f"   已将 {self.last_backup} 恢复到 {target}")
            
            # 删除备份文件
            os.remove(self.last_backup)
            self.log(f"   已删除备份文件: {self.last_backup}")
            self.last_backup = None
            
            messagebox.showinfo("成功", "文件恢复完成!")
            
        except Exception as e:
            messagebox.showerror("错误", f"恢复失败: {str(e)}")
            self.log(f"❌ 恢复失败: {str(e)}")
            
    def clear_file_selection(self):
        """清空文件选择"""
        self.source_file.set("")
        self.target_file.set("")
        self.log("已清空文件选择")

    def clear_folder_selection(self):
        """清空文件夹选择"""
        self.source_folder.set("")
        self.target_folder.set("")
        self.log("已清空文件夹选择")

    def clear_all_selections(self):
        """清空所有选择"""
        self.clear_file_selection()
        self.clear_folder_selection()
        self.log("已清空所有选择")

    def clear_selection(self):
        """兼容旧方法名"""
        self.clear_all_selections()
        
    def log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.status_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.status_text.see(tk.END)
        self.root.update()

    def refresh_backup_list(self):
        """刷新备份列表"""
        # 清空现有列表
        for item in self.backup_tree.get_children():
            self.backup_tree.delete(item)

        # 重新加载备份历史
        self.load_backup_history()

        # 添加备份记录到列表
        for record in self.backup_history:
            size_mb = record["backup_size"] / (1024 * 1024) if record["backup_size"] > 0 else 0
            size_str = f"{size_mb:.2f} MB" if size_mb > 0 else "未知"

            # 判断备份类型
            backup_file = record["backup_file"]
            if os.path.exists(backup_file):
                backup_type = "📁文件夹" if os.path.isdir(backup_file) else "📄文件"
            else:
                # 根据备份文件名判断（文件夹备份通常没有扩展名）
                backup_type = "📁文件夹" if "." not in os.path.basename(backup_file) else "📄文件"

            self.backup_tree.insert("", tk.END, values=(
                record["timestamp"],
                backup_type,
                os.path.basename(record["source_file"]),
                os.path.basename(record["target_file"]),
                os.path.basename(record["backup_file"]),
                size_str
            ))

    def restore_selected_backup(self):
        """恢复选中的备份"""
        selection = self.backup_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要恢复的备份")
            return

        # 获取选中项的索引
        item = selection[0]
        index = self.backup_tree.index(item)

        if index >= len(self.backup_history):
            messagebox.showerror("错误", "选中的备份记录无效")
            return

        record = self.backup_history[index]
        backup_file = record["backup_file"]
        target_file = record["target_file"]

        if not os.path.exists(backup_file):
            messagebox.showerror("错误", f"备份文件不存在: {backup_file}")
            return

        # 判断备份类型并显示相应的确认信息
        backup_type = "文件夹" if os.path.isdir(backup_file) else "文件"
        confirm_msg = f"确定要恢复此{backup_type}备份吗?\n\n"
        confirm_msg += f"备份类型: {backup_type}\n"
        confirm_msg += f"目标位置: {target_file}\n"
        confirm_msg += f"备份时间: {record['timestamp']}\n\n"

        if backup_type == "文件夹":
            confirm_msg += "⚠️ 注意: 如果目标文件夹存在，将被完全替换！"
        else:
            confirm_msg += "⚠️ 注意: 如果目标文件存在，将被覆盖！"

        if not messagebox.askyesno("确认恢复", confirm_msg):
            return

        try:
            # 智能判断是文件还是文件夹
            if os.path.isdir(backup_file):
                # 文件夹恢复
                self.restore_folder(backup_file, target_file)
            else:
                # 文件恢复
                self.restore_file_item(backup_file, target_file)

            self.log(f"✅ 恢复成功!")
            self.log(f"   已将 {backup_file} 恢复到 {target_file}")
            messagebox.showinfo("成功", "恢复完成!")

        except Exception as e:
            error_msg = f"恢复失败: {str(e)}"
            self.log(f"❌ {error_msg}")

            # 如果是权限问题，提供额外的解决方案
            if "Permission denied" in str(e) or "权限" in str(e):
                detailed_msg = f"{error_msg}\n\n可能的解决方案:\n"
                detailed_msg += "1. 关闭可能占用文件的程序（如文件管理器、编辑器）\n"
                detailed_msg += "2. 以管理员身份运行此工具\n"
                detailed_msg += "3. 手动删除目标文件夹后重试\n"
                detailed_msg += "4. 重启电脑后重试\n\n"
                detailed_msg += "是否要打开备份文件夹位置，手动处理？"

                if messagebox.askyesno("权限错误", detailed_msg):
                    # 打开备份文件夹
                    backup_dir = os.path.dirname(backup_file)
                    try:
                        os.startfile(backup_dir)  # Windows
                    except:
                        try:
                            subprocess.run(['open', backup_dir])  # macOS
                        except:
                            try:
                                subprocess.run(['xdg-open', backup_dir])  # Linux
                            except:
                                messagebox.showinfo("提示", f"请手动打开文件夹: {backup_dir}")
            else:
                messagebox.showerror("错误", error_msg)

    def restore_file_item(self, backup_file, target_file):
        """恢复单个文件"""
        # 确保目标目录存在
        target_dir = os.path.dirname(target_file)
        if target_dir and not os.path.exists(target_dir):
            os.makedirs(target_dir)

        # 复制文件
        shutil.copy2(backup_file, target_file)

    def restore_folder(self, backup_folder, target_folder):
        """恢复文件夹"""
        self.log(f"开始恢复文件夹: {backup_folder} → {target_folder}")

        # 检查备份文件夹是否存在
        if not os.path.exists(backup_folder):
            raise Exception(f"备份文件夹不存在: {backup_folder}")

        if not os.path.isdir(backup_folder):
            raise Exception(f"备份路径不是文件夹: {backup_folder}")

        # 如果目标文件夹存在，先删除
        if os.path.exists(target_folder):
            self.log(f"目标文件夹已存在，正在删除: {target_folder}")
            try:
                # 首先尝试普通删除
                shutil.rmtree(target_folder)
                self.log("目标文件夹删除成功")
            except PermissionError as e:
                self.log(f"权限不足，尝试强制删除: {e}")
                try:
                    # 如果权限不足，尝试修改权限后删除
                    self.force_remove_directory(target_folder)
                    self.log("强制删除成功")
                except Exception as e2:
                    raise Exception(f"无法删除目标文件夹: {target_folder}\n原因: {str(e2)}\n\n请手动删除该文件夹后重试")
            except Exception as e:
                raise Exception(f"删除目标文件夹失败: {str(e)}")

        # 确保目标父目录存在
        target_parent = os.path.dirname(target_folder)
        if target_parent and not os.path.exists(target_parent):
            self.log(f"创建父目录: {target_parent}")
            os.makedirs(target_parent)

        # 复制文件夹
        self.log("开始复制备份文件夹...")
        try:
            shutil.copytree(backup_folder, target_folder)
            self.log("文件夹复制完成")
        except Exception as e:
            raise Exception(f"复制文件夹失败: {str(e)}")

    def force_remove_directory(self, directory):
        """强制删除目录（处理权限问题）"""
        import stat

        def handle_remove_readonly(func, path, exc):
            """处理只读文件的删除"""
            try:
                if os.path.exists(path):
                    # 修改文件/目录权限为可写
                    if os.path.isdir(path):
                        os.chmod(path, stat.S_IWRITE | stat.S_IREAD | stat.S_IEXEC)
                    else:
                        os.chmod(path, stat.S_IWRITE | stat.S_IREAD)
                    # 重试删除
                    func(path)
            except Exception as e:
                print(f"权限处理失败: {path}, 错误: {e}")

        try:
            # 首先尝试递归修改整个目录树的权限
            self.fix_directory_permissions(directory)
            # 使用错误处理函数删除目录
            shutil.rmtree(directory, onerror=handle_remove_readonly)
        except Exception as e:
            # 如果还是失败，尝试使用系统命令
            self.force_delete_with_system_command(directory)

    def fix_directory_permissions(self, directory):
        """递归修复目录权限"""
        import stat

        try:
            for root, dirs, files in os.walk(directory):
                # 修复目录权限
                try:
                    os.chmod(root, stat.S_IWRITE | stat.S_IREAD | stat.S_IEXEC)
                except:
                    pass

                # 修复文件权限
                for file in files:
                    try:
                        file_path = os.path.join(root, file)
                        os.chmod(file_path, stat.S_IWRITE | stat.S_IREAD)
                    except:
                        pass
        except Exception as e:
            print(f"权限修复失败: {e}")

    def force_delete_with_system_command(self, directory):
        """使用系统命令强制删除"""
        import subprocess

        try:
            if os.name == 'nt':  # Windows
                # 使用 rmdir /s /q 命令
                subprocess.run(['rmdir', '/s', '/q', directory],
                             shell=True, check=True, capture_output=True)
            else:  # Linux/Mac
                # 使用 rm -rf 命令
                subprocess.run(['rm', '-rf', directory],
                             check=True, capture_output=True)
        except subprocess.CalledProcessError as e:
            raise Exception(f"系统命令删除失败: {e}")
        except Exception as e:
            raise Exception(f"强制删除失败: {e}")

    def delete_selected_backup(self):
        """删除选中的备份"""
        selection = self.backup_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要删除的备份")
            return

        # 获取选中项的索引
        item = selection[0]
        index = self.backup_tree.index(item)

        if index >= len(self.backup_history):
            messagebox.showerror("错误", "选中的备份记录无效")
            return

        record = self.backup_history[index]
        backup_file = record["backup_file"]

        if not messagebox.askyesno("确认", f"确定要删除此备份吗?\n\n备份文件: {backup_file}\n备份时间: {record['timestamp']}\n\n⚠️ 此操作不可恢复"):
            return

        try:
            # 删除备份文件
            if os.path.exists(backup_file):
                os.remove(backup_file)

            # 从历史记录中移除
            self.backup_history.pop(index)
            self.save_backup_history()

            # 刷新列表
            self.refresh_backup_list()

            self.log(f"✅ 已删除备份: {backup_file}")
            messagebox.showinfo("成功", "备份删除完成!")

        except Exception as e:
            messagebox.showerror("错误", f"删除失败: {str(e)}")
            self.log(f"❌ 删除失败: {str(e)}")

    def clear_all_backups(self):
        """清理所有备份"""
        if not self.backup_history:
            messagebox.showinfo("提示", "没有备份需要清理")
            return

        if not messagebox.askyesno("确认", f"确定要清理所有 {len(self.backup_history)} 个备份吗?\n\n⚠️ 此操作不可恢复"):
            return

        try:
            deleted_count = 0
            for record in self.backup_history:
                backup_file = record["backup_file"]
                if os.path.exists(backup_file):
                    os.remove(backup_file)
                    deleted_count += 1

            # 清空历史记录
            self.backup_history.clear()
            self.save_backup_history()

            # 刷新列表
            self.refresh_backup_list()

            self.log(f"✅ 已清理 {deleted_count} 个备份文件")
            messagebox.showinfo("成功", f"备份清理完成!\n删除了 {deleted_count} 个文件")

        except Exception as e:
            messagebox.showerror("错误", f"清理失败: {str(e)}")
            self.log(f"❌ 清理失败: {str(e)}")

def main():
    root = tk.Tk()
    app = FileCopierGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
