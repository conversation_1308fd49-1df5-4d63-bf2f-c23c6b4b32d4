@echo off
cd /d "%~dp0"

echo ========================================
echo    Portable File Copier Tool v2.0
echo ========================================
echo.
echo Starting tool...
echo.

REM Try different Python commands
python file_copier.py
if %errorlevel% neq 0 (
    echo Python command failed, trying py...
    py file_copier.py
    if %errorlevel% neq 0 (
        echo py command failed, trying python3...
        python3 file_copier.py
        if %errorlevel% neq 0 (
            echo.
            echo ERROR: Python not found or failed to start!
            echo Please make sure Python is installed and in PATH.
            echo.
            echo You can also run the tool directly:
            echo   python file_copier.py
            echo.
        )
    )
)

echo.
echo Tool closed.
pause
