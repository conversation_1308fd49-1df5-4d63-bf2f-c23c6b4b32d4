import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
import webbrowser
import json
import subprocess
import sys

# 现代浅蓝色主题配色方案
THEME_COLORS = {
    'primary': '#E3F2FD',      # 浅蓝色背景
    'secondary': '#BBDEFB',     # 中等蓝色
    'accent': '#2196F3',        # 强调蓝色
    'dark_accent': '#1976D2',   # 深蓝色
    'text': '#263238',          # 深灰色文字
    'text_light': '#546E7A',    # 浅灰色文字
    'white': '#FFFFFF',         # 白色
    'success': '#4CAF50',       # 成功绿色
    'warning': '#FF9800',       # 警告橙色
    'error': '#F44336',         # 错误红色
    'border': '#CFD8DC',        # 边框颜色
    'hover': '#E1F5FE'          # 悬停颜色
}

class PathManager:
    def __init__(self):
        print("正在初始化路径管理器...")
        self.root = tk.Tk()
        self.root.title("路径管理器 - Path Manager")
        self.root.geometry("700x550")
        self.root.resizable(True, True)
        self.root.minsize(600, 450)

        # 设置窗口图标和样式
        self.setup_window_style()

        # 确保窗口显示在前台
        self.root.lift()
        self.root.attributes('-topmost', True)
        self.root.after_idle(lambda: self.root.attributes('-topmost', False))

        print("窗口创建完成")
        
        # 配置文件路径
        self.config_file = "path_config.json"
        
        # 默认路径配置
        self.default_paths = [
            {"name": "桌面", "path": os.path.expanduser("~/Desktop"), "type": "folder"},
            {"name": "文档", "path": os.path.expanduser("~/Documents"), "type": "folder"},
            {"name": "下载", "path": os.path.expanduser("~/Downloads"), "type": "folder"},
            {"name": "百度网盘", "path": "https://pan.baidu.com", "type": "url"}
        ]
        
        # 加载配置
        self.paths = self.load_config()
        
        # 创建界面
        self.create_widgets()

    def setup_window_style(self):
        """设置窗口样式和主题"""
        # 设置窗口背景色
        self.root.configure(bg=THEME_COLORS['primary'])

        # 配置ttk样式
        self.style = ttk.Style()

        # 配置按钮样式
        self.style.configure('Modern.TButton',
                           background=THEME_COLORS['accent'],
                           foreground=THEME_COLORS['white'],
                           borderwidth=0,
                           focuscolor='none',
                           padding=(15, 8))

        self.style.map('Modern.TButton',
                      background=[('active', THEME_COLORS['dark_accent']),
                                ('pressed', THEME_COLORS['dark_accent'])])

        # 配置标签框样式
        self.style.configure('Modern.TLabelframe',
                           background=THEME_COLORS['white'],
                           borderwidth=1,
                           relief='solid')

        self.style.configure('Modern.TLabelframe.Label',
                           background=THEME_COLORS['white'],
                           foreground=THEME_COLORS['text'],
                           font=('Segoe UI', 10, 'bold'))

        # 配置Treeview样式
        self.style.configure('Modern.Treeview',
                           background=THEME_COLORS['white'],
                           foreground=THEME_COLORS['text'],
                           fieldbackground=THEME_COLORS['white'],
                           borderwidth=1,
                           relief='solid')

        self.style.configure('Modern.Treeview.Heading',
                           background=THEME_COLORS['secondary'],
                           foreground=THEME_COLORS['text'],
                           font=('Segoe UI', 9, 'bold'),
                           borderwidth=1,
                           relief='solid')

        # 配置Entry样式
        self.style.configure('Modern.TEntry',
                           fieldbackground=THEME_COLORS['white'],
                           borderwidth=1,
                           relief='solid',
                           padding=5)

        # 配置Combobox样式
        self.style.configure('Modern.TCombobox',
                           fieldbackground=THEME_COLORS['white'],
                           borderwidth=1,
                           relief='solid')
        
    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                return self.default_paths.copy()
        except Exception as e:
            messagebox.showerror("错误", f"加载配置失败: {e}")
            return self.default_paths.copy()
    
    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.paths, f, ensure_ascii=False, indent=2)
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {e}")
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = tk.Frame(self.root, bg=THEME_COLORS['primary'], padx=20, pady=20)
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 创建标题区域
        self.create_header(main_frame)

        # 创建路径列表区域
        self.create_path_list(main_frame)

        # 创建按钮区域
        self.create_buttons(main_frame)

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)

    def create_header(self, parent):
        """创建标题区域"""
        header_frame = tk.Frame(parent, bg=THEME_COLORS['primary'])
        header_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 25))
        header_frame.columnconfigure(0, weight=1)

        # 主标题
        title_label = tk.Label(header_frame,
                              text="🚀 路径管理器",
                              font=("Segoe UI", 18, "bold"),
                              fg=THEME_COLORS['text'],
                              bg=THEME_COLORS['primary'])
        title_label.grid(row=0, column=0)

        # 副标题
        subtitle_label = tk.Label(header_frame,
                                 text="快速访问您的常用文件夹和网址",
                                 font=("Segoe UI", 10),
                                 fg=THEME_COLORS['text_light'],
                                 bg=THEME_COLORS['primary'])
        subtitle_label.grid(row=1, column=0, pady=(5, 0))

    def create_path_list(self, parent):
        """创建路径列表区域"""
        # 路径列表框架
        list_frame = ttk.LabelFrame(parent, text="📁 常用路径",
                                   style='Modern.TLabelframe', padding="15")
        list_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 20))
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)
        
        # 创建Treeview容器
        tree_container = tk.Frame(list_frame, bg=THEME_COLORS['white'], relief='solid', bd=1)
        tree_container.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        tree_container.columnconfigure(0, weight=1)
        tree_container.rowconfigure(0, weight=1)

        # 创建Treeview来显示路径
        columns = ("name", "path", "type")
        self.tree = ttk.Treeview(tree_container, columns=columns, show="headings",
                                height=12, style='Modern.Treeview')

        # 设置列标题
        self.tree.heading("name", text="📝 名称")
        self.tree.heading("path", text="📍 路径/网址")
        self.tree.heading("type", text="🏷️ 类型")

        # 设置列宽和对齐
        self.tree.column("name", width=120, anchor='w')
        self.tree.column("path", width=350, anchor='w')
        self.tree.column("type", width=100, anchor='center')

        # 滚动条
        scrollbar = ttk.Scrollbar(tree_container, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)

        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=1, pady=1)
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # 绑定双击事件和选择事件
        self.tree.bind("<Double-1>", lambda e: self.open_selected())
        self.tree.bind("<<TreeviewSelect>>", self.on_item_select)

        # 配置行颜色交替
        self.tree.tag_configure('evenrow', background=THEME_COLORS['primary'])
        self.tree.tag_configure('oddrow', background=THEME_COLORS['white'])
        
    def create_buttons(self, parent):
        """创建按钮区域"""
        # 主按钮框架
        button_frame = tk.Frame(parent, bg=THEME_COLORS['primary'])
        button_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        button_frame.columnconfigure((0, 1, 2, 3), weight=1)

        # 操作按钮
        buttons = [
            ("🚀 打开", self.open_selected, THEME_COLORS['success']),
            ("➕ 添加", self.add_path, THEME_COLORS['accent']),
            ("✏️ 编辑", self.edit_path, THEME_COLORS['warning']),
            ("🗑️ 删除", self.delete_path, THEME_COLORS['error'])
        ]

        for i, (text, command, color) in enumerate(buttons):
            btn = tk.Button(button_frame,
                           text=text,
                           command=command,
                           bg=color,
                           fg=THEME_COLORS['white'],
                           font=("Segoe UI", 10, "bold"),
                           relief='flat',
                           borderwidth=0,
                           padx=20,
                           pady=10,
                           cursor='hand2')
            btn.grid(row=0, column=i, padx=8, sticky=(tk.W, tk.E))

            # 添加悬停效果
            self.add_hover_effect(btn, color)

        # 分隔线
        separator = tk.Frame(parent, height=2, bg=THEME_COLORS['border'])
        separator.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=15)

        # 底部按钮框架
        bottom_frame = tk.Frame(parent, bg=THEME_COLORS['primary'])
        bottom_frame.grid(row=4, column=0, sticky=(tk.W, tk.E))
        bottom_frame.columnconfigure(0, weight=1)

        # 关闭按钮
        close_btn = tk.Button(bottom_frame,
                             text="❌ 关闭程序",
                             command=self.root.quit,
                             bg=THEME_COLORS['text_light'],
                             fg=THEME_COLORS['white'],
                             font=("Segoe UI", 10),
                             relief='flat',
                             borderwidth=0,
                             padx=30,
                             pady=8,
                             cursor='hand2')
        close_btn.grid(row=0, column=0)
        self.add_hover_effect(close_btn, THEME_COLORS['text_light'])

        # 刷新列表
        self.refresh_list()

    def add_hover_effect(self, button, original_color):
        """为按钮添加悬停效果"""
        def on_enter(e):
            # 计算悬停时的颜色（稍微变暗）
            button.configure(bg=self.darken_color(original_color))

        def on_leave(e):
            button.configure(bg=original_color)

        button.bind("<Enter>", on_enter)
        button.bind("<Leave>", on_leave)

    def darken_color(self, color):
        """将颜色变暗一些"""
        # 简单的颜色变暗算法
        if color == THEME_COLORS['success']:
            return '#45A049'
        elif color == THEME_COLORS['accent']:
            return '#1976D2'
        elif color == THEME_COLORS['warning']:
            return '#F57C00'
        elif color == THEME_COLORS['error']:
            return '#D32F2F'
        else:
            return '#455A64'

    def on_item_select(self, event):
        """处理列表项选择事件"""
        selection = self.tree.selection()
        if selection:
            # 可以在这里添加选择项的预览或其他功能
            pass
    
    def refresh_list(self):
        """刷新路径列表"""
        # 清空现有项目
        for item in self.tree.get_children():
            self.tree.delete(item)

        # 添加路径项目
        for i, path_info in enumerate(self.paths):
            type_text = "📁 文件夹" if path_info["type"] == "folder" else "🌐 网址"
            # 交替行颜色
            tag = 'evenrow' if i % 2 == 0 else 'oddrow'
            self.tree.insert("", tk.END,
                           values=(path_info["name"], path_info["path"], type_text),
                           tags=(tag,))
    
    def open_selected(self):
        """打开选中的路径"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个路径")
            return
        
        item = self.tree.item(selection[0])
        values = item["values"]
        path = values[1]
        path_type = "folder" if "文件夹" in values[2] else "url"
        
        try:
            if path_type == "folder":
                # 打开文件夹
                if os.path.exists(path):
                    if sys.platform == "win32":
                        os.startfile(path)
                    elif sys.platform == "darwin":
                        subprocess.run(["open", path])
                    else:
                        subprocess.run(["xdg-open", path])
                else:
                    messagebox.showerror("错误", f"路径不存在: {path}")
            else:
                # 打开网址
                webbrowser.open(path)
                
        except Exception as e:
            messagebox.showerror("错误", f"打开失败: {e}")
    
    def add_path(self):
        """添加新路径"""
        self.edit_path_dialog()
    
    def edit_path(self):
        """编辑选中的路径"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个路径")
            return
        
        item = self.tree.item(selection[0])
        values = item["values"]
        index = self.tree.index(selection[0])
        
        path_info = self.paths[index]
        self.edit_path_dialog(path_info, index)
    
    def edit_path_dialog(self, path_info=None, index=None):
        """路径编辑对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title("✏️ 编辑路径" if path_info else "➕ 添加路径")
        dialog.geometry("500x350")
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()
        dialog.configure(bg=THEME_COLORS['primary'])

        # 居中显示
        dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 100, self.root.winfo_rooty() + 100))

        # 主框架
        main_frame = tk.Frame(dialog, bg=THEME_COLORS['primary'], padx=30, pady=25)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_text = "编辑路径信息" if path_info else "添加新路径"
        title_label = tk.Label(main_frame,
                              text=title_text,
                              font=("Segoe UI", 14, "bold"),
                              fg=THEME_COLORS['text'],
                              bg=THEME_COLORS['primary'])
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 名称输入
        name_label = tk.Label(main_frame,
                             text="📝 显示名称:",
                             font=("Segoe UI", 10, "bold"),
                             fg=THEME_COLORS['text'],
                             bg=THEME_COLORS['primary'])
        name_label.grid(row=1, column=0, sticky=tk.W, pady=(10, 5))

        name_var = tk.StringVar(value=path_info["name"] if path_info else "")
        name_entry = tk.Entry(main_frame,
                             textvariable=name_var,
                             font=("Segoe UI", 10),
                             bg=THEME_COLORS['white'],
                             fg=THEME_COLORS['text'],
                             relief='solid',
                             bd=1,
                             width=40)
        name_entry.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 15))

        # 路径输入
        path_label = tk.Label(main_frame,
                             text="📍 路径/网址:",
                             font=("Segoe UI", 10, "bold"),
                             fg=THEME_COLORS['text'],
                             bg=THEME_COLORS['primary'])
        path_label.grid(row=3, column=0, sticky=tk.W, pady=(0, 5))

        path_var = tk.StringVar(value=path_info["path"] if path_info else "")
        path_entry = tk.Entry(main_frame,
                             textvariable=path_var,
                             font=("Segoe UI", 10),
                             bg=THEME_COLORS['white'],
                             fg=THEME_COLORS['text'],
                             relief='solid',
                             bd=1,
                             width=35)
        path_entry.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))
        
        # 浏览按钮
        def browse_folder():
            folder = filedialog.askdirectory()
            if folder:
                path_var.set(folder)
                type_var.set("folder")

        browse_btn = tk.Button(main_frame,
                              text="📁 浏览",
                              command=browse_folder,
                              bg=THEME_COLORS['secondary'],
                              fg=THEME_COLORS['text'],
                              font=("Segoe UI", 9),
                              relief='flat',
                              bd=0,
                              padx=15,
                              pady=5,
                              cursor='hand2')
        browse_btn.grid(row=4, column=2, padx=(10, 0), pady=(0, 15))
        self.add_hover_effect(browse_btn, THEME_COLORS['secondary'])

        # 类型选择
        type_label = tk.Label(main_frame,
                             text="🏷️ 类型:",
                             font=("Segoe UI", 10, "bold"),
                             fg=THEME_COLORS['text'],
                             bg=THEME_COLORS['primary'])
        type_label.grid(row=5, column=0, sticky=tk.W, pady=(0, 5))

        type_var = tk.StringVar(value=path_info["type"] if path_info else "folder")

        # 类型选择框架
        type_frame = tk.Frame(main_frame, bg=THEME_COLORS['primary'])
        type_frame.grid(row=6, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 20))

        # 单选按钮
        folder_radio = tk.Radiobutton(type_frame,
                                     text="📁 文件夹",
                                     variable=type_var,
                                     value="folder",
                                     bg=THEME_COLORS['primary'],
                                     fg=THEME_COLORS['text'],
                                     font=("Segoe UI", 10),
                                     selectcolor=THEME_COLORS['accent'])
        folder_radio.grid(row=0, column=0, padx=(0, 20))

        url_radio = tk.Radiobutton(type_frame,
                                  text="🌐 网址",
                                  variable=type_var,
                                  value="url",
                                  bg=THEME_COLORS['primary'],
                                  fg=THEME_COLORS['text'],
                                  font=("Segoe UI", 10),
                                  selectcolor=THEME_COLORS['accent'])
        url_radio.grid(row=0, column=1)
        
        # 按钮区域
        button_frame = tk.Frame(main_frame, bg=THEME_COLORS['primary'])
        button_frame.grid(row=7, column=0, columnspan=3, pady=20)
        button_frame.columnconfigure((0, 1), weight=1)

        def save_path():
            name = name_var.get().strip()
            path = path_var.get().strip()
            path_type = type_var.get()

            if not name or not path:
                messagebox.showerror("❌ 错误", "名称和路径不能为空！")
                return

            new_path_info = {"name": name, "path": path, "type": path_type}

            if index is not None:
                self.paths[index] = new_path_info
            else:
                self.paths.append(new_path_info)

            self.save_config()
            self.refresh_list()
            dialog.destroy()

        # 保存按钮
        save_btn = tk.Button(button_frame,
                            text="💾 保存",
                            command=save_path,
                            bg=THEME_COLORS['success'],
                            fg=THEME_COLORS['white'],
                            font=("Segoe UI", 10, "bold"),
                            relief='flat',
                            bd=0,
                            padx=25,
                            pady=8,
                            cursor='hand2')
        save_btn.grid(row=0, column=0, padx=10)
        self.add_hover_effect(save_btn, THEME_COLORS['success'])

        # 取消按钮
        cancel_btn = tk.Button(button_frame,
                              text="❌ 取消",
                              command=dialog.destroy,
                              bg=THEME_COLORS['text_light'],
                              fg=THEME_COLORS['white'],
                              font=("Segoe UI", 10),
                              relief='flat',
                              bd=0,
                              padx=25,
                              pady=8,
                              cursor='hand2')
        cancel_btn.grid(row=0, column=1, padx=10)
        self.add_hover_effect(cancel_btn, THEME_COLORS['text_light'])

        # 配置网格权重
        main_frame.columnconfigure(0, weight=1)
        name_entry.focus()
    
    def delete_path(self):
        """删除选中的路径"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个路径")
            return
        
        if messagebox.askyesno("确认", "确定要删除选中的路径吗？"):
            index = self.tree.index(selection[0])
            del self.paths[index]
            self.save_config()
            self.refresh_list()
    
    def run(self):
        """运行程序"""
        print("启动主循环...")
        try:
            self.root.mainloop()
            print("程序正常退出")
        except Exception as e:
            print(f"程序运行错误: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    print("开始启动路径管理器...")
    try:
        app = PathManager()
        app.run()
    except Exception as e:
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")
