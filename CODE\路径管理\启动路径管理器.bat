@echo off
title Path Manager Launcher

echo ================================
echo      Path Manager Launcher
echo ================================
echo.

echo Current Directory: %CD%
echo.

echo Checking Python...
python --version
if errorlevel 1 (
    echo.
    echo ERROR: Python not found
    echo Please install Python first
    echo.
    pause
    exit /b 1
)

echo Python OK!
echo.

if not exist "path_manager.py" (
    echo ERROR: path_manager.py not found
    echo.
    pause
    exit /b 1
)

echo Starting Path Manager...
echo.

python path_manager.py
set exit_code=%errorlevel%

echo.
echo ================================
if %exit_code% equ 0 (
    echo Program exited normally
) else (
    echo ERROR: Program crashed with code: %exit_code%
)
echo ================================
echo.
echo Press any key to exit...
pause >nul
