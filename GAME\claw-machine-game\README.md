# 🎮 Labubu抓娃娃机游戏

一个支持自定义图片的可爱Labubu抓娃娃机游戏！

## 🎯 游戏特色

- 🎨 **自定义图片支持** - 可以使用真实的Labubu图片
- 🌈 **8种颜色Labubu** - 粉色、蓝色、绿色、黄色、紫色、橙色、红色、青色
- ✨ **精美动画效果** - 浮动动画、粒子效果、发光效果
- 🎪 **真实抓娃娃机体验** - 抓取→运送→获得的完整流程
- 📱 **响应式设计** - 支持不同屏幕尺寸

## 🎮 游戏操作

- **← → 方向键**: 移动抓手
- **空格键**: 下降抓取
- **R键**: 重新开始游戏

## 🖼️ 自定义图片说明

### 当前图片

游戏包含8个预制的SVG格式Labubu图片：
- `labubu_pink.svg` - 粉色Labubu
- `labubu_blue.svg` - 蓝色Labubu  
- `labubu_green.svg` - 绿色Labubu
- `labubu_yellow.svg` - 黄色Labubu
- `labubu_purple.svg` - 紫色Labubu
- `labubu_orange.svg` - 橙色Labubu
- `labubu_red.svg` - 红色Labubu
- `labubu_cyan.svg` - 青色Labubu

### 如何添加自定义图片

1. **准备图片文件**
   - 支持格式：PNG, JPG, SVG
   - 推荐尺寸：100x100像素或更大
   - 建议使用透明背景的PNG文件

2. **替换图片**
   - 将你的Labubu图片放入 `images/` 文件夹
   - 使用相同的文件名替换现有图片
   - 例如：用你的粉色Labubu图片替换 `labubu_pink.svg`

3. **添加新的Labubu类型**
   - 在 `images/` 文件夹中添加新图片
   - 修改 `game.js` 中的 `loadImages()` 和 `initDolls()` 方法
   - 添加新的图片文件名和对应的颜色信息

### 图片要求

- **尺寸**: 建议100x100像素或更大（游戏中会自动缩放到60x60像素）
- **格式**: PNG（推荐，支持透明）、JPG、SVG
- **背景**: 透明背景效果最佳
- **内容**: 可爱的Labubu角色，清晰可辨

### 示例：添加新Labubu

1. 准备一张名为 `labubu_rainbow.png` 的彩虹色Labubu图片
2. 将图片放入 `images/` 文件夹
3. 修改 `game.js`：

```javascript
// 在 loadImages() 方法中添加
'labubu_rainbow.png',

// 在 initDolls() 方法中添加
{ image: 'labubu_rainbow.png', emoji: '🌈', color: '#FF69B4', name: '彩虹Labubu' },
```

## 🎨 SVG图片优势

游戏默认使用SVG格式的Labubu图片，具有以下优势：
- **矢量图形** - 无论如何缩放都保持清晰
- **文件小** - 加载速度快
- **可编辑** - 可以用文本编辑器修改颜色和形状
- **兼容性好** - 所有现代浏览器都支持

## 🚀 运行游戏

1. 确保所有文件在同一文件夹中
2. 用浏览器打开 `index.html`
3. 点击"开始游戏"按钮
4. 享受抓Labubu的乐趣！

## 🎯 游戏目标

- 在30秒内抓取尽可能多的Labubu
- 每抓到一个Labubu获得10分
- 抓完所有Labubu额外获得50分奖励
- 挑战你的最高分！

## 🛠️ 技术说明

- **HTML5 Canvas** - 游戏渲染
- **JavaScript** - 游戏逻辑
- **CSS3** - 界面样式和动画
- **SVG** - 矢量图形支持

---

🎮 **祝你游戏愉快，收集到所有可爱的Labubu！** ✨
