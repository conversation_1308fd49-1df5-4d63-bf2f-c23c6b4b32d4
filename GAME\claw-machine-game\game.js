class ClawMachineGame {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.gameState = 'loading'; // loading, waiting, playing, paused, gameOver

        // 游戏数据
        this.score = 0;
        this.coins = 10;
        this.timeLeft = 30;
        this.gameTimer = null;

        // 图片资源
        this.images = {};
        this.imagesLoaded = 0;
        this.totalImages = 0;
        
        // 抓手相关
        this.claw = {
            x: 400,
            y: 50,
            width: 60,
            height: 40,
            isMoving: false,
            isGrabbing: false,
            speed: 3,
            grabSpeed: 2,
            originalY: 50,
            maxY: 450
        };
        
        // 娃娃数组
        this.dolls = [];
        this.initDolls();
        
        // 抓到的娃娃
        this.grabbedDoll = null;
        this.deliveryState = 'none'; // none, delivering, delivered

        // 按键状态
        this.keys = {};

        // 动画效果
        this.particles = [];

        this.initEventListeners();
        this.loadImages();
        this.gameLoop();
    }
    
    loadImages() {
        // Labubu图片文件列表
        const imageFiles = [
            'labubu_pink.svg',
            'labubu_blue.svg',
            'labubu_green.svg',
            'labubu_yellow.svg',
            'labubu_purple.svg',
            'labubu_orange.svg',
            'labubu_red.svg',
            'labubu_cyan.svg'
        ];

        this.totalImages = imageFiles.length;
        this.updateMessage('正在加载Labubu图片...');

        imageFiles.forEach((filename, index) => {
            const img = new Image();
            img.onload = () => {
                this.imagesLoaded++;
                if (this.imagesLoaded === this.totalImages) {
                    this.gameState = 'waiting';
                    this.initDolls();
                    this.updateMessage('准备好收集可爱的Labubu了吗？按开始游戏按钮！👹✨');
                }
            };
            img.onerror = () => {
                console.warn(`无法加载图片: ${filename}, 使用备用emoji`);
                this.imagesLoaded++;
                if (this.imagesLoaded === this.totalImages) {
                    this.gameState = 'waiting';
                    this.initDolls();
                    this.updateMessage('准备好收集可爱的Labubu了吗？按开始游戏按钮！👹✨');
                }
            };
            img.src = `images/${filename}`;
            this.images[filename] = img;
        });
    }

    initDolls() {
        // Labubu玩偶的不同类型
        const labubuTypes = [
            { image: 'labubu_pink.svg', emoji: '👹', color: '#FF6B9D', name: '粉色Labubu' },
            { image: 'labubu_blue.svg', emoji: '👺', color: '#45B7D1', name: '蓝色Labubu' },
            { image: 'labubu_green.svg', emoji: '🤡', color: '#96CEB4', name: '绿色Labubu' },
            { image: 'labubu_yellow.svg', emoji: '👻', color: '#FFEAA7', name: '黄色Labubu' },
            { image: 'labubu_purple.svg', emoji: '🎪', color: '#DDA0DD', name: '紫色Labubu' },
            { image: 'labubu_orange.svg', emoji: '🎨', color: '#FFB347', name: '橙色Labubu' },
            { image: 'labubu_red.svg', emoji: '🎯', color: '#FF7675', name: '红色Labubu' },
            { image: 'labubu_cyan.svg', emoji: '🎭', color: '#4ECDC4', name: '青色Labubu' }
        ];

        this.dolls = [];

        // 创建更容易抓到的娃娃分布
        for (let i = 0; i < 12; i++) {
            const labubu = labubuTypes[Math.floor(Math.random() * labubuTypes.length)];

            // 调整位置让娃娃更容易抓到
            const x = 120 + (i % 4) * 140 + Math.random() * 60; // 4列分布
            const y = 400 + Math.floor(i / 4) * 50 + Math.random() * 30; // 3行分布

            this.dolls.push({
                x: x,
                y: y,
                width: 60,
                height: 60,
                image: labubu.image,
                emoji: labubu.emoji,
                color: labubu.color,
                name: labubu.name,
                grabbed: false,
                physics: {
                    vx: 0,
                    vy: 0,
                    gravity: 0.3
                },
                // 添加一些动画效果
                bobOffset: Math.random() * Math.PI * 2,
                bobSpeed: 0.02 + Math.random() * 0.02
            });
        }
    }
    
    initEventListeners() {
        // 键盘事件
        document.addEventListener('keydown', (e) => {
            this.keys[e.code] = true;
            
            if (e.code === 'Space') {
                e.preventDefault();
                this.startGrab();
            }
            
            if (e.code === 'KeyR') {
                this.restartGame();
            }
        });
        
        document.addEventListener('keyup', (e) => {
            this.keys[e.code] = false;
        });
        
        // 按钮事件
        document.getElementById('startBtn').addEventListener('click', () => this.startGame());
        document.getElementById('pauseBtn').addEventListener('click', () => this.togglePause());
        document.getElementById('restartBtn').addEventListener('click', () => this.restartGame());
    }
    
    startGame() {
        if (this.coins <= 0) {
            this.updateMessage('游戏币不足！');
            return;
        }
        
        this.gameState = 'playing';
        this.coins--;
        this.timeLeft = 30;
        this.updateUI();
        this.updateMessage('快来抓可爱的Labubu玩偶吧！🎯');
        
        // 开始计时器
        this.gameTimer = setInterval(() => {
            this.timeLeft--;
            this.updateUI();
            
            if (this.timeLeft <= 0) {
                this.endGame();
            }
        }, 1000);
    }
    
    togglePause() {
        if (this.gameState === 'playing') {
            this.gameState = 'paused';
            clearInterval(this.gameTimer);
            this.updateMessage('游戏已暂停');
        } else if (this.gameState === 'paused') {
            this.gameState = 'playing';
            this.gameTimer = setInterval(() => {
                this.timeLeft--;
                this.updateUI();
                
                if (this.timeLeft <= 0) {
                    this.endGame();
                }
            }, 1000);
            this.updateMessage('游戏继续！');
        }
    }
    
    restartGame() {
        this.gameState = 'waiting';
        this.score = 0;
        this.coins = 10;
        this.timeLeft = 30;
        this.claw.x = 400;
        this.claw.y = this.claw.originalY;
        this.claw.isMoving = false;
        this.claw.isGrabbing = false;
        this.grabbedDoll = null;
        this.deliveryState = 'none';
        this.particles = [];

        clearInterval(this.gameTimer);
        this.initDolls();
        this.updateUI();
        this.updateMessage('按开始游戏按钮开始！');
    }
    
    endGame() {
        this.gameState = 'gameOver';
        clearInterval(this.gameTimer);
        this.updateMessage(`游戏结束！最终得分: ${this.score}`);
    }
    
    startGrab() {
        if (this.gameState !== 'playing' || this.claw.isMoving) return;
        
        this.claw.isMoving = true;
        this.claw.isGrabbing = true;
    }
    
    update() {
        if (this.gameState !== 'playing') return;
        
        // 更新抓手移动
        if (!this.claw.isMoving) {
            if (this.keys['ArrowLeft'] && this.claw.x > 50) {
                this.claw.x -= this.claw.speed;
            }
            if (this.keys['ArrowRight'] && this.claw.x < 750) {
                this.claw.x += this.claw.speed;
            }
        }
        
        // 更新抓手抓取动作
        if (this.claw.isMoving) {
            if (this.claw.isGrabbing && this.claw.y < this.claw.maxY) {
                // 下降
                this.claw.y += this.claw.grabSpeed;

                // 检查是否抓到娃娃
                if (this.claw.y >= this.claw.maxY - 50) {
                    this.checkGrab();
                    this.claw.isGrabbing = false;
                }
            } else {
                // 上升
                this.claw.y -= this.claw.grabSpeed;

                if (this.claw.y <= this.claw.originalY) {
                    this.claw.y = this.claw.originalY;
                    this.claw.isMoving = false;

                    // 检查是否成功抓到娃娃
                    if (this.grabbedDoll) {
                        this.deliveryState = 'delivering';
                        this.updateMessage('正在运送Labubu到出口...🚚');
                    }
                }
            }
        }

        // 处理娃娃运送到出口的过程
        if (this.deliveryState === 'delivering' && this.grabbedDoll) {
            // 将娃娃移动到出口
            const targetX = 400; // 出口中心
            const targetY = 500; // 出口位置

            const dx = targetX - this.grabbedDoll.x;
            const dy = targetY - this.grabbedDoll.y;

            if (Math.abs(dx) > 2 || Math.abs(dy) > 2) {
                this.grabbedDoll.x += dx * 0.1;
                this.grabbedDoll.y += dy * 0.1;
            } else {
                // 到达出口，完成抓取
                this.deliverDoll();
            }
        }
        
        // 更新被抓娃娃的位置（只在抓手移动时）
        if (this.grabbedDoll && this.deliveryState === 'none') {
            this.grabbedDoll.x = this.claw.x;
            this.grabbedDoll.y = this.claw.y + 30;
        }

        // 更新粒子效果
        this.updateParticles();

        // 更新Labubu娃娃的动画
        this.updateDollAnimations();
    }
    
    checkGrab() {
        for (let doll of this.dolls) {
            const distance = Math.sqrt(
                Math.pow(this.claw.x - doll.x, 2) +
                Math.pow(this.claw.y + 20 - doll.y, 2)
            );

            if (distance < 40 && !doll.grabbed) {
                // 抓取成功概率（添加一些随机性）
                if (Math.random() < 0.7) {
                    this.grabbedDoll = doll;
                    doll.grabbed = true;
                    this.updateMessage(`抓到了${doll.name}！🎉`);
                    // 添加抓取成功的粒子效果
                    this.createParticles(doll.x, doll.y, '✨');
                    break;
                } else {
                    // 抓取失败的提示
                    this.updateMessage('Labubu太调皮了，差一点就抓到了！😅');
                    this.createParticles(this.claw.x, this.claw.y, '💨');
                }
            }
        }
    }

    deliverDoll() {
        if (this.grabbedDoll) {
            // 成功运送娃娃
            this.score += 10;
            this.updateUI();
            this.updateMessage(`成功获得${this.grabbedDoll.name}！+10分 🎊`);

            // 创建庆祝粒子效果
            this.createParticles(400, 500, '🎉');

            // 移除被抓到的娃娃
            this.dolls = this.dolls.filter(doll => doll !== this.grabbedDoll);
            this.grabbedDoll = null;
            this.deliveryState = 'none';

            // 如果抓完了所有娃娃
            if (this.dolls.length === 0) {
                this.score += 50;
                this.updateMessage('太棒了！收集了所有Labubu！+50分 🏆');
                this.createParticles(400, 300, '🏆');
                this.initDolls();
            }
        }
    }

    createParticles(x, y, emoji) {
        for (let i = 0; i < 8; i++) {
            this.particles.push({
                x: x,
                y: y,
                vx: (Math.random() - 0.5) * 4,
                vy: (Math.random() - 0.5) * 4 - 2,
                emoji: emoji,
                life: 60,
                maxLife: 60
            });
        }
    }

    updateParticles() {
        for (let i = this.particles.length - 1; i >= 0; i--) {
            const particle = this.particles[i];
            particle.x += particle.vx;
            particle.y += particle.vy;
            particle.vy += 0.1; // 重力
            particle.life--;

            if (particle.life <= 0) {
                this.particles.splice(i, 1);
            }
        }
    }

    updateDollAnimations() {
        // 让Labubu娃娃有轻微的上下浮动效果
        for (let doll of this.dolls) {
            if (!doll.grabbed) {
                doll.bobOffset += doll.bobSpeed;
            }
        }
    }

    draw() {
        // 清空画布
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        // 绘制背景
        this.drawBackground();

        // 绘制娃娃机框架
        this.drawMachineFrame();

        // 绘制娃娃
        this.drawDolls();

        // 绘制抓手
        this.drawClaw();

        // 绘制抓手线条
        this.drawClawLine();

        // 绘制粒子效果
        this.drawParticles();
    }

    drawBackground() {
        // 绘制渐变背景
        const gradient = this.ctx.createLinearGradient(0, 0, 0, this.canvas.height);
        gradient.addColorStop(0, '#87CEEB');
        gradient.addColorStop(1, '#98FB98');
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
    }

    drawMachineFrame() {
        // 绘制抓娃娃机的框架
        this.ctx.strokeStyle = '#8B4513';
        this.ctx.lineWidth = 8;

        // 顶部横梁
        this.ctx.beginPath();
        this.ctx.moveTo(50, 30);
        this.ctx.lineTo(750, 30);
        this.ctx.stroke();

        // 左右支柱
        this.ctx.beginPath();
        this.ctx.moveTo(50, 30);
        this.ctx.lineTo(50, 500);
        this.ctx.stroke();

        this.ctx.beginPath();
        this.ctx.moveTo(750, 30);
        this.ctx.lineTo(750, 500);
        this.ctx.stroke();

        // 底部
        this.ctx.beginPath();
        this.ctx.moveTo(50, 500);
        this.ctx.lineTo(750, 500);
        this.ctx.stroke();

        // 出口
        this.ctx.fillStyle = '#FFD700';
        this.ctx.fillRect(350, 500, 100, 30);
        this.ctx.fillStyle = '#000';
        this.ctx.font = '16px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText('出口', 400, 520);
    }

    drawDolls() {
        this.ctx.textAlign = 'center';

        for (let doll of this.dolls) {
            // 计算浮动效果
            const bobY = doll.y + Math.sin(doll.bobOffset) * 3;

            // 绘制底座阴影
            this.ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
            this.ctx.beginPath();
            this.ctx.ellipse(doll.x + 2, doll.y + 35, 25, 8, 0, 0, 2 * Math.PI);
            this.ctx.fill();

            // 绘制Labubu的底座
            this.ctx.fillStyle = doll.color;
            this.ctx.beginPath();
            this.ctx.ellipse(doll.x, bobY + 30, 25, 10, 0, 0, 2 * Math.PI);
            this.ctx.fill();

            // 尝试绘制图片，如果失败则使用emoji
            const img = this.images[doll.image];
            if (img && img.complete && img.naturalHeight !== 0) {
                // 绘制图片阴影
                this.ctx.globalAlpha = 0.3;
                this.ctx.drawImage(img, doll.x - doll.width/2 + 2, bobY - doll.height/2 + 2, doll.width, doll.height);
                this.ctx.globalAlpha = 1;

                // 绘制Labubu图片
                this.ctx.drawImage(img, doll.x - doll.width/2, bobY - doll.height/2, doll.width, doll.height);
            } else {
                // 备用emoji绘制
                this.ctx.font = '40px Arial';
                this.ctx.fillStyle = 'rgba(0, 0, 0, 0.2)';
                this.ctx.fillText(doll.emoji, doll.x + 2, bobY + 2);
                this.ctx.fillStyle = '#000';
                this.ctx.fillText(doll.emoji, doll.x, bobY);
            }

            // 绘制名字标签
            this.ctx.font = '12px Arial';
            this.ctx.fillStyle = '#333';
            this.ctx.strokeStyle = '#fff';
            this.ctx.lineWidth = 2;
            this.ctx.strokeText(doll.name, doll.x, bobY + 45);
            this.ctx.fillText(doll.name, doll.x, bobY + 45);

            // 如果娃娃被抓住，添加特殊效果
            if (doll.grabbed && this.grabbedDoll === doll) {
                // 发光光环
                this.ctx.shadowColor = doll.color;
                this.ctx.shadowBlur = 20;
                this.ctx.strokeStyle = doll.color;
                this.ctx.lineWidth = 4;
                this.ctx.beginPath();
                this.ctx.arc(doll.x, bobY, 40, 0, 2 * Math.PI);
                this.ctx.stroke();
                this.ctx.shadowBlur = 0;

                // 闪光效果
                this.ctx.fillStyle = '#FFD700';
                this.ctx.font = '25px Arial';
                this.ctx.fillText('✨', doll.x - 35, bobY - 20);
                this.ctx.fillText('✨', doll.x + 35, bobY - 20);
                this.ctx.fillText('⭐', doll.x, bobY - 40);
            }
        }
    }

    drawClaw() {
        // 绘制抓手
        this.ctx.fillStyle = '#C0C0C0';
        this.ctx.fillRect(this.claw.x - 30, this.claw.y, 60, 20);

        // 绘制抓手爪子
        this.ctx.strokeStyle = '#808080';
        this.ctx.lineWidth = 3;

        // 左爪
        this.ctx.beginPath();
        this.ctx.moveTo(this.claw.x - 20, this.claw.y + 20);
        this.ctx.lineTo(this.claw.x - 10, this.claw.y + 35);
        this.ctx.stroke();

        // 右爪
        this.ctx.beginPath();
        this.ctx.moveTo(this.claw.x + 20, this.claw.y + 20);
        this.ctx.lineTo(this.claw.x + 10, this.claw.y + 35);
        this.ctx.stroke();

        // 中间爪
        this.ctx.beginPath();
        this.ctx.moveTo(this.claw.x, this.claw.y + 20);
        this.ctx.lineTo(this.claw.x, this.claw.y + 40);
        this.ctx.stroke();
    }

    drawClawLine() {
        // 绘制抓手的线
        this.ctx.strokeStyle = '#000';
        this.ctx.lineWidth = 2;
        this.ctx.beginPath();
        this.ctx.moveTo(this.claw.x, 30);
        this.ctx.lineTo(this.claw.x, this.claw.y);
        this.ctx.stroke();
    }

    drawParticles() {
        this.ctx.font = '20px Arial';
        this.ctx.textAlign = 'center';

        for (let particle of this.particles) {
            const alpha = particle.life / particle.maxLife;
            this.ctx.globalAlpha = alpha;
            this.ctx.fillStyle = '#FFD700';
            this.ctx.fillText(particle.emoji, particle.x, particle.y);
        }

        this.ctx.globalAlpha = 1;
    }

    updateUI() {
        document.getElementById('score').textContent = this.score;
        document.getElementById('coins').textContent = this.coins;
        document.getElementById('timer').textContent = this.timeLeft;
    }

    updateMessage(message) {
        document.getElementById('gameMessage').textContent = message;
    }

    gameLoop() {
        this.update();
        this.draw();
        requestAnimationFrame(() => this.gameLoop());
    }
}

// 启动游戏
window.addEventListener('load', () => {
    new ClawMachineGame();
});
