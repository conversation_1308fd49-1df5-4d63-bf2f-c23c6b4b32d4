* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
}

.game-container {
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
    border: 1px solid rgba(255, 255, 255, 0.18);
}

h1 {
    font-size: 2.5em;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    background: linear-gradient(45deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
    background-size: 400% 400%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.game-info {
    display: flex;
    justify-content: space-around;
    margin-bottom: 20px;
    font-size: 1.2em;
    font-weight: bold;
}

.game-info > div {
    background: rgba(255, 255, 255, 0.2);
    padding: 10px 20px;
    border-radius: 10px;
    backdrop-filter: blur(5px);
}

#gameCanvas {
    border: 3px solid #fff;
    border-radius: 15px;
    background: linear-gradient(to bottom, #87CEEB 0%, #98FB98 100%);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    margin-bottom: 20px;
}

.controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.control-info {
    text-align: left;
    background: rgba(255, 255, 255, 0.1);
    padding: 15px;
    border-radius: 10px;
    font-size: 0.9em;
}

.control-info p {
    margin: 5px 0;
}

.buttons {
    display: flex;
    gap: 10px;
}

button {
    padding: 12px 24px;
    font-size: 1em;
    font-weight: bold;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: linear-gradient(45deg, #ff6b6b, #feca57);
    color: white;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    background: linear-gradient(45deg, #feca57, #ff6b6b);
}

button:active {
    transform: translateY(0);
}

button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.game-status {
    background: rgba(255, 255, 255, 0.2);
    padding: 15px;
    border-radius: 10px;
    font-size: 1.1em;
    font-weight: bold;
}

#gameMessage {
    color: #fff;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

/* 响应式设计 */
@media (max-width: 900px) {
    .game-container {
        padding: 15px;
    }
    
    #gameCanvas {
        width: 100%;
        max-width: 600px;
        height: auto;
    }
    
    .controls {
        flex-direction: column;
        gap: 15px;
    }
    
    .control-info {
        text-align: center;
    }
    
    h1 {
        font-size: 2em;
    }
    
    .game-info {
        flex-direction: column;
        gap: 10px;
    }
}
