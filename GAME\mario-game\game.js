// 马里奥游戏主类
class MarioGame {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        
        // 游戏状态
        this.gameState = 'menu'; // menu, playing, paused, gameOver, levelComplete
        this.score = 0;
        this.coins = 0;
        this.lives = 3;
        this.time = 400;
        this.level = 1;
        
        // 游戏对象
        this.mario = null;
        this.blocks = [];
        this.enemies = [];
        this.items = [];
        this.particles = [];
        
        // 输入状态
        this.keys = {};
        
        // 游戏设置
        this.gravity = 0.8;
        this.groundLevel = this.canvas.height - 64;
        this.cameraX = 0;
        this.levelWidth = 3200; // 关卡宽度
        
        // 计时器
        this.gameTimer = 0;
        
        // 初始化
        this.initializeEventListeners();
        this.initializeUI();

        // 开始游戏循环
        this.gameLoop();
    }
    
    initializeEventListeners() {
        // 键盘事件
        document.addEventListener('keydown', (e) => {
            this.keys[e.code] = true;
            this.handleKeyPress(e);
        });
        
        document.addEventListener('keyup', (e) => {
            this.keys[e.code] = false;
        });
        
        // 按钮事件
        document.getElementById('startButton').addEventListener('click', () => {
            this.startGame();
        });
        
        document.getElementById('restartButton').addEventListener('click', () => {
            this.restartGame();
        });
        
        document.getElementById('menuButton').addEventListener('click', () => {
            this.showMainMenu();
        });
        
        document.getElementById('resumeButton').addEventListener('click', () => {
            this.resumeGame();
        });
        
        document.getElementById('mainMenuButton').addEventListener('click', () => {
            this.showMainMenu();
        });
        
        document.getElementById('nextLevelButton').addEventListener('click', () => {
            this.nextLevel();
        });
    }
    
    initializeUI() {
        this.updateUI();
    }
    
    handleKeyPress(e) {
        switch(e.code) {
            case 'KeyP':
                if (this.gameState === 'playing') {
                    this.pauseGame();
                } else if (this.gameState === 'paused') {
                    this.resumeGame();
                }
                break;
            case 'Escape':
                if (this.gameState === 'playing' || this.gameState === 'paused') {
                    this.showMainMenu();
                }
                break;
        }
    }
    
    startGame() {
        this.gameState = 'playing';
        this.score = 0;
        this.coins = 0;
        this.lives = 3;
        this.time = 400;
        this.level = 1;
        this.cameraX = 0;
        
        // 重置游戏对象
        this.blocks = [];
        this.enemies = [];
        this.items = [];
        this.particles = [];
        
        // 创建马里奥（稍微高一点，让他自然落到地面）
        this.mario = new Mario(100, this.groundLevel - 100);
        
        // 创建关卡
        this.createLevel();
        
        // 隐藏菜单
        this.hideAllMenus();
        
        this.updateUI();
    }
    
    pauseGame() {
        this.gameState = 'paused';
        document.getElementById('pauseMenu').classList.remove('hidden');
    }
    
    resumeGame() {
        this.gameState = 'playing';
        document.getElementById('pauseMenu').classList.add('hidden');
    }
    
    restartGame() {
        this.startGame();
    }
    
    showMainMenu() {
        this.gameState = 'menu';
        document.getElementById('gameMenu').classList.remove('hidden');
        this.hideOtherMenus(['gameMenu']);
    }
    
    gameOver() {
        this.gameState = 'gameOver';
        document.getElementById('finalScore').textContent = this.score;
        document.getElementById('finalCoins').textContent = this.coins;
        document.getElementById('gameOver').classList.remove('hidden');
    }
    
    levelComplete() {
        this.gameState = 'levelComplete';
        const timeBonus = this.time * 50;
        this.score += timeBonus;
        
        document.getElementById('levelScore').textContent = this.score;
        document.getElementById('timeBonus').textContent = timeBonus;
        document.getElementById('levelComplete').classList.remove('hidden');
        
        this.updateUI();
    }
    
    nextLevel() {
        this.level++;
        this.time = 400;
        this.startGame();
    }
    
    hideAllMenus() {
        document.getElementById('gameMenu').classList.add('hidden');
        document.getElementById('gameOver').classList.add('hidden');
        document.getElementById('pauseMenu').classList.add('hidden');
        document.getElementById('levelComplete').classList.add('hidden');
    }
    
    hideOtherMenus(except) {
        const menus = ['gameMenu', 'gameOver', 'pauseMenu', 'levelComplete'];
        menus.forEach(menu => {
            if (!except.includes(menu)) {
                document.getElementById(menu).classList.add('hidden');
            }
        });
    }
    
    updateUI() {
        document.getElementById('score').textContent = this.score.toString().padStart(6, '0');
        document.getElementById('coins').textContent = this.coins.toString().padStart(2, '0');
        document.getElementById('time').textContent = this.time;
        document.getElementById('lives').textContent = this.lives;
    }
    
    // 添加音效支持
    playSound(frequency, duration, type = 'square') {
        if (!this.audioContext) {
            try {
                this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            } catch (e) {
                return;
            }
        }
        
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);
        
        oscillator.frequency.value = frequency;
        oscillator.type = type;
        
        gainNode.gain.setValueAtTime(0.1, this.audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + duration);
        
        oscillator.start(this.audioContext.currentTime);
        oscillator.stop(this.audioContext.currentTime + duration);
    }
    
    createLevel() {
        // 创建地面
        for (let x = 0; x < this.levelWidth; x += 32) {
            this.blocks.push(new Block(x, this.groundLevel, 'ground'));
            this.blocks.push(new Block(x, this.groundLevel + 32, 'ground'));
        }
        
        // 创建一些砖块和问号砖块
        this.blocks.push(new Block(300, this.groundLevel - 64, 'brick'));
        this.blocks.push(new Block(332, this.groundLevel - 64, 'question', 'mushroom'));
        this.blocks.push(new Block(364, this.groundLevel - 64, 'brick'));
        this.blocks.push(new Block(396, this.groundLevel - 64, 'question', 'fireflower'));

        this.blocks.push(new Block(500, this.groundLevel - 128, 'question', 'coin'));
        this.blocks.push(new Block(532, this.groundLevel - 128, 'question', 'star'));
        this.blocks.push(new Block(564, this.groundLevel - 128, 'question', 'oneup'));

        // 添加更多砖块
        for (let i = 0; i < 5; i++) {
            this.blocks.push(new Block(800 + i * 32, this.groundLevel - 64, 'brick'));
        }
        
        // 创建管道
        this.blocks.push(new Block(600, this.groundLevel - 32, 'pipe'));
        this.blocks.push(new Block(600, this.groundLevel - 64, 'pipe'));
        
        // 创建敌人
        this.enemies.push(new Goomba(400, this.groundLevel - 32));
        this.enemies.push(new Koopa(550, this.groundLevel - 32));
        this.enemies.push(new Goomba(700, this.groundLevel - 32));
        this.enemies.push(new Koopa(850, this.groundLevel - 32));
        this.enemies.push(new Goomba(1000, this.groundLevel - 32));
        this.enemies.push(new Koopa(1200, this.groundLevel - 32));
        
        // 创建旗杆（关卡结束点）
        this.blocks.push(new Block(this.levelWidth - 100, this.groundLevel - 200, 'flag'));
    }
    
    update() {
        if (this.gameState !== 'playing') return;
        
        // 更新计时器（每120帧减少1秒，约2秒减1）
        this.gameTimer++;
        if (this.gameTimer % 120 === 0 && this.time > 0) {
            this.time--;
            this.updateUI();

            if (this.time <= 0) {
                this.mario.takeDamage();
            }
        }
        
        // 更新马里奥
        if (this.mario) {
            this.mario.update(this.keys, this.blocks);
            
            // 检查马里奥是否掉落
            if (this.mario.y > this.canvas.height) {
                this.mario.takeDamage();
            }
            
            // 检查生命值
            if (this.mario.lives <= 0) {
                this.lives--;
                if (this.lives <= 0) {
                    this.gameOver();
                    return;
                } else {
                    // 重置马里奥位置
                    this.mario = new Mario(100, this.groundLevel - 100);
                    this.cameraX = 0;
                }
            }
        }
        
        // 更新敌人
        this.enemies = this.enemies.filter(enemy => {
            enemy.update(this.blocks);
            return enemy.alive && enemy.x > this.cameraX - 100;
        });
        
        // 更新道具
        this.items = this.items.filter(item => {
            item.update(this.blocks);
            return item.active && item.x > this.cameraX - 100;
        });
        
        // 更新粒子效果
        this.particles = this.particles.filter(particle => {
            particle.update();
            return particle.life > 0;
        });
        
        // 碰撞检测
        this.checkCollisions();
        
        // 更新摄像机
        this.updateCamera();
    }
    
    updateCamera() {
        if (this.mario) {
            // 摄像机跟随马里奥
            const targetX = this.mario.x - this.canvas.width / 3;
            this.cameraX = Math.max(0, Math.min(targetX, this.levelWidth - this.canvas.width));
        }
    }
    
    checkCollisions() {
        if (!this.mario) return;
        
        // 马里奥与敌人的碰撞
        this.enemies.forEach(enemy => {
            if (enemy.alive && this.mario.checkCollision(enemy)) {
                if (this.mario.velocityY > 0 && this.mario.y < enemy.y) {
                    // 踩踏敌人
                    if (enemy instanceof Koopa && enemy.isShell) {
                        // 踩踏龟壳会踢击
                        enemy.kick();
                        this.score += 200;
                        this.playSound(1000, 0.2);
                    } else {
                        // 普通踩踏
                        enemy.stomp();
                        this.score += 100;
                        this.playSound(800, 0.2);
                    }
                    this.mario.bounce();
                    this.updateUI();
                } else if (enemy instanceof Koopa && enemy.isShell && !enemy.shellKicked) {
                    // 碰触静止的龟壳会踢击
                    enemy.kick();
                    this.score += 200;
                    this.playSound(1000, 0.2);
                    this.updateUI();
                } else if (!this.mario.invulnerable) {
                    // 马里奥受伤
                    this.mario.takeDamage();
                    this.playSound(200, 0.5, 'sawtooth');
                }
            }
        });
        
        // 马里奥与道具的碰撞
        this.items.forEach(item => {
            if (item.active && this.mario.checkCollision(item)) {
                item.collect(this.mario);

                switch (item.type) {
                    case 'mushroom':
                        this.score += 1000;
                        this.playSound(600, 0.3);
                        break;
                    case 'fireflower':
                        this.score += 1000;
                        this.playSound(600, 0.3);
                        break;
                    case 'star':
                        this.score += 1000;
                        this.playSound(400, 0.5, 'triangle');
                        break;
                    case 'oneup':
                        this.score += 1000;
                        this.playSound(800, 0.5);
                        break;
                    case 'coin':
                        this.coins++;
                        this.score += 200;
                        this.playSound(1200, 0.1);

                        // 100个金币获得额外生命
                        if (this.coins >= 100) {
                            this.coins = 0;
                            this.lives++;
                        }
                        break;
                    case 'fireball':
                        // 火球不能被马里奥收集
                        return;
                }
                this.updateUI();
            }
        });
        
        // 检查是否到达旗杆
        if (this.mario.x >= this.levelWidth - 150) {
            this.levelComplete();
        }
    }
    
    render() {
        // 清空画布
        this.ctx.fillStyle = '#87CEEB';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        // 绘制背景
        this.drawBackground();
        
        // 保存上下文状态
        this.ctx.save();
        
        // 应用摄像机变换
        this.ctx.translate(-this.cameraX, 0);
        
        if (this.gameState === 'playing' || this.gameState === 'paused') {
            // 绘制砖块
            this.blocks.forEach(block => {
                if (block.x + block.width > this.cameraX && block.x < this.cameraX + this.canvas.width) {
                    block.render(this.ctx);
                }
            });
            
            // 绘制敌人
            this.enemies.forEach(enemy => {
                if (enemy.x + enemy.width > this.cameraX && enemy.x < this.cameraX + this.canvas.width) {
                    enemy.render(this.ctx);
                }
            });
            
            // 绘制道具
            this.items.forEach(item => {
                if (item.x + item.width > this.cameraX && item.x < this.cameraX + this.canvas.width) {
                    item.render(this.ctx);
                }
            });
            
            // 绘制马里奥
            if (this.mario) {
                this.mario.render(this.ctx);
            }
            
            // 绘制粒子效果
            this.particles.forEach(particle => particle.render(this.ctx));
        }
        
        // 恢复上下文状态
        this.ctx.restore();
        
        // 绘制暂停提示
        if (this.gameState === 'paused') {
            this.ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
            this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        }
    }
    
    drawBackground() {
        // 绘制天空渐变
        const gradient = this.ctx.createLinearGradient(0, 0, 0, this.canvas.height);
        gradient.addColorStop(0, '#87CEEB');
        gradient.addColorStop(0.6, '#87CEEB');
        gradient.addColorStop(0.6, '#228B22');
        gradient.addColorStop(1, '#32CD32');
        
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        // 绘制云朵
        this.drawClouds();
    }
    
    drawClouds() {
        this.ctx.fillStyle = '#ffffff';
        const cloudPositions = [200, 500, 800, 1200, 1600, 2000];
        
        cloudPositions.forEach(pos => {
            const cloudX = pos - (this.cameraX * 0.3) % (this.levelWidth + 400);
            if (cloudX > -100 && cloudX < this.canvas.width + 100) {
                // 简单的云朵形状
                this.ctx.beginPath();
                this.ctx.arc(cloudX, 80, 20, 0, Math.PI * 2);
                this.ctx.arc(cloudX + 25, 80, 25, 0, Math.PI * 2);
                this.ctx.arc(cloudX + 50, 80, 20, 0, Math.PI * 2);
                this.ctx.arc(cloudX + 25, 60, 20, 0, Math.PI * 2);
                this.ctx.fill();
            }
        });
    }
    
    gameLoop() {
        this.update();
        this.render();
        requestAnimationFrame(() => this.gameLoop());
    }
}

// 马里奥角色类
class Mario {
    constructor(x, y) {
        this.x = x;
        this.y = y;
        this.width = 32;
        this.height = 32;
        this.velocityX = 0;
        this.velocityY = 0;
        this.speed = 5;
        this.jumpPower = 15;
        this.onGround = false;
        this.facingRight = true;
        this.isSuper = false;
        this.isFire = false;
        this.isStarPowered = false;
        this.invulnerable = 0;
        this.starTimer = 0;
        this.lives = 1;
        this.animationFrame = 0;
        this.animationTimer = 0;
        this.fireballCooldown = 0;
    }

    update(keys, blocks) {
        // 处理输入
        this.velocityX = 0;

        if (keys['ArrowLeft'] || keys['KeyA']) {
            this.velocityX = -this.speed;
            this.facingRight = false;
        }
        if (keys['ArrowRight'] || keys['KeyD']) {
            this.velocityX = this.speed;
            this.facingRight = true;
        }
        if ((keys['Space'] || keys['ArrowUp'] || keys['KeyW']) && this.onGround) {
            this.velocityY = -this.jumpPower;
            this.onGround = false;
            // 播放跳跃音效
            if (window.marioGame) {
                window.marioGame.playSound(400, 0.2);
            }
        }

        // 火球射击 (Shift键)
        if ((keys['ShiftLeft'] || keys['ShiftRight']) && this.isFire && this.fireballCooldown <= 0) {
            this.shootFireball();
            this.fireballCooldown = 20; // 冷却时间
        }

        // 应用重力
        this.velocityY += window.marioGame.gravity;

        // 更新位置
        this.x += this.velocityX;
        this.y += this.velocityY;

        // 碰撞检测
        this.checkBlockCollisions(blocks);

        // 更新动画
        this.updateAnimation();

        // 更新无敌时间
        if (this.invulnerable > 0) {
            this.invulnerable--;
        }

        // 更新星星状态
        if (this.isStarPowered) {
            this.starTimer--;
            if (this.starTimer <= 0) {
                this.isStarPowered = false;
            }
        }

        // 更新火球冷却时间
        if (this.fireballCooldown > 0) {
            this.fireballCooldown--;
        }
    }

    checkBlockCollisions(blocks) {
        this.onGround = false;

        blocks.forEach(block => {
            if (block.broken) return; // 跳过已破坏的砖块

            if (this.checkCollision(block)) {
                // 检查垂直碰撞（着陆）
                if (this.velocityY >= 0 && this.y + this.height - this.velocityY <= block.y) {
                    // 从上方着陆
                    this.y = block.y - this.height;
                    this.velocityY = 0;
                    this.onGround = true;
                }
                // 检查垂直碰撞（撞头）
                else if (this.velocityY < 0 && this.y - this.velocityY >= block.y + block.height) {
                    // 从下方撞击
                    this.y = block.y + block.height;
                    this.velocityY = 0;

                    // 撞击砖块
                    if (block.type === 'brick' && this.isSuper) {
                        block.break();
                        if (window.marioGame) {
                            window.marioGame.score += 50;
                            window.marioGame.playSound(300, 0.3);
                        }
                    } else if (block.type === 'question' && !block.used) {
                        block.activate();
                        if (window.marioGame) {
                            window.marioGame.playSound(500, 0.3);
                        }
                    }
                }
                // 检查水平碰撞
                else if (this.velocityX > 0 && this.x + this.width - this.velocityX <= block.x) {
                    // 从左侧碰撞
                    this.x = block.x - this.width;
                } else if (this.velocityX < 0 && this.x - this.velocityX >= block.x + block.width) {
                    // 从右侧碰撞
                    this.x = block.x + block.width;
                }
            }
        });
    }

    checkCollision(other) {
        return this.x < other.x + other.width &&
               this.x + this.width > other.x &&
               this.y < other.y + other.height &&
               this.y + this.height > other.y;
    }

    takeDamage() {
        if (this.invulnerable > 0 || this.isStarPowered) return;

        if (this.isFire) {
            // 火花马里奥变成超级马里奥
            this.isFire = false;
            this.invulnerable = 120;
        } else if (this.isSuper) {
            // 如果是超级马里奥，变回小马里奥
            this.isSuper = false;
            this.height = 32;
            this.invulnerable = 120; // 2秒无敌时间
        } else {
            // 如果是小马里奥，失去一条命
            this.die();
        }
    }

    die() {
        this.lives = 0; // 标记马里奥死亡，让游戏主循环处理
        this.invulnerable = 120;
    }

    powerUp() {
        if (!this.isSuper) {
            this.isSuper = true;
            this.height = 48;
            this.y -= 16; // 调整位置避免卡在地面
        }
    }

    powerUpToFire() {
        if (!this.isSuper) {
            this.isSuper = true;
            this.height = 48;
            this.y -= 16;
        }
        this.isFire = true;
    }

    starPower() {
        this.isStarPowered = true;
        this.starTimer = 600; // 10秒无敌时间
        this.invulnerable = 600;
    }

    shootFireball() {
        if (window.marioGame) {
            const fireball = new Fireball(
                this.x + (this.facingRight ? this.width : 0),
                this.y + this.height / 2,
                this.facingRight ? 8 : -8
            );
            window.marioGame.items.push(fireball);
            window.marioGame.playSound(1200, 0.2, 'square');
        }
    }

    bounce() {
        this.velocityY = -8; // 踩踏敌人后的小跳跃
    }

    updateAnimation() {
        this.animationTimer++;
        if (this.animationTimer >= 8) {
            this.animationTimer = 0;
            this.animationFrame = (this.animationFrame + 1) % 4;
        }
    }

    render(ctx) {
        // 无敌时闪烁效果（星星状态除外）
        if (this.invulnerable > 0 && !this.isStarPowered && Math.floor(this.invulnerable / 8) % 2 === 0) {
            return;
        }

        // 星星状态的彩虹效果
        let bodyColor, hatColor;
        if (this.isStarPowered) {
            const colors = ['#ff0000', '#ff6600', '#ffff00', '#00ff00', '#0066ff', '#6600ff'];
            const colorIndex = Math.floor(Date.now() / 100) % colors.length;
            bodyColor = colors[colorIndex];
            hatColor = colors[(colorIndex + 1) % colors.length];
        } else if (this.isFire) {
            bodyColor = '#ffffff';
            hatColor = '#ff0000';
        } else {
            bodyColor = this.isSuper ? '#ff0000' : '#ff6600';
            hatColor = '#cc0000';
        }

        // 绘制马里奥
        ctx.fillStyle = bodyColor;

        // 主体
        ctx.fillRect(this.x + 8, this.y + 8, 16, this.height - 16);

        // 帽子
        ctx.fillStyle = hatColor;
        ctx.fillRect(this.x + 4, this.y, 24, 12);

        // 脸部
        ctx.fillStyle = '#ffcc99';
        ctx.fillRect(this.x + 8, this.y + 8, 16, 8);

        // 胡子
        ctx.fillStyle = '#8B4513';
        ctx.fillRect(this.x + 12, this.y + 12, 8, 4);

        // 手臂
        ctx.fillStyle = '#ffcc99';
        if (this.velocityX !== 0) {
            // 跑步动画
            const armOffset = Math.sin(this.animationFrame) * 2;
            ctx.fillRect(this.x + 2, this.y + 16 + armOffset, 6, 8);
            ctx.fillRect(this.x + 24, this.y + 16 - armOffset, 6, 8);
        } else {
            ctx.fillRect(this.x + 2, this.y + 16, 6, 8);
            ctx.fillRect(this.x + 24, this.y + 16, 6, 8);
        }

        // 腿部
        ctx.fillStyle = '#0000ff';
        if (this.onGround && this.velocityX !== 0) {
            // 跑步动画
            const legOffset = Math.sin(this.animationFrame + Math.PI) * 3;
            ctx.fillRect(this.x + 8, this.y + this.height - 12, 6, 12);
            ctx.fillRect(this.x + 18, this.y + this.height - 12, 6, 12);
        } else {
            ctx.fillRect(this.x + 8, this.y + this.height - 12, 6, 12);
            ctx.fillRect(this.x + 18, this.y + this.height - 12, 6, 12);
        }

        // 鞋子
        ctx.fillStyle = '#8B4513';
        ctx.fillRect(this.x + 6, this.y + this.height - 4, 10, 4);
        ctx.fillRect(this.x + 16, this.y + this.height - 4, 10, 4);
    }
}

// 砖块类
class Block {
    constructor(x, y, type, itemType = null) {
        this.x = x;
        this.y = y;
        this.width = 32;
        this.height = 32;
        this.type = type;
        this.itemType = itemType || (type === 'question' ? 'mushroom' : null);
        this.used = false;
        this.broken = false;
    }

    break() {
        if (this.type === 'brick') {
            this.broken = true;
            // 创建破碎粒子效果
            if (window.marioGame) {
                for (let i = 0; i < 4; i++) {
                    window.marioGame.particles.push(new Particle(
                        this.x + this.width / 2,
                        this.y + this.height / 2,
                        'brick'
                    ));
                }
            }
        }
    }

    activate() {
        if (this.type === 'question' && !this.used) {
            this.used = true;

            // 生成道具
            if (window.marioGame) {
                let item;
                switch (this.itemType) {
                    case 'mushroom':
                        item = new Mushroom(this.x, this.y - 32);
                        break;
                    case 'fireflower':
                        item = new FireFlower(this.x, this.y - 32);
                        break;
                    case 'star':
                        item = new Star(this.x, this.y - 32);
                        break;
                    case 'oneup':
                        item = new OneUpMushroom(this.x, this.y - 32);
                        break;
                    case 'coin':
                    default:
                        item = new Coin(this.x, this.y - 32);
                        break;
                }
                window.marioGame.items.push(item);
            }
        }
    }

    render(ctx) {
        if (this.broken) return;

        switch (this.type) {
            case 'ground':
                ctx.fillStyle = '#8B4513';
                ctx.fillRect(this.x, this.y, this.width, this.height);
                ctx.strokeStyle = '#654321';
                ctx.strokeRect(this.x, this.y, this.width, this.height);
                break;

            case 'brick':
                ctx.fillStyle = '#CD853F';
                ctx.fillRect(this.x, this.y, this.width, this.height);
                ctx.strokeStyle = '#8B4513';
                ctx.strokeRect(this.x, this.y, this.width, this.height);
                // 砖块纹理
                ctx.strokeRect(this.x + 8, this.y + 8, 16, 16);
                break;

            case 'question':
                ctx.fillStyle = this.used ? '#8B4513' : '#FFD700';
                ctx.fillRect(this.x, this.y, this.width, this.height);
                ctx.strokeStyle = '#000';
                ctx.strokeRect(this.x, this.y, this.width, this.height);

                if (!this.used) {
                    // 绘制问号
                    ctx.fillStyle = '#000';
                    ctx.font = '20px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('?', this.x + 16, this.y + 22);
                }
                break;

            case 'pipe':
                ctx.fillStyle = '#228B22';
                ctx.fillRect(this.x, this.y, this.width, this.height);
                ctx.strokeStyle = '#006400';
                ctx.strokeRect(this.x, this.y, this.width, this.height);
                break;

            case 'flag':
                // 旗杆
                ctx.fillStyle = '#8B4513';
                ctx.fillRect(this.x + 14, this.y, 4, this.height);
                // 旗帜
                ctx.fillStyle = '#ff0000';
                ctx.fillRect(this.x - 16, this.y, 16, 12);
                break;
        }
    }
}

// 栗子怪类
class Goomba {
    constructor(x, y) {
        this.x = x;
        this.y = y;
        this.width = 32;
        this.height = 32;
        this.velocityX = -1;
        this.velocityY = 0;
        this.alive = true;
        this.stomped = false;
        this.stompTimer = 0;
    }

    update(blocks) {
        if (!this.alive) return;

        if (this.stomped) {
            this.stompTimer++;
            if (this.stompTimer > 30) {
                this.alive = false;
            }
            return;
        }

        // 应用重力
        this.velocityY += window.marioGame.gravity;

        // 更新位置
        this.x += this.velocityX;
        this.y += this.velocityY;

        // 碰撞检测
        this.checkBlockCollisions(blocks);

        // 边界检测 - 遇到边缘转向
        if (this.velocityX < 0 && this.isAtEdge(blocks)) {
            this.velocityX = 1;
        } else if (this.velocityX > 0 && this.isAtEdge(blocks)) {
            this.velocityX = -1;
        }
    }

    checkBlockCollisions(blocks) {
        blocks.forEach(block => {
            if (this.checkCollision(block)) {
                if (this.velocityY > 0) {
                    // 着陆
                    this.y = block.y - this.height;
                    this.velocityY = 0;
                }

                if (this.velocityX > 0) {
                    this.x = block.x - this.width;
                    this.velocityX = -1;
                } else if (this.velocityX < 0) {
                    this.x = block.x + block.width;
                    this.velocityX = 1;
                }
            }
        });
    }

    checkCollision(other) {
        return this.x < other.x + other.width &&
               this.x + this.width > other.x &&
               this.y < other.y + other.height &&
               this.y + this.height > other.y;
    }

    isAtEdge(blocks) {
        const checkX = this.velocityX > 0 ? this.x + this.width + 5 : this.x - 5;
        const checkY = this.y + this.height + 5;

        return !blocks.some(block =>
            checkX >= block.x && checkX <= block.x + block.width &&
            checkY >= block.y && checkY <= block.y + block.height
        );
    }

    stomp() {
        this.stomped = true;
        this.height = 16;
        this.y += 16;
    }

    hitByShell() {
        // 被龟壳击中
        this.alive = false;
        // 创建击飞效果
        if (window.marioGame) {
            for (let i = 0; i < 6; i++) {
                window.marioGame.particles.push(new Particle(
                    this.x + this.width / 2,
                    this.y + this.height / 2,
                    'enemy'
                ));
            }
        }
    }

    hitByFireball() {
        // 被火球击中
        this.alive = false;
        // 创建火焰效果
        if (window.marioGame) {
            for (let i = 0; i < 8; i++) {
                window.marioGame.particles.push(new Particle(
                    this.x + this.width / 2,
                    this.y + this.height / 2,
                    'fire'
                ));
            }
        }
    }

    render(ctx) {
        if (!this.alive) return;

        if (this.stomped) {
            // 被踩扁的样子
            ctx.fillStyle = '#8B4513';
            ctx.fillRect(this.x, this.y, this.width, this.height);
        } else {
            // 正常的栗子怪
            ctx.fillStyle = '#8B4513';
            ctx.fillRect(this.x + 4, this.y + 8, 24, 24);

            // 眼睛
            ctx.fillStyle = '#000';
            ctx.fillRect(this.x + 8, this.y + 12, 4, 4);
            ctx.fillRect(this.x + 20, this.y + 12, 4, 4);

            // 眉毛
            ctx.fillRect(this.x + 6, this.y + 10, 8, 2);
            ctx.fillRect(this.x + 18, this.y + 10, 8, 2);

            // 脚
            ctx.fillStyle = '#654321';
            ctx.fillRect(this.x, this.y + 28, 8, 4);
            ctx.fillRect(this.x + 24, this.y + 28, 8, 4);
        }
    }
}

// 蘑菇道具类
class Mushroom {
    constructor(x, y) {
        this.x = x;
        this.y = y;
        this.width = 32;
        this.height = 32;
        this.velocityX = 2;
        this.velocityY = 0;
        this.active = true;
        this.type = 'mushroom';
    }

    update(blocks) {
        if (!this.active) return;

        // 应用重力
        this.velocityY += window.marioGame.gravity;

        // 更新位置
        this.x += this.velocityX;
        this.y += this.velocityY;

        // 碰撞检测
        this.checkBlockCollisions(blocks);
    }

    checkBlockCollisions(blocks) {
        blocks.forEach(block => {
            if (this.checkCollision(block)) {
                if (this.velocityY > 0) {
                    this.y = block.y - this.height;
                    this.velocityY = 0;
                }

                if (this.velocityX > 0) {
                    this.x = block.x - this.width;
                    this.velocityX = -2;
                } else if (this.velocityX < 0) {
                    this.x = block.x + block.width;
                    this.velocityX = 2;
                }
            }
        });
    }

    checkCollision(other) {
        return this.x < other.x + other.width &&
               this.x + this.width > other.x &&
               this.y < other.y + other.height &&
               this.y + this.height > other.y;
    }

    collect(mario) {
        this.active = false;
        mario.powerUp();
    }

    render(ctx) {
        if (!this.active) return;

        // 蘑菇帽
        ctx.fillStyle = '#ff0000';
        ctx.fillRect(this.x + 4, this.y, 24, 20);

        // 蘑菇斑点
        ctx.fillStyle = '#fff';
        ctx.fillRect(this.x + 8, this.y + 4, 6, 6);
        ctx.fillRect(this.x + 18, this.y + 4, 6, 6);
        ctx.fillRect(this.x + 13, this.y + 12, 6, 6);

        // 蘑菇茎
        ctx.fillStyle = '#ffcc99';
        ctx.fillRect(this.x + 12, this.y + 20, 8, 12);
    }
}

// 金币类
class Coin {
    constructor(x, y) {
        this.x = x;
        this.y = y;
        this.width = 24;
        this.height = 24;
        this.velocityY = -5;
        this.active = true;
        this.type = 'coin';
        this.animationFrame = 0;
        this.animationTimer = 0;
        this.collectTimer = 0;
    }

    update(blocks) {
        if (!this.active) return;

        if (this.collectTimer > 0) {
            this.collectTimer--;
            this.y -= 2;
            if (this.collectTimer <= 0) {
                this.active = false;
            }
            return;
        }

        // 更新动画
        this.animationTimer++;
        if (this.animationTimer >= 10) {
            this.animationTimer = 0;
            this.animationFrame = (this.animationFrame + 1) % 4;
        }

        // 应用重力
        this.velocityY += window.marioGame.gravity;
        this.y += this.velocityY;

        // 碰撞检测
        this.checkBlockCollisions(blocks);
    }

    checkBlockCollisions(blocks) {
        blocks.forEach(block => {
            if (this.checkCollision(block) && this.velocityY > 0) {
                this.y = block.y - this.height;
                this.velocityY = 0;
            }
        });
    }

    checkCollision(other) {
        return this.x < other.x + other.width &&
               this.x + this.width > other.x &&
               this.y < other.y + other.height &&
               this.y + this.height > other.y;
    }

    collect(mario) {
        this.collectTimer = 30;
    }

    render(ctx) {
        if (!this.active) return;

        // 金币旋转效果
        const scale = Math.abs(Math.sin(this.animationFrame * Math.PI / 2));
        const width = this.width * scale;
        const offsetX = (this.width - width) / 2;

        ctx.fillStyle = '#FFD700';
        ctx.fillRect(this.x + offsetX, this.y, width, this.height);

        if (scale > 0.3) {
            ctx.fillStyle = '#FFA500';
            ctx.fillRect(this.x + offsetX + 2, this.y + 2, width - 4, this.height - 4);
        }
    }
}

// 粒子效果类
class Particle {
    constructor(x, y, type) {
        this.x = x;
        this.y = y;
        this.velocityX = (Math.random() - 0.5) * 8;
        this.velocityY = (Math.random() - 0.5) * 8 - 2;
        this.life = 60;
        this.maxLife = 60;
        this.type = type;
        this.size = Math.random() * 6 + 2;
    }

    update() {
        this.x += this.velocityX;
        this.y += this.velocityY;
        this.velocityY += 0.3; // 重力
        this.life--;
    }

    render(ctx) {
        const alpha = this.life / this.maxLife;

        switch (this.type) {
            case 'brick':
                ctx.fillStyle = `rgba(205, 133, 63, ${alpha})`;
                break;
            case 'enemy':
                ctx.fillStyle = `rgba(255, 100, 100, ${alpha})`;
                break;
            case 'fire':
                ctx.fillStyle = `rgba(255, ${Math.floor(100 + alpha * 155)}, 0, ${alpha})`;
                break;
            default:
                ctx.fillStyle = `rgba(255, 255, 255, ${alpha})`;
        }

        ctx.fillRect(this.x, this.y, this.size, this.size);
    }
}

// 乌龟敌人类 (Koopa)
class Koopa {
    constructor(x, y) {
        this.x = x;
        this.y = y;
        this.width = 32;
        this.height = 32;
        this.velocityX = -1;
        this.velocityY = 0;
        this.alive = true;
        this.isShell = false; // 是否变成龟壳状态
        this.shellKicked = false; // 龟壳是否被踢击
        this.shellTimer = 0; // 龟壳状态计时器
        this.animationFrame = 0;
        this.animationTimer = 0;
    }

    update(blocks) {
        if (!this.alive) return;

        // 更新动画
        this.animationTimer++;
        if (this.animationTimer >= 15) {
            this.animationTimer = 0;
            this.animationFrame = (this.animationFrame + 1) % 2;
        }

        if (this.isShell) {
            // 龟壳状态
            if (this.shellKicked) {
                // 被踢击的龟壳快速移动
                this.velocityX = this.velocityX > 0 ? 8 : -8;

                // 检查与其他敌人的碰撞
                if (window.marioGame) {
                    window.marioGame.enemies.forEach(enemy => {
                        if (enemy !== this && enemy.alive && this.checkCollision(enemy)) {
                            enemy.hitByShell();
                            // 播放击中音效
                            window.marioGame.playSound(600, 0.2);
                            window.marioGame.score += 100;
                            window.marioGame.updateUI();
                        }
                    });
                }
            } else {
                // 静止的龟壳
                this.velocityX = 0;
            }

            // 龟壳计时器
            this.shellTimer++;
            if (this.shellTimer > 600) { // 10秒后复活
                this.revive();
            }
        } else {
            // 正常乌龟状态 - 保持恒定速度移动
            // 确保乌龟始终在移动
            if (this.velocityX === 0) {
                this.velocityX = -1; // 默认向左移动
            }
            // 保持恒定的移动速度
            this.velocityX = this.velocityX > 0 ? 1 : -1;
        }

        // 应用重力
        this.velocityY += window.marioGame.gravity;

        // 更新位置
        this.x += this.velocityX;
        this.y += this.velocityY;

        // 碰撞检测
        this.checkBlockCollisions(blocks);

        // 边界检测 - 遇到边缘转向
        if (!this.isShell && this.isAtEdge(blocks)) {
            this.velocityX = -this.velocityX;
        }
    }

    checkBlockCollisions(blocks) {
        blocks.forEach(block => {
            if (this.checkCollision(block)) {
                if (this.velocityY > 0) {
                    // 着陆
                    this.y = block.y - this.height;
                    this.velocityY = 0;
                }

                // 水平碰撞 - 转向
                if (this.velocityX > 0) {
                    this.x = block.x - this.width;
                    if (!this.isShell) {
                        this.velocityX = -1; // 转向左
                    } else if (this.shellKicked) {
                        this.velocityX = -8; // 龟壳反弹
                    }
                } else if (this.velocityX < 0) {
                    this.x = block.x + block.width;
                    if (!this.isShell) {
                        this.velocityX = 1; // 转向右
                    } else if (this.shellKicked) {
                        this.velocityX = 8; // 龟壳反弹
                    }
                }
            }
        });
    }

    checkCollision(other) {
        return this.x < other.x + other.width &&
               this.x + this.width > other.x &&
               this.y < other.y + other.height &&
               this.y + this.height > other.y;
    }

    isAtEdge(blocks) {
        const checkX = this.velocityX > 0 ? this.x + this.width + 5 : this.x - 5;
        const checkY = this.y + this.height + 5;

        return !blocks.some(block =>
            checkX >= block.x && checkX <= block.x + block.width &&
            checkY >= block.y && checkY <= block.y + block.height
        );
    }

    stomp() {
        if (this.isShell) {
            // 如果已经是龟壳，踩踏会踢击
            this.kick();
        } else {
            // 变成龟壳
            this.isShell = true;
            this.height = 24;
            this.y += 8;
            this.velocityX = 0;
            this.shellTimer = 0;
        }
    }

    kick() {
        if (this.isShell) {
            this.shellKicked = !this.shellKicked;
            if (this.shellKicked) {
                // 根据马里奥的位置决定踢击方向
                if (window.marioGame && window.marioGame.mario) {
                    this.velocityX = window.marioGame.mario.x < this.x ? 8 : -8;
                }
            } else {
                this.velocityX = 0;
            }
        }
    }

    revive() {
        this.isShell = false;
        this.shellKicked = false;
        this.height = 32;
        this.y -= 8;
        this.velocityX = -1;
        this.shellTimer = 0;
    }

    hitByShell() {
        // 被龟壳击中
        this.alive = false;
        // 创建击飞效果
        if (window.marioGame) {
            for (let i = 0; i < 6; i++) {
                window.marioGame.particles.push(new Particle(
                    this.x + this.width / 2,
                    this.y + this.height / 2,
                    'enemy'
                ));
            }
        }
    }

    hitByFireball() {
        // 被火球击中
        this.alive = false;
        // 创建火焰效果
        if (window.marioGame) {
            for (let i = 0; i < 8; i++) {
                window.marioGame.particles.push(new Particle(
                    this.x + this.width / 2,
                    this.y + this.height / 2,
                    'fire'
                ));
            }
        }
    }

    render(ctx) {
        if (!this.alive) return;

        if (this.isShell) {
            // 绘制龟壳
            ctx.fillStyle = this.shellKicked ? '#ff6600' : '#00aa00';
            ctx.fillRect(this.x, this.y, this.width, this.height);

            // 龟壳纹理
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(this.x + 4, this.y + 4, this.width - 8, this.height - 8);

            ctx.fillStyle = this.shellKicked ? '#ff6600' : '#00aa00';
            ctx.fillRect(this.x + 8, this.y + 8, this.width - 16, this.height - 16);

            // 龟壳闪烁效果（即将复活时）
            if (this.shellTimer > 480 && Math.floor(this.shellTimer / 10) % 2 === 0) {
                ctx.fillStyle = 'rgba(255, 255, 255, 0.5)';
                ctx.fillRect(this.x, this.y, this.width, this.height);
            }
        } else {
            // 绘制正常乌龟
            // 龟壳
            ctx.fillStyle = '#00aa00';
            ctx.fillRect(this.x + 4, this.y, 24, 20);

            // 龟壳花纹
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(this.x + 8, this.y + 4, 6, 6);
            ctx.fillRect(this.x + 18, this.y + 4, 6, 6);
            ctx.fillRect(this.x + 13, this.y + 10, 6, 6);

            // 身体
            ctx.fillStyle = '#ffcc99';
            ctx.fillRect(this.x + 8, this.y + 20, 16, 12);

            // 头部
            ctx.fillStyle = '#ffcc99';
            const headOffset = this.animationFrame * 2;
            ctx.fillRect(this.x + (this.velocityX > 0 ? 24 : 0), this.y + 16 + headOffset, 8, 8);

            // 眼睛
            ctx.fillStyle = '#000';
            ctx.fillRect(this.x + (this.velocityX > 0 ? 26 : 2), this.y + 18 + headOffset, 2, 2);

            // 脚部动画
            if (this.animationFrame === 0) {
                ctx.fillStyle = '#ffcc99';
                ctx.fillRect(this.x + 6, this.y + 28, 4, 4);
                ctx.fillRect(this.x + 22, this.y + 28, 4, 4);
            } else {
                ctx.fillStyle = '#ffcc99';
                ctx.fillRect(this.x + 4, this.y + 30, 4, 2);
                ctx.fillRect(this.x + 24, this.y + 30, 4, 2);
            }
        }
    }
}

// 火花道具类
class FireFlower {
    constructor(x, y) {
        this.x = x;
        this.y = y;
        this.width = 32;
        this.height = 32;
        this.velocityX = 0;
        this.velocityY = 0;
        this.active = true;
        this.type = 'fireflower';
        this.animationFrame = 0;
        this.animationTimer = 0;
    }

    update(blocks) {
        if (!this.active) return;

        // 更新动画
        this.animationTimer++;
        if (this.animationTimer >= 8) {
            this.animationTimer = 0;
            this.animationFrame = (this.animationFrame + 1) % 4;
        }

        // 火花道具不受重力影响，固定在原地
    }

    checkCollision(other) {
        return this.x < other.x + other.width &&
               this.x + this.width > other.x &&
               this.y < other.y + other.height &&
               this.y + this.height > other.y;
    }

    collect(mario) {
        this.active = false;
        mario.powerUpToFire();
    }

    render(ctx) {
        if (!this.active) return;

        // 火花花朵
        const colors = ['#ff0000', '#ff6600', '#ffaa00', '#ff0000'];
        ctx.fillStyle = colors[this.animationFrame];

        // 花瓣
        ctx.fillRect(this.x + 8, this.y + 4, 16, 8);
        ctx.fillRect(this.x + 4, this.y + 8, 8, 16);
        ctx.fillRect(this.x + 20, this.y + 8, 8, 16);
        ctx.fillRect(this.x + 8, this.y + 20, 16, 8);

        // 花心
        ctx.fillStyle = '#ffff00';
        ctx.fillRect(this.x + 12, this.y + 12, 8, 8);

        // 茎
        ctx.fillStyle = '#00aa00';
        ctx.fillRect(this.x + 14, this.y + 24, 4, 8);
    }
}

// 星星道具类
class Star {
    constructor(x, y) {
        this.x = x;
        this.y = y;
        this.width = 24;
        this.height = 24;
        this.velocityX = 3;
        this.velocityY = -8;
        this.active = true;
        this.type = 'star';
        this.animationFrame = 0;
        this.animationTimer = 0;
    }

    update(blocks) {
        if (!this.active) return;

        // 更新动画
        this.animationTimer++;
        if (this.animationTimer >= 5) {
            this.animationTimer = 0;
            this.animationFrame = (this.animationFrame + 1) % 8;
        }

        // 应用重力
        this.velocityY += window.marioGame.gravity;

        // 更新位置
        this.x += this.velocityX;
        this.y += this.velocityY;

        // 碰撞检测
        this.checkBlockCollisions(blocks);

        // 边界反弹
        if (this.x <= 0 || this.x >= window.marioGame.levelWidth - this.width) {
            this.velocityX = -this.velocityX;
        }
    }

    checkBlockCollisions(blocks) {
        blocks.forEach(block => {
            if (this.checkCollision(block)) {
                if (this.velocityY > 0) {
                    this.y = block.y - this.height;
                    this.velocityY = -8; // 星星会弹跳
                }

                if (this.velocityX > 0) {
                    this.x = block.x - this.width;
                    this.velocityX = -3;
                } else if (this.velocityX < 0) {
                    this.x = block.x + block.width;
                    this.velocityX = 3;
                }
            }
        });
    }

    checkCollision(other) {
        return this.x < other.x + other.width &&
               this.x + this.width > other.x &&
               this.y < other.y + other.height &&
               this.y + this.height > other.y;
    }

    collect(mario) {
        this.active = false;
        mario.starPower();
    }

    render(ctx) {
        if (!this.active) return;

        // 彩虹星星效果
        const colors = ['#ff0000', '#ff6600', '#ffff00', '#00ff00', '#0066ff', '#6600ff', '#ff00ff', '#ff0000'];
        ctx.fillStyle = colors[this.animationFrame];

        // 绘制星星形状
        const centerX = this.x + this.width / 2;
        const centerY = this.y + this.height / 2;
        const radius = 10;

        ctx.beginPath();
        for (let i = 0; i < 5; i++) {
            const angle = (i * 4 * Math.PI) / 5 - Math.PI / 2;
            const x = centerX + Math.cos(angle) * radius;
            const y = centerY + Math.sin(angle) * radius;
            if (i === 0) ctx.moveTo(x, y);
            else ctx.lineTo(x, y);
        }
        ctx.closePath();
        ctx.fill();
    }
}

// 1UP蘑菇类
class OneUpMushroom {
    constructor(x, y) {
        this.x = x;
        this.y = y;
        this.width = 32;
        this.height = 32;
        this.velocityX = 2;
        this.velocityY = 0;
        this.active = true;
        this.type = 'oneup';
    }

    update(blocks) {
        if (!this.active) return;

        // 应用重力
        this.velocityY += window.marioGame.gravity;

        // 更新位置
        this.x += this.velocityX;
        this.y += this.velocityY;

        // 碰撞检测
        this.checkBlockCollisions(blocks);
    }

    checkBlockCollisions(blocks) {
        blocks.forEach(block => {
            if (this.checkCollision(block)) {
                if (this.velocityY > 0) {
                    this.y = block.y - this.height;
                    this.velocityY = 0;
                }

                if (this.velocityX > 0) {
                    this.x = block.x - this.width;
                    this.velocityX = -2;
                } else if (this.velocityX < 0) {
                    this.x = block.x + block.width;
                    this.velocityX = 2;
                }
            }
        });
    }

    checkCollision(other) {
        return this.x < other.x + other.width &&
               this.x + this.width > other.x &&
               this.y < other.y + other.height &&
               this.y + this.height > other.y;
    }

    collect(mario) {
        this.active = false;
        // 增加生命
        if (window.marioGame) {
            window.marioGame.lives++;
            window.marioGame.updateUI();
        }
    }

    render(ctx) {
        if (!this.active) return;

        // 绿色蘑菇帽
        ctx.fillStyle = '#00aa00';
        ctx.fillRect(this.x + 4, this.y, 24, 20);

        // 白色斑点
        ctx.fillStyle = '#fff';
        ctx.fillRect(this.x + 8, this.y + 4, 6, 6);
        ctx.fillRect(this.x + 18, this.y + 4, 6, 6);
        ctx.fillRect(this.x + 13, this.y + 12, 6, 6);

        // 蘑菇茎
        ctx.fillStyle = '#ffcc99';
        ctx.fillRect(this.x + 12, this.y + 20, 8, 12);
    }
}

// 火球类
class Fireball {
    constructor(x, y, velocityX) {
        this.x = x;
        this.y = y;
        this.width = 16;
        this.height = 16;
        this.velocityX = velocityX;
        this.velocityY = -4;
        this.active = true;
        this.type = 'fireball';
        this.bounces = 0;
        this.maxBounces = 3;
        this.animationFrame = 0;
        this.animationTimer = 0;
    }

    update(blocks) {
        if (!this.active) return;

        // 更新动画
        this.animationTimer++;
        if (this.animationTimer >= 4) {
            this.animationTimer = 0;
            this.animationFrame = (this.animationFrame + 1) % 4;
        }

        // 应用重力
        this.velocityY += window.marioGame.gravity * 0.5;

        // 更新位置
        this.x += this.velocityX;
        this.y += this.velocityY;

        // 碰撞检测
        this.checkBlockCollisions(blocks);

        // 检查与敌人的碰撞
        if (window.marioGame) {
            window.marioGame.enemies.forEach(enemy => {
                if (enemy.alive && this.checkCollision(enemy)) {
                    enemy.hitByFireball();
                    this.active = false;
                    window.marioGame.score += 200;
                    window.marioGame.playSound(800, 0.2);
                    window.marioGame.updateUI();
                }
            });
        }

        // 边界检查
        if (this.x < -50 || this.x > window.marioGame.levelWidth + 50 || this.bounces >= this.maxBounces) {
            this.active = false;
        }
    }

    checkBlockCollisions(blocks) {
        blocks.forEach(block => {
            if (this.checkCollision(block)) {
                if (this.velocityY > 0) {
                    this.y = block.y - this.height;
                    this.velocityY = -6; // 弹跳
                    this.bounces++;
                }
            }
        });
    }

    checkCollision(other) {
        return this.x < other.x + other.width &&
               this.x + this.width > other.x &&
               this.y < other.y + other.height &&
               this.y + this.height > other.y;
    }

    collect(mario) {
        // 火球不能被收集
    }

    render(ctx) {
        if (!this.active) return;

        // 火球动画效果
        const colors = ['#ff0000', '#ff6600', '#ffaa00', '#ff0000'];
        ctx.fillStyle = colors[this.animationFrame];

        // 绘制火球
        ctx.beginPath();
        ctx.arc(this.x + this.width / 2, this.y + this.height / 2, this.width / 2, 0, Math.PI * 2);
        ctx.fill();

        // 内部火焰
        ctx.fillStyle = '#ffff00';
        ctx.beginPath();
        ctx.arc(this.x + this.width / 2, this.y + this.height / 2, this.width / 4, 0, Math.PI * 2);
        ctx.fill();
    }
}

// 当页面加载完成后启动游戏
document.addEventListener('DOMContentLoaded', () => {
    window.marioGame = new MarioGame();
});
