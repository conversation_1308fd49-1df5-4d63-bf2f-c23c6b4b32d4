<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>超级马里奥 - 简单版</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="game-container">
        <div class="game-header">
            <h1>🍄 超级马里奥 🍄</h1>
            <div class="game-hud">
                <div class="hud-item">
                    <span class="label">马里奥</span>
                    <div class="score" id="score">000000</div>
                </div>
                <div class="hud-item">
                    <span class="label">金币</span>
                    <div class="coins">
                        <span id="coins">00</span>
                        <span class="coin-icon">🪙</span>
                    </div>
                </div>
                <div class="hud-item">
                    <span class="label">世界</span>
                    <div class="world">1-1</div>
                </div>
                <div class="hud-item">
                    <span class="label">时间</span>
                    <div class="time" id="time">400</div>
                </div>
                <div class="hud-item">
                    <span class="label">生命</span>
                    <div class="lives">
                        <span id="lives">3</span>
                        <span class="mario-icon">🔴</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="game-area">
            <canvas id="gameCanvas" width="1024" height="512"></canvas>
            
            <div id="gameMenu" class="game-menu">
                <h2>🍄 超级马里奥 🍄</h2>
                <div class="menu-content">
                    <p>经典的马里奥平台跳跃游戏！</p>
                    <div class="controls-info">
                        <p>🎮 游戏控制：</p>
                        <p>← → 方向键：移动</p>
                        <p>空格键：跳跃</p>
                        <p>Shift键：发射火球（火花状态）</p>
                        <p>踩踏敌人消灭它们</p>
                        <p>撞击砖块获得道具</p>
                        <p>收集蘑菇变大变强！</p>
                    </div>
                    <button id="startButton" class="mario-button">开始游戏</button>
                </div>
            </div>
            
            <div id="gameOver" class="game-over hidden">
                <h2>游戏结束</h2>
                <div class="game-over-content">
                    <p>最终得分: <span id="finalScore">0</span></p>
                    <p>收集金币: <span id="finalCoins">0</span></p>
                    <button id="restartButton" class="mario-button">重新开始</button>
                    <button id="menuButton" class="mario-button">返回菜单</button>
                </div>
            </div>
            
            <div id="pauseMenu" class="pause-menu hidden">
                <h2>游戏暂停</h2>
                <div class="pause-content">
                    <button id="resumeButton" class="mario-button">继续游戏</button>
                    <button id="mainMenuButton" class="mario-button">返回主菜单</button>
                </div>
            </div>
            
            <div id="levelComplete" class="level-complete hidden">
                <h2>🎉 关卡完成！🎉</h2>
                <div class="complete-content">
                    <p>恭喜通过关卡！</p>
                    <p>获得分数: <span id="levelScore">0</span></p>
                    <p>时间奖励: <span id="timeBonus">0</span></p>
                    <button id="nextLevelButton" class="mario-button">下一关</button>
                </div>
            </div>
        </div>
        
        <div class="game-instructions">
            <div class="instruction-section">
                <h3>🎯 游戏目标</h3>
                <p>帮助马里奥穿越危险的蘑菇王国，收集金币，消灭敌人，到达旗杆完成关卡！</p>
            </div>
            
            <div class="instruction-section">
                <h3>🍄 道具说明</h3>
                <div class="items-grid">
                    <div class="item">
                        <span class="item-icon">🍄</span>
                        <span class="item-desc">超级蘑菇 - 让马里奥变大变强</span>
                    </div>
                    <div class="item">
                        <span class="item-icon">🌸</span>
                        <span class="item-desc">火花道具 - 获得发射火球的能力</span>
                    </div>
                    <div class="item">
                        <span class="item-icon">⭐</span>
                        <span class="item-desc">星星道具 - 短时间无敌状态</span>
                    </div>
                    <div class="item">
                        <span class="item-icon">🟢</span>
                        <span class="item-desc">1UP蘑菇 - 获得额外生命</span>
                    </div>
                    <div class="item">
                        <span class="item-icon">🪙</span>
                        <span class="item-desc">金币 - 收集100个获得额外生命</span>
                    </div>
                    <div class="item">
                        <span class="item-icon">🧱</span>
                        <span class="item-desc">砖块 - 撞击可能获得道具</span>
                    </div>
                    <div class="item">
                        <span class="item-icon">❓</span>
                        <span class="item-desc">问号砖块 - 包含惊喜道具</span>
                    </div>
                </div>
            </div>
            
            <div class="instruction-section">
                <h3>👾 敌人介绍</h3>
                <div class="enemies-grid">
                    <div class="enemy">
                        <span class="enemy-icon">🟤</span>
                        <span class="enemy-desc">栗子怪 - 踩踏消灭，触碰会受伤</span>
                    </div>
                    <div class="enemy">
                        <span class="enemy-icon">🟢</span>
                        <span class="enemy-desc">乌龟 - 踩踏变龟壳，可踢击攻击其他敌人</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="game.js"></script>
</body>
</html>
