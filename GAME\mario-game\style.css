* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Courier New', monospace;
    background: linear-gradient(135deg, #87CEEB 0%, #98FB98 100%);
    color: #333;
    min-height: 100vh;
    overflow-x: auto;
}

.game-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 10px;
}

.game-header {
    text-align: center;
    margin-bottom: 10px;
    background: #000;
    color: #fff;
    padding: 10px;
    border-radius: 8px;
    border: 3px solid #fff;
}

.game-header h1 {
    font-size: 2em;
    margin-bottom: 10px;
    text-shadow: 2px 2px 0px #ff0000;
    letter-spacing: 2px;
}

.game-hud {
    display: flex;
    justify-content: space-around;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.hud-item {
    text-align: center;
    min-width: 80px;
}

.hud-item .label {
    display: block;
    font-size: 0.8em;
    color: #ccc;
    margin-bottom: 2px;
}

.hud-item .score,
.hud-item .time,
.hud-item .world {
    font-size: 1.2em;
    font-weight: bold;
    color: #fff;
}

.coins, .lives {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    font-size: 1.2em;
    font-weight: bold;
    color: #fff;
}

.coin-icon, .mario-icon {
    font-size: 1em;
}

.game-area {
    position: relative;
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
}

#gameCanvas {
    border: 4px solid #000;
    border-radius: 8px;
    background: linear-gradient(180deg, #87CEEB 0%, #87CEEB 60%, #228B22 60%, #32CD32 100%);
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
}

.game-menu, .game-over, .pause-menu, .level-complete {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(45deg, #ff6b6b, #ff8e53);
    border: 4px solid #000;
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.7);
    z-index: 10;
    color: #fff;
    min-width: 300px;
}

.game-menu h2, .game-over h2, .pause-menu h2, .level-complete h2 {
    font-size: 2em;
    margin-bottom: 20px;
    text-shadow: 2px 2px 0px #000;
}

.menu-content, .game-over-content, .pause-content, .complete-content {
    margin: 20px 0;
}

.controls-info {
    background: rgba(0, 0, 0, 0.3);
    padding: 15px;
    border-radius: 10px;
    margin: 15px 0;
    text-align: left;
}

.controls-info p {
    margin: 5px 0;
    font-size: 0.9em;
}

.mario-button {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: white;
    border: 3px solid #000;
    padding: 12px 24px;
    font-size: 1.1em;
    font-weight: bold;
    border-radius: 25px;
    cursor: pointer;
    margin: 8px;
    transition: all 0.3s ease;
    font-family: 'Courier New', monospace;
    text-transform: uppercase;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.mario-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
    background: linear-gradient(45deg, #5CBF60, #4CAF50);
}

.mario-button:active {
    transform: translateY(0);
}

.hidden {
    display: none !important;
}

.game-instructions {
    background: rgba(255, 255, 255, 0.9);
    border: 3px solid #000;
    border-radius: 15px;
    padding: 20px;
    margin-top: 20px;
}

.instruction-section {
    margin-bottom: 20px;
}

.instruction-section h3 {
    color: #ff0000;
    font-size: 1.3em;
    margin-bottom: 10px;
    text-shadow: 1px 1px 0px #000;
}

.items-grid, .enemies-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 10px;
    margin-top: 10px;
}

.item, .enemy {
    display: flex;
    align-items: center;
    gap: 10px;
    background: rgba(0, 0, 0, 0.1);
    padding: 8px;
    border-radius: 8px;
}

.item-icon, .enemy-icon {
    font-size: 1.5em;
    min-width: 30px;
    text-align: center;
}

.item-desc, .enemy-desc {
    font-size: 0.9em;
    color: #333;
}

/* 响应式设计 */
@media (max-width: 1100px) {
    #gameCanvas {
        width: 100%;
        height: auto;
        max-width: 1024px;
    }
}

@media (max-width: 768px) {
    .game-header h1 {
        font-size: 1.5em;
    }
    
    .game-hud {
        font-size: 0.9em;
    }
    
    .game-menu, .game-over, .pause-menu, .level-complete {
        padding: 20px;
        min-width: 280px;
    }
    
    .items-grid, .enemies-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .game-container {
        padding: 5px;
    }
    
    .game-hud {
        gap: 8px;
    }
    
    .hud-item {
        min-width: 60px;
    }
}
