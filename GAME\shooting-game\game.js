// 游戏主类
class SpaceShooterGame {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        
        // 游戏状态
        this.gameState = 'menu'; // menu, playing, paused, gameOver
        this.score = 0;
        this.lives = 3;
        this.level = 1;
        
        // 游戏对象数组
        this.player = null;
        this.bullets = [];
        this.enemies = [];
        this.particles = [];
        
        // 输入状态
        this.keys = {};
        
        // 游戏设置
        this.enemySpawnRate = 0.02;
        this.enemySpawnTimer = 0;
        
        // 初始化
        this.initializeEventListeners();
        this.initializeUI();
        
        // 开始游戏循环
        this.gameLoop();
    }
    
    initializeEventListeners() {
        // 键盘事件
        document.addEventListener('keydown', (e) => {
            this.keys[e.code] = true;
            this.handleKeyPress(e);
        });
        
        document.addEventListener('keyup', (e) => {
            this.keys[e.code] = false;
        });
        
        // 按钮事件
        document.getElementById('startButton').addEventListener('click', () => {
            this.startGame();
        });
        
        document.getElementById('restartButton').addEventListener('click', () => {
            this.restartGame();
        });
        
        document.getElementById('resumeButton').addEventListener('click', () => {
            this.resumeGame();
        });
        
        document.getElementById('mainMenuButton').addEventListener('click', () => {
            this.showMainMenu();
        });
    }
    
    initializeUI() {
        this.updateUI();
    }
    
    handleKeyPress(e) {
        switch(e.code) {
            case 'KeyP':
                if (this.gameState === 'playing') {
                    this.pauseGame();
                } else if (this.gameState === 'paused') {
                    this.resumeGame();
                }
                break;
            case 'Escape':
                if (this.gameState === 'playing' || this.gameState === 'paused') {
                    this.showMainMenu();
                }
                break;
        }
    }
    
    startGame() {
        this.gameState = 'playing';
        this.score = 0;
        this.lives = 3;
        this.level = 1;
        
        // 重置游戏对象
        this.bullets = [];
        this.enemies = [];
        this.particles = [];
        
        // 创建玩家
        this.player = new Player(this.canvas.width / 2, this.canvas.height - 50);
        
        // 隐藏菜单
        document.getElementById('gameMenu').classList.add('hidden');
        document.getElementById('gameOver').classList.add('hidden');
        document.getElementById('pauseMenu').classList.add('hidden');
        
        this.updateUI();
    }
    
    pauseGame() {
        this.gameState = 'paused';
        document.getElementById('pauseMenu').classList.remove('hidden');
    }
    
    resumeGame() {
        this.gameState = 'playing';
        document.getElementById('pauseMenu').classList.add('hidden');
    }
    
    restartGame() {
        this.startGame();
    }
    
    showMainMenu() {
        this.gameState = 'menu';
        document.getElementById('gameMenu').classList.remove('hidden');
        document.getElementById('gameOver').classList.add('hidden');
        document.getElementById('pauseMenu').classList.add('hidden');
    }
    
    gameOver() {
        this.gameState = 'gameOver';
        document.getElementById('finalScore').textContent = this.score;
        document.getElementById('gameOver').classList.remove('hidden');
    }
    
    updateUI() {
        document.getElementById('score').textContent = this.score;
        document.getElementById('lives').textContent = this.lives;
        document.getElementById('level').textContent = this.level;
    }
    
    update() {
        if (this.gameState !== 'playing') return;
        
        // 更新玩家
        if (this.player) {
            this.player.update(this.keys);
            
            // 检查玩家边界
            if (this.player.x < 0) this.player.x = 0;
            if (this.player.x > this.canvas.width - this.player.width) {
                this.player.x = this.canvas.width - this.player.width;
            }
            if (this.player.y < 0) this.player.y = 0;
            if (this.player.y > this.canvas.height - this.player.height) {
                this.player.y = this.canvas.height - this.player.height;
            }
        }
        
        // 更新子弹
        this.bullets = this.bullets.filter(bullet => {
            bullet.update();
            return bullet.y > -bullet.height;
        });
        
        // 更新敌人
        this.enemies = this.enemies.filter(enemy => {
            enemy.update();
            return enemy.y < this.canvas.height + enemy.height;
        });
        
        // 更新粒子效果
        this.particles = this.particles.filter(particle => {
            particle.update();
            return particle.life > 0;
        });
        
        // 生成敌人
        this.spawnEnemies();
        
        // 碰撞检测
        this.checkCollisions();
        
        // 检查游戏结束条件
        if (this.lives <= 0) {
            this.gameOver();
        }
    }
    
    spawnEnemies() {
        this.enemySpawnTimer += this.enemySpawnRate + (this.level - 1) * 0.005;

        if (this.enemySpawnTimer >= 1) {
            this.enemySpawnTimer = 0;

            const x = Math.random() * (this.canvas.width - 40);
            let enemyType = 'basic';

            // 根据等级调整敌人类型概率
            const rand = Math.random();
            if (this.level >= 3 && rand < 0.2) {
                enemyType = 'boss';
            } else if (this.level >= 2 && rand < 0.4) {
                enemyType = 'fast';
            }

            this.enemies.push(new Enemy(x, -40, enemyType));
        }
    }

    // 检查等级提升
    checkLevelUp() {
        const newLevel = Math.floor(this.score / 100) + 1;
        if (newLevel > this.level) {
            this.level = newLevel;
            this.updateUI();

            // 等级提升奖励
            if (this.level % 3 === 0) {
                this.lives = Math.min(this.lives + 1, 5); // 最多5条命
            }
        }
    }

    // 添加音效支持（简单的音频上下文）
    playSound(frequency, duration, type = 'sine') {
        if (!this.audioContext) {
            try {
                this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            } catch (e) {
                return; // 如果不支持音频，静默失败
            }
        }

        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);

        oscillator.frequency.value = frequency;
        oscillator.type = type;

        gainNode.gain.setValueAtTime(0.1, this.audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + duration);

        oscillator.start(this.audioContext.currentTime);
        oscillator.stop(this.audioContext.currentTime + duration);
    }
    
    checkCollisions() {
        // 子弹与敌人的碰撞
        for (let i = this.bullets.length - 1; i >= 0; i--) {
            for (let j = this.enemies.length - 1; j >= 0; j--) {
                if (this.checkCollision(this.bullets[i], this.enemies[j])) {
                    // 创建爆炸效果
                    this.createExplosion(this.enemies[j].x + this.enemies[j].width / 2,
                                       this.enemies[j].y + this.enemies[j].height / 2);

                    // 播放击中音效
                    this.playSound(800, 0.1, 'square');

                    // Boss敌人需要多次击中
                    if (this.enemies[j].type === 'boss') {
                        this.enemies[j].health--;
                        if (this.enemies[j].health <= 0) {
                            this.score += this.enemies[j].points;
                            this.enemies.splice(j, 1);
                        }
                    } else {
                        // 增加分数
                        this.score += this.enemies[j].points;
                        this.enemies.splice(j, 1);
                    }

                    // 移除子弹
                    this.bullets.splice(i, 1);

                    // 检查等级提升
                    this.checkLevelUp();
                    this.updateUI();
                    break;
                }
            }
        }
        
        // 玩家与敌人的碰撞（只有在非无敌状态下才检测）
        if (this.player && this.player.invulnerable <= 0) {
            for (let i = this.enemies.length - 1; i >= 0; i--) {
                if (this.checkCollision(this.player, this.enemies[i])) {
                    // 创建爆炸效果
                    this.createExplosion(this.enemies[i].x + this.enemies[i].width / 2,
                                       this.enemies[i].y + this.enemies[i].height / 2);

                    // 播放受伤音效
                    this.playSound(200, 0.3, 'sawtooth');

                    // 减少生命值
                    this.lives--;

                    // 移除敌人
                    this.enemies.splice(i, 1);

                    // 添加无敌时间
                    this.player.invulnerable = 60; // 60帧无敌时间

                    this.updateUI();
                }
            }
        }
    }
    
    checkCollision(obj1, obj2) {
        return obj1.x < obj2.x + obj2.width &&
               obj1.x + obj1.width > obj2.x &&
               obj1.y < obj2.y + obj2.height &&
               obj1.y + obj1.height > obj2.y;
    }
    
    createExplosion(x, y) {
        for (let i = 0; i < 8; i++) {
            this.particles.push(new Particle(x, y));
        }
    }
    
    render() {
        // 清空画布
        this.ctx.fillStyle = 'rgba(0, 0, 17, 0.1)';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        // 绘制星空背景
        this.drawStars();
        
        if (this.gameState === 'playing' || this.gameState === 'paused') {
            // 绘制玩家
            if (this.player) {
                this.player.render(this.ctx);
            }
            
            // 绘制子弹
            this.bullets.forEach(bullet => bullet.render(this.ctx));
            
            // 绘制敌人
            this.enemies.forEach(enemy => enemy.render(this.ctx));
            
            // 绘制粒子效果
            this.particles.forEach(particle => particle.render(this.ctx));
        }
    }
    
    drawStars() {
        this.ctx.fillStyle = '#ffffff';
        for (let i = 0; i < 50; i++) {
            const x = (i * 37) % this.canvas.width;
            const y = (i * 73 + Date.now() * 0.01) % this.canvas.height;
            const size = Math.sin(i) * 2 + 1;
            this.ctx.fillRect(x, y, size, size);
        }
    }
    
    gameLoop() {
        this.update();
        this.render();
        requestAnimationFrame(() => this.gameLoop());
    }
}

// 玩家类
class Player {
    constructor(x, y) {
        this.x = x;
        this.y = y;
        this.width = 40;
        this.height = 40;
        this.speed = 5;
        this.shootCooldown = 0;
        this.maxShootCooldown = 10;
        this.invulnerable = 0; // 无敌时间
    }

    update(keys) {
        // 移动控制
        if (keys['KeyW'] || keys['ArrowUp']) {
            this.y -= this.speed;
        }
        if (keys['KeyS'] || keys['ArrowDown']) {
            this.y += this.speed;
        }
        if (keys['KeyA'] || keys['ArrowLeft']) {
            this.x -= this.speed;
        }
        if (keys['KeyD'] || keys['ArrowRight']) {
            this.x += this.speed;
        }

        // 射击控制
        if (keys['Space'] && this.shootCooldown <= 0) {
            this.shoot();
            this.shootCooldown = this.maxShootCooldown;
        }

        if (this.shootCooldown > 0) {
            this.shootCooldown--;
        }

        // 更新无敌时间
        if (this.invulnerable > 0) {
            this.invulnerable--;
        }
    }

    shoot() {
        // 添加子弹到游戏中
        if (window.game) {
            window.game.bullets.push(new Bullet(this.x + this.width / 2 - 2, this.y, -8));
            // 播放射击音效
            window.game.playSound(1000, 0.1, 'square');
        }
    }

    render(ctx) {
        // 无敌时闪烁效果
        if (this.invulnerable > 0 && Math.floor(this.invulnerable / 5) % 2 === 0) {
            return; // 跳过渲染实现闪烁
        }

        // 绘制飞船主体
        ctx.fillStyle = '#00ffff';
        ctx.fillRect(this.x + 15, this.y + 10, 10, 25);

        // 绘制飞船翅膀
        ctx.fillStyle = '#0080ff';
        ctx.fillRect(this.x, this.y + 20, 15, 10);
        ctx.fillRect(this.x + 25, this.y + 20, 15, 10);

        // 绘制飞船头部
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(this.x + 17, this.y, 6, 15);

        // 绘制引擎火焰
        if (Math.random() > 0.5) {
            ctx.fillStyle = '#ff4400';
            ctx.fillRect(this.x + 16, this.y + 35, 8, 5);
            ctx.fillStyle = '#ffaa00';
            ctx.fillRect(this.x + 18, this.y + 37, 4, 3);
        }
    }
}

// 子弹类
class Bullet {
    constructor(x, y, speedY) {
        this.x = x;
        this.y = y;
        this.width = 4;
        this.height = 10;
        this.speedY = speedY;
    }

    update() {
        this.y += this.speedY;
    }

    render(ctx) {
        ctx.fillStyle = '#ffff00';
        ctx.fillRect(this.x, this.y, this.width, this.height);

        // 添加发光效果
        ctx.shadowColor = '#ffff00';
        ctx.shadowBlur = 5;
        ctx.fillRect(this.x + 1, this.y + 2, 2, 6);
        ctx.shadowBlur = 0;
    }
}

// 敌人类
class Enemy {
    constructor(x, y, type = 'basic') {
        this.x = x;
        this.y = y;
        this.type = type;
        this.movePattern = Math.random() * Math.PI * 2;

        if (type === 'basic') {
            this.width = 30;
            this.height = 30;
            this.speed = 2;
            this.points = 10;
            this.color = '#ff0000';
            this.health = 1;
        } else if (type === 'fast') {
            this.width = 25;
            this.height = 25;
            this.speed = 4;
            this.points = 20;
            this.color = '#ff8800';
            this.health = 1;
        } else if (type === 'boss') {
            this.width = 60;
            this.height = 60;
            this.speed = 1;
            this.points = 100;
            this.color = '#ff00ff';
            this.health = 5;
            this.maxHealth = 5;
        }
    }

    update() {
        this.y += this.speed;

        // 不同类型的移动模式
        if (this.type === 'fast') {
            this.x += Math.sin(this.movePattern + this.y * 0.01) * 2;
            this.movePattern += 0.1;
        } else if (this.type === 'boss') {
            // Boss左右移动
            this.x += Math.sin(this.movePattern) * 3;
            this.movePattern += 0.05;

            // 限制Boss在屏幕内
            if (this.x < 0) this.x = 0;
            if (this.x > 800 - this.width) this.x = 800 - this.width;
        }
    }

    render(ctx) {
        // 绘制敌人主体
        ctx.fillStyle = this.color;
        ctx.fillRect(this.x, this.y, this.width, this.height);

        // 绘制敌人细节
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(this.x + 5, this.y + 5, this.width - 10, this.height - 10);

        ctx.fillStyle = this.color;
        ctx.fillRect(this.x + 10, this.y + 10, this.width - 20, this.height - 20);

        // Boss敌人的血量条
        if (this.type === 'boss' && this.health < this.maxHealth) {
            const barWidth = this.width;
            const barHeight = 6;
            const healthPercent = this.health / this.maxHealth;

            // 血量条背景
            ctx.fillStyle = '#333333';
            ctx.fillRect(this.x, this.y - 10, barWidth, barHeight);

            // 血量条
            ctx.fillStyle = healthPercent > 0.5 ? '#00ff00' : healthPercent > 0.25 ? '#ffff00' : '#ff0000';
            ctx.fillRect(this.x, this.y - 10, barWidth * healthPercent, barHeight);
        }

        // 添加发光效果
        ctx.shadowColor = this.color;
        ctx.shadowBlur = this.type === 'boss' ? 8 : 3;
        ctx.fillRect(this.x + 2, this.y + 2, this.width - 4, this.height - 4);
        ctx.shadowBlur = 0;
    }
}

// 粒子类（爆炸效果）
class Particle {
    constructor(x, y) {
        this.x = x;
        this.y = y;
        this.speedX = (Math.random() - 0.5) * 8;
        this.speedY = (Math.random() - 0.5) * 8;
        this.life = 30;
        this.maxLife = 30;
        this.size = Math.random() * 4 + 2;
    }

    update() {
        this.x += this.speedX;
        this.y += this.speedY;
        this.life--;
        this.speedX *= 0.98;
        this.speedY *= 0.98;
    }

    render(ctx) {
        const alpha = this.life / this.maxLife;
        ctx.fillStyle = `rgba(255, ${Math.floor(100 + alpha * 155)}, 0, ${alpha})`;
        ctx.fillRect(this.x, this.y, this.size, this.size);
    }
}

// 当页面加载完成后启动游戏
document.addEventListener('DOMContentLoaded', () => {
    window.game = new SpaceShooterGame();
});
