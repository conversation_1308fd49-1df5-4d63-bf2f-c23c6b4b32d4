<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>太空射击游戏</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="game-container">
        <div class="game-header">
            <h1>太空射击游戏</h1>
            <div class="game-info">
                <div class="score">得分: <span id="score">0</span></div>
                <div class="lives">生命: <span id="lives">3</span></div>
                <div class="level">等级: <span id="level">1</span></div>
            </div>
        </div>
        
        <div class="game-area">
            <canvas id="gameCanvas" width="800" height="600"></canvas>
            
            <div id="gameMenu" class="game-menu">
                <h2>太空射击游戏</h2>
                <p>使用 WASD 或方向键移动飞船</p>
                <p>按空格键射击</p>
                <p>消灭敌人获得分数！</p>
                <button id="startButton" class="game-button">开始游戏</button>
            </div>
            
            <div id="gameOver" class="game-over hidden">
                <h2>游戏结束</h2>
                <p>最终得分: <span id="finalScore">0</span></p>
                <button id="restartButton" class="game-button">重新开始</button>
            </div>
            
            <div id="pauseMenu" class="pause-menu hidden">
                <h2>游戏暂停</h2>
                <button id="resumeButton" class="game-button">继续游戏</button>
                <button id="mainMenuButton" class="game-button">返回主菜单</button>
            </div>
        </div>
        
        <div class="game-controls">
            <div class="controls-info">
                <h3>游戏控制</h3>
                <div class="control-item">
                    <span class="key">WASD / 方向键</span>
                    <span class="action">移动飞船</span>
                </div>
                <div class="control-item">
                    <span class="key">空格键</span>
                    <span class="action">射击</span>
                </div>
                <div class="control-item">
                    <span class="key">P</span>
                    <span class="action">暂停/继续</span>
                </div>
                <div class="control-item">
                    <span class="key">ESC</span>
                    <span class="action">返回菜单</span>
                </div>
            </div>
        </div>
    </div>
    
    <script src="game.js"></script>
</body>
</html>
