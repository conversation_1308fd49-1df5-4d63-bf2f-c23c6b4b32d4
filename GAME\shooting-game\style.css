* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
    color: #ffffff;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

.game-container {
    max-width: 1200px;
    width: 100%;
    padding: 20px;
}

.game-header {
    text-align: center;
    margin-bottom: 20px;
}

.game-header h1 {
    font-size: 2.5em;
    color: #00ffff;
    text-shadow: 0 0 20px #00ffff;
    margin-bottom: 15px;
}

.game-info {
    display: flex;
    justify-content: center;
    gap: 30px;
    font-size: 1.2em;
    font-weight: bold;
}

.game-info > div {
    background: rgba(0, 255, 255, 0.1);
    padding: 8px 16px;
    border-radius: 20px;
    border: 2px solid #00ffff;
    box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
}

.game-area {
    position: relative;
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
}

#gameCanvas {
    border: 3px solid #00ffff;
    border-radius: 10px;
    background: linear-gradient(180deg, #000011 0%, #000033 100%);
    box-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
}

.game-menu, .game-over, .pause-menu {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.9);
    border: 2px solid #00ffff;
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    box-shadow: 0 0 30px rgba(0, 255, 255, 0.7);
    z-index: 10;
}

.game-menu h2, .game-over h2, .pause-menu h2 {
    color: #00ffff;
    font-size: 2em;
    margin-bottom: 20px;
    text-shadow: 0 0 10px #00ffff;
}

.game-menu p {
    margin: 10px 0;
    font-size: 1.1em;
    color: #cccccc;
}

.game-button {
    background: linear-gradient(45deg, #00ffff, #0080ff);
    color: #000;
    border: none;
    padding: 12px 24px;
    font-size: 1.1em;
    font-weight: bold;
    border-radius: 25px;
    cursor: pointer;
    margin: 10px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 255, 255, 0.3);
}

.game-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 255, 255, 0.5);
    background: linear-gradient(45deg, #00cccc, #0066cc);
}

.game-button:active {
    transform: translateY(0);
}

.hidden {
    display: none !important;
}

.game-controls {
    background: rgba(0, 255, 255, 0.1);
    border: 2px solid #00ffff;
    border-radius: 15px;
    padding: 20px;
    margin-top: 20px;
}

.game-controls h3 {
    color: #00ffff;
    text-align: center;
    margin-bottom: 15px;
    font-size: 1.3em;
}

.control-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 8px 0;
    padding: 5px 0;
}

.key {
    background: #333;
    color: #00ffff;
    padding: 4px 8px;
    border-radius: 5px;
    font-family: monospace;
    font-weight: bold;
    min-width: 100px;
    text-align: center;
}

.action {
    color: #cccccc;
}

/* 响应式设计 */
@media (max-width: 900px) {
    .game-container {
        padding: 10px;
    }
    
    #gameCanvas {
        width: 100%;
        height: auto;
        max-width: 800px;
    }
    
    .game-info {
        flex-direction: column;
        gap: 10px;
    }
    
    .game-header h1 {
        font-size: 2em;
    }
}

@media (max-width: 600px) {
    .game-header h1 {
        font-size: 1.5em;
    }
    
    .game-info {
        font-size: 1em;
    }
    
    .game-menu, .game-over, .pause-menu {
        padding: 20px;
        width: 90%;
    }
}
