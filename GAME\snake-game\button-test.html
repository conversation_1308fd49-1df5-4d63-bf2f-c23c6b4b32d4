<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>按钮测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            padding: 50px;
        }
        button {
            padding: 15px 30px;
            font-size: 18px;
            margin: 10px;
            cursor: pointer;
        }
        .status {
            margin: 20px;
            padding: 10px;
            background: #f0f0f0;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>按钮功能测试</h1>
    
    <div class="status">
        <p>游戏状态: <span id="gameState">waiting</span></p>
        <p>按钮点击次数: <span id="clickCount">0</span></p>
    </div>
    
    <button type="button" id="testButton">开始游戏</button>
    <button type="button" onclick="simulateGameOver()">模拟游戏结束</button>
    <button type="button" onclick="resetTest()">重置测试</button>
    
    <div id="log"></div>
    
    <script>
        let gameState = 'waiting';
        let clickCount = 0;
        
        const button = document.getElementById('testButton');
        const stateSpan = document.getElementById('gameState');
        const countSpan = document.getElementById('clickCount');
        const log = document.getElementById('log');
        
        function updateDisplay() {
            stateSpan.textContent = gameState;
            countSpan.textContent = clickCount;
            
            if (gameState === 'waiting') {
                button.textContent = '开始游戏';
            } else if (gameState === 'playing') {
                button.textContent = '游戏进行中';
            } else if (gameState === 'gameOver') {
                button.textContent = '重新开始';
            }
        }
        
        function addLog(message) {
            const p = document.createElement('p');
            p.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
            log.appendChild(p);
        }
        
        button.addEventListener('click', function(e) {
            e.preventDefault();
            clickCount++;
            addLog(`按钮被点击，当前状态: ${gameState}`);
            
            if (gameState === 'waiting') {
                gameState = 'playing';
                addLog('游戏开始');
            } else if (gameState === 'gameOver') {
                gameState = 'waiting';
                addLog('游戏重置');
            }
            
            updateDisplay();
        });
        
        function simulateGameOver() {
            gameState = 'gameOver';
            addLog('模拟游戏结束');
            updateDisplay();
        }
        
        function resetTest() {
            gameState = 'waiting';
            clickCount = 0;
            log.innerHTML = '';
            addLog('测试重置');
            updateDisplay();
        }
        
        // 初始化显示
        updateDisplay();
        addLog('测试页面加载完成');
    </script>
</body>
</html>
