<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>贪吃蛇游戏</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="game-container">
        <div class="game-header">
            <h1>贪吃蛇游戏</h1>
            <div class="game-info">
                <div class="score">
                    <span>分数: </span>
                    <span id="score">0</span>
                </div>
                <div class="high-score">
                    <span>最高分: </span>
                    <span id="high-score">0</span>
                </div>
            </div>
        </div>
        
        <div class="game-area">
            <canvas id="gameCanvas" width="400" height="400"></canvas>
            <div class="game-overlay" id="gameOverlay">
                <div class="overlay-content">
                    <h2 id="overlayTitle">开始游戏</h2>
                    <p id="overlayMessage">按空格键开始游戏</p>
                    <button type="button" id="startButton">开始游戏</button>
                </div>
            </div>
        </div>
        
        <div class="game-controls">
            <div class="control-instructions">
                <h3>游戏控制</h3>
                <div class="controls-grid">
                    <div class="control-item">
                        <span class="key">↑</span>
                        <span>向上</span>
                    </div>
                    <div class="control-item">
                        <span class="key">↓</span>
                        <span>向下</span>
                    </div>
                    <div class="control-item">
                        <span class="key">←</span>
                        <span>向左</span>
                    </div>
                    <div class="control-item">
                        <span class="key">→</span>
                        <span>向右</span>
                    </div>
                    <div class="control-item">
                        <span class="key">空格</span>
                        <span>暂停/继续</span>
                    </div>
                    <div class="control-item">
                        <span class="key">R</span>
                        <span>重新开始</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="script.js"></script>
</body>
</html>
