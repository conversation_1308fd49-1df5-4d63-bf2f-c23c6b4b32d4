class SnakeGame {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.scoreElement = document.getElementById('score');
        this.highScoreElement = document.getElementById('high-score');
        this.gameOverlay = document.getElementById('gameOverlay');
        this.overlayTitle = document.getElementById('overlayTitle');
        this.overlayMessage = document.getElementById('overlayMessage');
        this.startButton = document.getElementById('startButton');


        
        // 游戏配置
        this.gridSize = 20;
        this.tileCount = this.canvas.width / this.gridSize;
        
        // 游戏状态
        this.gameState = 'waiting'; // waiting, playing, paused, gameOver
        this.score = 0;
        this.highScore = localStorage.getItem('snakeHighScore') || 0;
        
        // 蛇的初始状态
        this.snake = [
            { x: 10, y: 10 }
        ];
        this.direction = { x: 0, y: 0 };
        this.nextDirection = { x: 0, y: 0 };
        
        // 食物
        this.food = this.generateFood();
        
        this.init();
    }
    
    init() {
        this.updateHighScoreDisplay();
        this.setupEventListeners();
        this.draw(); // 初始绘制
        this.showOverlay('开始游戏', '使用方向键控制蛇的移动，吃到食物获得分数！');
    }
    
    setupEventListeners() {
        // 键盘事件
        document.addEventListener('keydown', (e) => this.handleKeyPress(e));
        
        // 开始按钮
        this.startButton.addEventListener('click', (e) => {
            e.preventDefault();
            this.handleStartButton();
        });
        
        // 防止方向键滚动页面
        window.addEventListener('keydown', (e) => {
            if(['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', ' '].includes(e.key)) {
                e.preventDefault();
            }
        });
    }
    
    handleKeyPress(e) {
        if (this.gameState === 'waiting') {
            if (e.key === ' ') {
                this.startGame();
            }
            return;
        }
        
        if (this.gameState === 'playing') {
            switch(e.key) {
                case 'ArrowUp':
                    if (this.direction.y === 0) {
                        this.nextDirection = { x: 0, y: -1 };
                    }
                    break;
                case 'ArrowDown':
                    if (this.direction.y === 0) {
                        this.nextDirection = { x: 0, y: 1 };
                    }
                    break;
                case 'ArrowLeft':
                    if (this.direction.x === 0) {
                        this.nextDirection = { x: -1, y: 0 };
                    }
                    break;
                case 'ArrowRight':
                    if (this.direction.x === 0) {
                        this.nextDirection = { x: 1, y: 0 };
                    }
                    break;
                case ' ':
                    this.pauseGame();
                    break;
            }
        }
        
        if (e.key === 'r' || e.key === 'R') {
            this.resetGame();
        }
        
        if (this.gameState === 'paused' && e.key === ' ') {
            this.resumeGame();
        }
        
        if (this.gameState === 'gameOver' && e.key === ' ') {
            this.resetGame();
        }
    }

    handleStartButton() {
        if (this.gameState === 'waiting') {
            this.startGame();
        } else if (this.gameState === 'gameOver') {
            this.resetGame();
        }
    }

    startGame() {
        this.gameState = 'playing';
        this.hideOverlay();
        this.draw(); // 立即绘制一次
        this.gameLoop();
    }
    
    pauseGame() {
        this.gameState = 'paused';
        this.showOverlay('游戏暂停', '按空格键继续游戏');
    }
    
    resumeGame() {
        this.gameState = 'playing';
        this.hideOverlay();
        this.gameLoop();
    }
    
    resetGame() {
        this.gameState = 'waiting';
        this.score = 0;
        this.snake = [{ x: 10, y: 10 }];
        this.direction = { x: 0, y: 0 };
        this.nextDirection = { x: 0, y: 0 };
        this.food = this.generateFood();
        this.updateScore();
        this.draw();
        this.showOverlay('开始游戏', '按空格键开始游戏');
    }
    
    gameOver() {
        this.gameState = 'gameOver';
        if (this.score > this.highScore) {
            this.highScore = this.score;
            localStorage.setItem('snakeHighScore', this.highScore);
            this.updateHighScoreDisplay();
            this.showOverlay('新纪录！', `恭喜！你创造了新的最高分：${this.score}分`);
        } else {
            this.showOverlay('游戏结束', `你的分数：${this.score}分\n按空格键重新开始`);
        }
    }
    
    generateFood() {
        let food;
        do {
            food = {
                x: Math.floor(Math.random() * this.tileCount),
                y: Math.floor(Math.random() * this.tileCount)
            };
        } while (this.snake.some(segment => segment.x === food.x && segment.y === food.y));
        return food;
    }
    
    update() {
        if (this.gameState !== 'playing') return;

        // 更新方向
        this.direction = { ...this.nextDirection };

        // 如果没有方向，不移动
        if (this.direction.x === 0 && this.direction.y === 0) {
            return;
        }

        // 移动蛇头
        const head = { ...this.snake[0] };
        head.x += this.direction.x;
        head.y += this.direction.y;
        
        // 检查墙壁碰撞
        if (head.x < 0 || head.x >= this.tileCount || head.y < 0 || head.y >= this.tileCount) {
            this.gameOver();
            return;
        }
        
        // 检查自身碰撞
        if (this.snake.some(segment => segment.x === head.x && segment.y === head.y)) {
            this.gameOver();
            return;
        }
        
        this.snake.unshift(head);
        
        // 检查是否吃到食物
        if (head.x === this.food.x && head.y === this.food.y) {
            this.score += 10;
            this.updateScore();
            this.food = this.generateFood();
        } else {
            this.snake.pop();
        }
    }
    
    draw() {
        // 清空画布
        this.ctx.fillStyle = '#2d3748';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        // 绘制蛇
        this.ctx.fillStyle = '#48bb78';
        this.snake.forEach((segment, index) => {
            if (index === 0) {
                // 蛇头用不同颜色
                this.ctx.fillStyle = '#38a169';
            } else {
                this.ctx.fillStyle = '#48bb78';
            }
            this.ctx.fillRect(
                segment.x * this.gridSize + 1,
                segment.y * this.gridSize + 1,
                this.gridSize - 2,
                this.gridSize - 2
            );
        });
        
        // 绘制食物
        this.ctx.fillStyle = '#f56565';
        this.ctx.fillRect(
            this.food.x * this.gridSize + 1,
            this.food.y * this.gridSize + 1,
            this.gridSize - 2,
            this.gridSize - 2
        );
    }
    
    updateScore() {
        this.scoreElement.textContent = this.score;
    }
    
    updateHighScoreDisplay() {
        this.highScoreElement.textContent = this.highScore;
    }
    
    showOverlay(title, message) {
        this.overlayTitle.textContent = title;
        this.overlayMessage.textContent = message;
        this.gameOverlay.classList.remove('hidden');
    }
    
    hideOverlay() {
        this.gameOverlay.classList.add('hidden');
    }
    
    gameLoop() {
        if (this.gameState === 'playing') {
            this.update();
            this.draw();
            setTimeout(() => this.gameLoop(), 150);
        }
    }
}

// 启动游戏
document.addEventListener('DOMContentLoaded', () => {
    new SnakeGame();
});
