* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #333;
}

.game-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    text-align: center;
    max-width: 600px;
    width: 100%;
}

.game-header {
    margin-bottom: 20px;
}

.game-header h1 {
    color: #4a5568;
    margin-bottom: 15px;
    font-size: 2.5em;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.game-info {
    display: flex;
    justify-content: space-around;
    background: #f7fafc;
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.score, .high-score {
    font-size: 1.2em;
    font-weight: bold;
    color: #2d3748;
}

.game-area {
    position: relative;
    display: inline-block;
    margin-bottom: 30px;
}

#gameCanvas {
    border: 4px solid #4a5568;
    border-radius: 10px;
    background: #2d3748;
    display: block;
    position: relative;
    z-index: 1;
}

.game-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 10px;
    color: white;
    z-index: 2;
}

.overlay-content {
    text-align: center;
    padding: 20px;
}

.overlay-content h2 {
    font-size: 2em;
    margin-bottom: 10px;
    color: #fff;
}

.overlay-content p {
    font-size: 1.2em;
    margin-bottom: 20px;
    color: #cbd5e0;
}

#startButton {
    background: #48bb78;
    color: white;
    border: none;
    padding: 12px 24px;
    font-size: 1.1em;
    border-radius: 8px;
    cursor: pointer;
    transition: background 0.3s ease;
}

#startButton:hover {
    background: #38a169;
}

.game-controls {
    background: #f7fafc;
    padding: 20px;
    border-radius: 10px;
}

.game-controls h3 {
    color: #2d3748;
    margin-bottom: 15px;
    font-size: 1.3em;
}

.controls-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
}

.control-item {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 8px;
    background: white;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.key {
    background: #4a5568;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.9em;
    font-weight: bold;
    min-width: 40px;
    text-align: center;
}

.hidden {
    display: none !important;
}

@media (max-width: 600px) {
    .game-container {
        margin: 20px;
        padding: 20px;
    }
    
    #gameCanvas {
        width: 100%;
        height: auto;
        max-width: 350px;
    }
    
    .game-info {
        flex-direction: column;
        gap: 10px;
    }
    
    .controls-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}
