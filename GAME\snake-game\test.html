<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Canvas测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            padding: 20px;
        }
        #testCanvas {
            border: 2px solid #000;
            background: #f0f0f0;
        }
        button {
            margin: 10px;
            padding: 10px 20px;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <h1>Canvas测试</h1>
    <canvas id="testCanvas" width="400" height="400"></canvas>
    <br>
    <button onclick="drawTest()">绘制测试</button>
    <button onclick="clearCanvas()">清空画布</button>
    
    <script>
        const canvas = document.getElementById('testCanvas');
        const ctx = canvas.getContext('2d');
        
        function drawTest() {
            console.log('Drawing test...');
            
            // 清空画布
            ctx.fillStyle = '#2d3748';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 绘制蛇
            ctx.fillStyle = '#48bb78';
            ctx.fillRect(200, 200, 20, 20);
            
            // 绘制食物
            ctx.fillStyle = '#f56565';
            ctx.fillRect(100, 100, 20, 20);
            
            console.log('Test drawing complete');
        }
        
        function clearCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }
        
        // 页面加载时绘制测试
        window.onload = function() {
            console.log('Page loaded, drawing initial test');
            drawTest();
        };
    </script>
</body>
</html>
