# 🎮 增强版俄罗斯方块

一个功能丰富的现代化俄罗斯方块游戏，包含多种增强功能和精美的视觉效果。

## ✨ 主要功能

### 🎯 核心游戏功能
- **经典俄罗斯方块玩法** - 7种不同形状的方块
- **下一个方块预览** - 提前查看下一个方块形状
- **保持方块功能** - 按H键保存当前方块供后续使用
- **幽灵方块预览** - 显示方块的落地位置
- **硬降功能** - 按空格键快速下降方块

### 📊 游戏统计
- **实时分数显示** - 包含等级和消除行数
- **最高分记录** - 自动保存并显示历史最高分
- **游戏时间统计** - 显示当前游戏进行时间
- **方块统计** - 显示总方块数和每分钟方块数

### 🎨 视觉效果
- **渐变背景动画** - 动态变化的背景色彩
- **方块高光效果** - 3D立体感的方块渲染
- **动画反馈** - 分数增加和等级提升动画
- **现代化UI设计** - 圆角、阴影和毛玻璃效果

### 🔧 游戏设置
- **音效开关** - 可开启/关闭游戏音效
- **幽灵方块开关** - 可显示/隐藏幽灵方块
- **游戏速度调节** - 慢速/正常/快速三种模式
- **设置持久化** - 自动保存用户偏好设置

### 🎵 音效系统
- **移动音效** - 方块移动时的反馈音
- **旋转音效** - 方块旋转时的音效
- **下降音效** - 硬降时的音效
- **消行音效** - 消除行时的庆祝音效
- **Tetris音效** - 一次消除4行的特殊音效
- **游戏结束音效** - 游戏结束提示音

## 🎮 操作说明

### 基本操作
- **← →** - 左右移动方块
- **↓** - 软降（加速下降）
- **↑** - 旋转方块
- **空格** - 硬降（瞬间下降到底部）
- **H** - 保持当前方块
- **P** - 暂停/继续游戏
- **R** - 重新开始游戏

### 游戏状态
- **等待状态** - 按空格键开始游戏
- **游戏中** - 正常游戏操作
- **暂停状态** - 按P键或空格键继续
- **游戏结束** - 按空格键重新开始

## 🏆 计分规则

### 基础分数
- **单行消除** - 100分 × 等级
- **双行消除** - 300分 × 等级
- **三行消除** - 500分 × 等级
- **四行消除(Tetris)** - 800分 × 等级 × 1.5倍奖励

### 额外分数
- **硬降奖励** - 每格2分
- **等级提升** - 每10行消除提升1级
- **速度加快** - 等级越高，下降速度越快

## 🚀 开始游戏

1. 打开 `index.html` 文件
2. 点击"开始游戏"按钮或按空格键
3. 使用方向键控制方块
4. 尽可能多地消除完整行获得高分！

## 🔧 技术特性

- **纯HTML5/CSS3/JavaScript** - 无需额外依赖
- **响应式设计** - 支持移动设备
- **本地存储** - 自动保存设置和最高分
- **Canvas渲染** - 流畅的游戏画面
- **Web Audio API** - 动态生成音效

## 📱 兼容性

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 11+
- ✅ Edge 79+
- ✅ 移动端浏览器

## 🎯 游戏技巧

1. **提前规划** - 利用下一个方块预览制定策略
2. **保持功能** - 合理使用保持功能储存有用的方块
3. **幽灵预览** - 利用幽灵方块精确定位
4. **消除技巧** - 尽量同时消除多行获得更高分数
5. **速度适应** - 随着等级提升适应更快的游戏节奏

享受游戏吧！🎉
