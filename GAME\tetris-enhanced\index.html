<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>增强版俄罗斯方块</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="game-container">
        <div class="game-header">
            <h1>🎮 增强版俄罗斯方块</h1>
            <div class="game-stats">
                <div class="stat-item">
                    <span class="stat-label">最高分</span>
                    <span class="stat-value" id="highScore">0</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">游戏时间</span>
                    <span class="stat-value" id="gameTime">00:00</span>
                </div>
            </div>
        </div>
        
        <div class="game-main">
            <div class="game-info">
                <div class="info-section">
                    <h3>分数</h3>
                    <div class="score-display" id="score">0</div>
                </div>
                
                <div class="info-section">
                    <h3>等级</h3>
                    <div class="level-display" id="level">1</div>
                </div>
                
                <div class="info-section">
                    <h3>行数</h3>
                    <div class="lines-display" id="lines">0</div>
                </div>
                
                <div class="info-section next-piece-section">
                    <h3>下一个</h3>
                    <canvas id="nextCanvas" width="100" height="100"></canvas>
                </div>

                <div class="info-section">
                    <h3>统计</h3>
                    <div class="stats-grid">
                        <div class="stat-row">
                            <span>总方块:</span>
                            <span id="totalPieces">0</span>
                        </div>
                        <div class="stat-row">
                            <span>每分钟:</span>
                            <span id="piecesPerMinute">0</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="game-area">
                <canvas id="gameCanvas" width="300" height="600"></canvas>
                <div class="game-overlay" id="gameOverlay">
                    <div class="overlay-content">
                        <h2 id="overlayTitle">🎯 开始游戏</h2>
                        <p id="overlayMessage">准备好挑战增强版俄罗斯方块了吗？</p>
                        <div class="overlay-buttons">
                            <button type="button" id="startButton" class="game-button primary">开始游戏</button>
                            <button type="button" id="settingsButton" class="game-button secondary">设置</button>
                        </div>
                    </div>
                </div>
                
                <!-- 设置面板 -->
                <div class="settings-panel" id="settingsPanel">
                    <div class="settings-content">
                        <h3>🔧 游戏设置</h3>
                        <div class="setting-item">
                            <label>
                                <input type="checkbox" id="soundEnabled" checked>
                                启用音效
                            </label>
                        </div>
                        <div class="setting-item">
                            <label>
                                <input type="checkbox" id="ghostPiece" checked>
                                显示幽灵方块
                            </label>
                        </div>
                        <div class="setting-item">
                            <label>
                                游戏速度:
                                <select id="gameSpeed">
                                    <option value="slow">慢速</option>
                                    <option value="normal" selected>正常</option>
                                    <option value="fast">快速</option>
                                </select>
                            </label>
                        </div>
                        <div class="settings-buttons">
                            <button type="button" id="saveSettings" class="game-button primary">保存</button>
                            <button type="button" id="closeSettings" class="game-button secondary">关闭</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="game-controls">
                <h3>🎮 游戏控制</h3>
                <div class="controls-grid">
                    <div class="control-item">
                        <span class="key">←</span>
                        <span>左移</span>
                    </div>
                    <div class="control-item">
                        <span class="key">→</span>
                        <span>右移</span>
                    </div>
                    <div class="control-item">
                        <span class="key">↓</span>
                        <span>软降</span>
                    </div>
                    <div class="control-item">
                        <span class="key">↑</span>
                        <span>旋转</span>
                    </div>
                    <div class="control-item">
                        <span class="key">空格</span>
                        <span>硬降/暂停</span>
                    </div>
                    <div class="control-item">
                        <span class="key">R</span>
                        <span>重新开始</span>
                    </div>
                    <div class="control-item">
                        <span class="key">H</span>
                        <span>保持方块</span>
                    </div>
                </div>
                
                <div class="hold-section">
                    <h4>保持方块</h4>
                    <canvas id="holdCanvas" width="80" height="80"></canvas>
                </div>
            </div>
        </div>
        
        <div class="game-footer">
            <p>🌟 增强版俄罗斯方块 - 消除完整行获得分数！支持保持方块、幽灵预览等功能</p>
        </div>
    </div>
    
    <script src="script.js"></script>
</body>
</html>
