class EnhancedTetrisGame {
    constructor() {
        // Canvas 元素
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.nextCanvas = document.getElementById('nextCanvas');
        this.nextCtx = this.nextCanvas.getContext('2d');
        this.holdCanvas = document.getElementById('holdCanvas');
        this.holdCtx = this.holdCanvas.getContext('2d');
        
        // UI 元素
        this.scoreElement = document.getElementById('score');
        this.levelElement = document.getElementById('level');
        this.linesElement = document.getElementById('lines');
        this.highScoreElement = document.getElementById('highScore');
        this.gameTimeElement = document.getElementById('gameTime');
        this.totalPiecesElement = document.getElementById('totalPieces');
        this.piecesPerMinuteElement = document.getElementById('piecesPerMinute');
        
        // 覆盖层和按钮
        this.gameOverlay = document.getElementById('gameOverlay');
        this.overlayTitle = document.getElementById('overlayTitle');
        this.overlayMessage = document.getElementById('overlayMessage');
        this.startButton = document.getElementById('startButton');
        this.settingsButton = document.getElementById('settingsButton');
        this.settingsPanel = document.getElementById('settingsPanel');
        
        // 设置元素
        this.soundEnabledCheckbox = document.getElementById('soundEnabled');
        this.ghostPieceCheckbox = document.getElementById('ghostPiece');
        this.gameSpeedSelect = document.getElementById('gameSpeed');
        this.saveSettingsButton = document.getElementById('saveSettings');
        this.closeSettingsButton = document.getElementById('closeSettings');
        
        // 游戏配置
        this.BOARD_WIDTH = 10;
        this.BOARD_HEIGHT = 20;
        this.BLOCK_SIZE = 30;
        
        // 游戏状态
        this.gameState = 'waiting'; // waiting, playing, paused, gameOver
        this.score = 0;
        this.level = 1;
        this.lines = 0;
        this.highScore = this.loadHighScore();
        this.dropTime = 0;
        this.dropInterval = 1000;
        this.gameStartTime = 0;
        this.totalPieces = 0;
        this.lastPieceTime = 0;
        
        // 游戏设置
        this.settings = {
            soundEnabled: true,
            ghostPiece: true,
            gameSpeed: 'normal'
        };
        
        // 游戏板面
        this.board = this.createBoard();
        
        // 方块相关
        this.currentPiece = null;
        this.nextPiece = null;
        this.holdPiece = null;
        this.canHold = true;
        this.ghostPiece = null;
        
        // 方块定义 - 增强版颜色
        this.pieces = {
            I: {
                shape: [
                    [0, 0, 0, 0],
                    [1, 1, 1, 1],
                    [0, 0, 0, 0],
                    [0, 0, 0, 0]
                ],
                color: '#00f5ff',
                shadowColor: 'rgba(0, 245, 255, 0.3)'
            },
            O: {
                shape: [
                    [1, 1],
                    [1, 1]
                ],
                color: '#ffff00',
                shadowColor: 'rgba(255, 255, 0, 0.3)'
            },
            T: {
                shape: [
                    [0, 1, 0],
                    [1, 1, 1],
                    [0, 0, 0]
                ],
                color: '#800080',
                shadowColor: 'rgba(128, 0, 128, 0.3)'
            },
            S: {
                shape: [
                    [0, 1, 1],
                    [1, 1, 0],
                    [0, 0, 0]
                ],
                color: '#00ff00',
                shadowColor: 'rgba(0, 255, 0, 0.3)'
            },
            Z: {
                shape: [
                    [1, 1, 0],
                    [0, 1, 1],
                    [0, 0, 0]
                ],
                color: '#ff0000',
                shadowColor: 'rgba(255, 0, 0, 0.3)'
            },
            J: {
                shape: [
                    [1, 0, 0],
                    [1, 1, 1],
                    [0, 0, 0]
                ],
                color: '#0000ff',
                shadowColor: 'rgba(0, 0, 255, 0.3)'
            },
            L: {
                shape: [
                    [0, 0, 1],
                    [1, 1, 1],
                    [0, 0, 0]
                ],
                color: '#ffa500',
                shadowColor: 'rgba(255, 165, 0, 0.3)'
            }
        };
        
        this.pieceTypes = Object.keys(this.pieces);
        
        // 音效系统
        this.sounds = {
            move: this.createSound(200, 0.1),
            rotate: this.createSound(300, 0.1),
            drop: this.createSound(150, 0.2),
            line: this.createSound(400, 0.3),
            tetris: this.createSound(500, 0.5),
            gameOver: this.createSound(100, 1.0)
        };
        
        this.loadSettings();
        this.init();
    }
    
    createBoard() {
        return Array(this.BOARD_HEIGHT).fill().map(() => Array(this.BOARD_WIDTH).fill(0));
    }
    
    createSound(frequency, duration) {
        return () => {
            if (!this.settings.soundEnabled) return;
            
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            oscillator.frequency.value = frequency;
            oscillator.type = 'square';
            
            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);
            
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + duration);
        };
    }
    
    loadSettings() {
        const saved = localStorage.getItem('tetrisSettings');
        if (saved) {
            this.settings = { ...this.settings, ...JSON.parse(saved) };
        }
        this.applySettings();
    }
    
    saveSettings() {
        localStorage.setItem('tetrisSettings', JSON.stringify(this.settings));
    }
    
    applySettings() {
        this.soundEnabledCheckbox.checked = this.settings.soundEnabled;
        this.ghostPieceCheckbox.checked = this.settings.ghostPiece;
        this.gameSpeedSelect.value = this.settings.gameSpeed;
        
        // 应用游戏速度
        const speedMultipliers = { slow: 1.5, normal: 1, fast: 0.7 };
        this.baseDropInterval = 1000 * speedMultipliers[this.settings.gameSpeed];
    }
    
    loadHighScore() {
        return parseInt(localStorage.getItem('tetrisHighScore') || '0');
    }
    
    saveHighScore() {
        if (this.score > this.highScore) {
            this.highScore = this.score;
            localStorage.setItem('tetrisHighScore', this.highScore.toString());
            this.highScoreElement.textContent = this.highScore;
            return true;
        }
        return false;
    }
    
    init() {
        this.setupEventListeners();
        this.updateDisplay();
        this.draw();
        this.drawNext();
        this.drawHold();
        this.showOverlay('🎯 开始游戏', '准备好挑战增强版俄罗斯方块了吗？');
    }
    
    setupEventListeners() {
        // 键盘事件
        document.addEventListener('keydown', (e) => this.handleKeyPress(e));
        
        // 按钮事件
        this.startButton.addEventListener('click', () => this.handleStartButton());
        this.settingsButton.addEventListener('click', () => this.showSettings());
        this.saveSettingsButton.addEventListener('click', () => this.handleSaveSettings());
        this.closeSettingsButton.addEventListener('click', () => this.hideSettings());
        
        // 防止方向键滚动页面
        window.addEventListener('keydown', (e) => {
            if(['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', ' '].includes(e.key)) {
                e.preventDefault();
            }
        });
    }
    
    handleKeyPress(e) {
        if (this.gameState === 'waiting') {
            if (e.key === ' ') {
                this.startGame();
            }
            return;
        }
        
        if (this.gameState === 'playing') {
            switch(e.key) {
                case 'ArrowLeft':
                    this.movePiece(-1, 0);
                    this.sounds.move();
                    break;
                case 'ArrowRight':
                    this.movePiece(1, 0);
                    this.sounds.move();
                    break;
                case 'ArrowDown':
                    this.movePiece(0, 1);
                    break;
                case 'ArrowUp':
                    this.rotatePiece();
                    this.sounds.rotate();
                    break;
                case ' ':
                    this.hardDrop();
                    break;
                case 'h':
                case 'H':
                    this.holdCurrentPiece();
                    break;
                case 'p':
                case 'P':
                    this.pauseGame();
                    break;
            }
        }
        
        if (e.key === 'r' || e.key === 'R') {
            this.resetGame();
        }
        
        if (this.gameState === 'paused' && (e.key === ' ' || e.key === 'p' || e.key === 'P')) {
            this.resumeGame();
        }
        
        if (this.gameState === 'gameOver' && e.key === ' ') {
            this.resetGame();
        }
    }
    
    handleStartButton() {
        if (this.gameState === 'waiting') {
            this.startGame();
        } else if (this.gameState === 'gameOver') {
            this.resetGame();
        }
    }
    
    showSettings() {
        this.settingsPanel.style.display = 'flex';
    }
    
    hideSettings() {
        this.settingsPanel.style.display = 'none';
    }
    
    handleSaveSettings() {
        this.settings.soundEnabled = this.soundEnabledCheckbox.checked;
        this.settings.ghostPiece = this.ghostPieceCheckbox.checked;
        this.settings.gameSpeed = this.gameSpeedSelect.value;

        this.saveSettings();
        this.applySettings();
        this.hideSettings();

        // 重新计算下降间隔
        if (this.gameState === 'playing') {
            this.updateDropInterval();
        }
    }

    startGame() {
        this.gameState = 'playing';
        this.gameStartTime = Date.now();
        this.hideOverlay();
        this.dropTime = Date.now();
        this.lastPieceTime = Date.now();
        this.updateDropInterval();
        this.spawnPiece();
        this.gameLoop();
    }

    pauseGame() {
        this.gameState = 'paused';
        this.showOverlay('⏸️ 游戏暂停', '按空格键或P键继续游戏');
    }

    resumeGame() {
        this.gameState = 'playing';
        this.hideOverlay();
        this.dropTime = Date.now();
        this.gameLoop();
    }

    resetGame() {
        this.gameState = 'waiting';
        this.score = 0;
        this.level = 1;
        this.lines = 0;
        this.totalPieces = 0;
        this.gameStartTime = 0;
        this.dropInterval = this.baseDropInterval || 1000;
        this.board = this.createBoard();
        this.currentPiece = null;
        this.nextPiece = null;
        this.holdPiece = null;
        this.canHold = true;
        this.updateDisplay();
        this.draw();
        this.drawNext();
        this.drawHold();
        this.showOverlay('🎯 开始游戏', '准备好挑战增强版俄罗斯方块了吗？');
    }

    gameOver() {
        this.gameState = 'gameOver';
        this.sounds.gameOver();

        const isNewHighScore = this.saveHighScore();
        const gameTime = this.formatTime(Date.now() - this.gameStartTime);

        let message = `你的分数：${this.score}分\n游戏时间：${gameTime}\n总方块数：${this.totalPieces}`;
        if (isNewHighScore) {
            message += '\n🎉 新的最高分！';
        }
        message += '\n\n按空格键重新开始';

        this.showOverlay('💀 游戏结束', message);
    }

    updateDropInterval() {
        const speedMultipliers = { slow: 1.5, normal: 1, fast: 0.7 };
        const baseInterval = 1000 * speedMultipliers[this.settings.gameSpeed];
        this.dropInterval = Math.max(50, baseInterval - (this.level - 1) * 50);
    }

    spawnPiece() {
        if (!this.nextPiece) {
            this.nextPiece = this.getRandomPiece();
        }

        this.currentPiece = this.nextPiece;
        this.nextPiece = this.getRandomPiece();
        this.canHold = true;
        this.totalPieces++;

        // 设置初始位置
        this.currentPiece.x = Math.floor((this.BOARD_WIDTH - this.currentPiece.shape[0].length) / 2);
        this.currentPiece.y = 0;

        // 检查游戏是否结束
        if (this.checkCollision(this.currentPiece, 0, 0)) {
            this.gameOver();
            return;
        }

        this.updateGhostPiece();
        this.drawNext();
        this.draw();
    }

    getRandomPiece() {
        const type = this.pieceTypes[Math.floor(Math.random() * this.pieceTypes.length)];
        const piece = this.pieces[type];
        return {
            shape: piece.shape.map(row => [...row]),
            color: piece.color,
            shadowColor: piece.shadowColor,
            x: 0,
            y: 0
        };
    }

    holdCurrentPiece() {
        if (!this.canHold || !this.currentPiece) return;

        if (this.holdPiece) {
            // 交换当前方块和保持方块
            const temp = this.holdPiece;
            this.holdPiece = { ...this.currentPiece };
            this.currentPiece = temp;

            // 重置位置
            this.currentPiece.x = Math.floor((this.BOARD_WIDTH - this.currentPiece.shape[0].length) / 2);
            this.currentPiece.y = 0;
        } else {
            // 第一次保持
            this.holdPiece = { ...this.currentPiece };
            this.spawnPiece();
            return;
        }

        this.canHold = false;
        this.updateGhostPiece();
        this.drawHold();
        this.draw();
    }

    movePiece(dx, dy) {
        if (!this.currentPiece) return;

        if (!this.checkCollision(this.currentPiece, dx, dy)) {
            this.currentPiece.x += dx;
            this.currentPiece.y += dy;
            this.updateGhostPiece();
            this.draw();
        } else if (dy > 0) {
            // 方块无法继续下降，固定到板面
            this.placePiece();
        }
    }

    hardDrop() {
        if (!this.currentPiece) return;

        let dropDistance = 0;
        while (!this.checkCollision(this.currentPiece, 0, dropDistance + 1)) {
            dropDistance++;
        }

        this.currentPiece.y += dropDistance;
        this.score += dropDistance * 2; // 硬降奖励分数
        this.sounds.drop();
        this.placePiece();
    }

    rotatePiece() {
        if (!this.currentPiece) return;

        const rotated = this.rotateMatrix(this.currentPiece.shape);
        const originalShape = this.currentPiece.shape;

        this.currentPiece.shape = rotated;

        // 检查旋转后是否有碰撞，尝试墙踢
        const kickTests = [
            [0, 0],   // 原位置
            [-1, 0],  // 左移
            [1, 0],   // 右移
            [0, -1],  // 上移
            [-1, -1], // 左上
            [1, -1]   // 右上
        ];

        let rotationSuccessful = false;
        for (const [dx, dy] of kickTests) {
            if (!this.checkCollision(this.currentPiece, dx, dy)) {
                this.currentPiece.x += dx;
                this.currentPiece.y += dy;
                rotationSuccessful = true;
                break;
            }
        }

        if (!rotationSuccessful) {
            // 无法旋转，恢复原状
            this.currentPiece.shape = originalShape;
            return;
        }

        this.updateGhostPiece();
        this.draw();
    }

    rotateMatrix(matrix) {
        const rows = matrix.length;
        const cols = matrix[0].length;
        const rotated = Array(cols).fill().map(() => Array(rows).fill(0));

        for (let i = 0; i < rows; i++) {
            for (let j = 0; j < cols; j++) {
                rotated[j][rows - 1 - i] = matrix[i][j];
            }
        }

        return rotated;
    }

    checkCollision(piece, dx, dy) {
        const newX = piece.x + dx;
        const newY = piece.y + dy;

        for (let y = 0; y < piece.shape.length; y++) {
            for (let x = 0; x < piece.shape[y].length; x++) {
                if (piece.shape[y][x]) {
                    const boardX = newX + x;
                    const boardY = newY + y;

                    // 检查边界
                    if (boardX < 0 || boardX >= this.BOARD_WIDTH || boardY >= this.BOARD_HEIGHT) {
                        return true;
                    }

                    // 检查与已有方块的碰撞
                    if (boardY >= 0 && this.board[boardY][boardX]) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    updateGhostPiece() {
        if (!this.currentPiece || !this.settings.ghostPiece) {
            this.ghostPiece = null;
            return;
        }

        this.ghostPiece = { ...this.currentPiece };
        this.ghostPiece.shape = this.currentPiece.shape.map(row => [...row]);

        // 找到最低可能位置
        while (!this.checkCollision(this.ghostPiece, 0, 1)) {
            this.ghostPiece.y++;
        }
    }

    placePiece() {
        if (!this.currentPiece) return;

        // 将当前方块放置到板面上
        for (let y = 0; y < this.currentPiece.shape.length; y++) {
            for (let x = 0; x < this.currentPiece.shape[y].length; x++) {
                if (this.currentPiece.shape[y][x]) {
                    const boardX = this.currentPiece.x + x;
                    const boardY = this.currentPiece.y + y;

                    if (boardY >= 0 && boardX >= 0 && boardX < this.BOARD_WIDTH && boardY < this.BOARD_HEIGHT) {
                        this.board[boardY][boardX] = this.currentPiece.color;
                    }
                }
            }
        }

        // 检查并清除完整的行
        this.clearLines();

        // 生成新方块
        this.spawnPiece();
    }

    clearLines() {
        let linesCleared = 0;
        const clearedRows = [];

        for (let y = this.BOARD_HEIGHT - 1; y >= 0; y--) {
            if (this.board[y].every(cell => cell !== 0)) {
                clearedRows.push(y);
                this.board.splice(y, 1);
                this.board.unshift(Array(this.BOARD_WIDTH).fill(0));
                linesCleared++;
                y++; // 重新检查这一行
            }
        }

        if (linesCleared > 0) {
            this.updateScore(linesCleared);
            this.playLineClearSound(linesCleared);
            this.animateLineClear(clearedRows);
        }
    }

    updateScore(linesCleared) {
        // 增强的计分规则
        const basePoints = [0, 100, 300, 500, 800];
        const points = basePoints[linesCleared] * this.level;

        // 连击奖励
        if (linesCleared === 4) {
            this.score += points * 1.5; // Tetris 奖励
        } else {
            this.score += points;
        }

        this.lines += linesCleared;

        // 每10行提升一个等级
        const newLevel = Math.floor(this.lines / 10) + 1;
        if (newLevel > this.level) {
            this.level = newLevel;
            this.updateDropInterval();
            this.animateLevelUp();
        }

        this.updateDisplay();
        this.animateScoreIncrease();
    }

    playLineClearSound(linesCleared) {
        if (linesCleared === 4) {
            this.sounds.tetris();
        } else {
            this.sounds.line();
        }
    }

    animateLineClear(clearedRows) {
        // 简单的闪烁效果
        setTimeout(() => {
            this.draw();
        }, 100);
    }

    animateScoreIncrease() {
        this.scoreElement.classList.add('score-animation');
        setTimeout(() => {
            this.scoreElement.classList.remove('score-animation');
        }, 300);
    }

    animateLevelUp() {
        this.levelElement.classList.add('level-animation');
        setTimeout(() => {
            this.levelElement.classList.remove('level-animation');
        }, 600);
    }

    updateDisplay() {
        this.scoreElement.textContent = this.score;
        this.levelElement.textContent = this.level;
        this.linesElement.textContent = this.lines;
        this.highScoreElement.textContent = this.highScore;
        this.totalPiecesElement.textContent = this.totalPieces;

        // 更新游戏时间和每分钟方块数
        if (this.gameStartTime > 0) {
            const gameTime = Date.now() - this.gameStartTime;
            this.gameTimeElement.textContent = this.formatTime(gameTime);

            const minutes = gameTime / 60000;
            const ppm = minutes > 0 ? Math.round(this.totalPieces / minutes) : 0;
            this.piecesPerMinuteElement.textContent = ppm;
        }
    }

    formatTime(milliseconds) {
        const seconds = Math.floor(milliseconds / 1000);
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }

    draw() {
        // 清空画布
        this.ctx.fillStyle = '#2d3748';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // 绘制网格
        this.drawGrid();

        // 绘制已放置的方块
        this.drawBoard();

        // 绘制幽灵方块
        if (this.ghostPiece && this.settings.ghostPiece) {
            this.drawGhostPiece();
        }

        // 绘制当前方块
        if (this.currentPiece) {
            this.drawPiece(this.currentPiece, this.ctx);
        }
    }

    drawGrid() {
        this.ctx.strokeStyle = '#4a5568';
        this.ctx.lineWidth = 1;

        // 绘制垂直线
        for (let x = 0; x <= this.BOARD_WIDTH; x++) {
            this.ctx.beginPath();
            this.ctx.moveTo(x * this.BLOCK_SIZE, 0);
            this.ctx.lineTo(x * this.BLOCK_SIZE, this.canvas.height);
            this.ctx.stroke();
        }

        // 绘制水平线
        for (let y = 0; y <= this.BOARD_HEIGHT; y++) {
            this.ctx.beginPath();
            this.ctx.moveTo(0, y * this.BLOCK_SIZE);
            this.ctx.lineTo(this.canvas.width, y * this.BLOCK_SIZE);
            this.ctx.stroke();
        }
    }

    drawBoard() {
        for (let y = 0; y < this.BOARD_HEIGHT; y++) {
            for (let x = 0; x < this.BOARD_WIDTH; x++) {
                if (this.board[y][x]) {
                    this.ctx.fillStyle = this.board[y][x];
                    this.ctx.fillRect(
                        x * this.BLOCK_SIZE + 1,
                        y * this.BLOCK_SIZE + 1,
                        this.BLOCK_SIZE - 2,
                        this.BLOCK_SIZE - 2
                    );

                    // 添加高光效果
                    this.ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
                    this.ctx.fillRect(
                        x * this.BLOCK_SIZE + 1,
                        y * this.BLOCK_SIZE + 1,
                        this.BLOCK_SIZE - 2,
                        4
                    );
                }
            }
        }
    }

    drawPiece(piece, context) {
        context.fillStyle = piece.color;

        for (let y = 0; y < piece.shape.length; y++) {
            for (let x = 0; x < piece.shape[y].length; x++) {
                if (piece.shape[y][x]) {
                    const drawX = (piece.x + x) * this.BLOCK_SIZE + 1;
                    const drawY = (piece.y + y) * this.BLOCK_SIZE + 1;

                    // 主要颜色
                    context.fillStyle = piece.color;
                    context.fillRect(drawX, drawY, this.BLOCK_SIZE - 2, this.BLOCK_SIZE - 2);

                    // 高光效果
                    context.fillStyle = 'rgba(255, 255, 255, 0.4)';
                    context.fillRect(drawX, drawY, this.BLOCK_SIZE - 2, 4);

                    // 阴影效果
                    context.fillStyle = 'rgba(0, 0, 0, 0.3)';
                    context.fillRect(drawX, drawY + this.BLOCK_SIZE - 6, this.BLOCK_SIZE - 2, 4);
                }
            }
        }
    }

    drawGhostPiece() {
        if (!this.ghostPiece) return;

        this.ctx.fillStyle = this.ghostPiece.shadowColor || 'rgba(255, 255, 255, 0.3)';

        for (let y = 0; y < this.ghostPiece.shape.length; y++) {
            for (let x = 0; x < this.ghostPiece.shape[y].length; x++) {
                if (this.ghostPiece.shape[y][x]) {
                    const drawX = (this.ghostPiece.x + x) * this.BLOCK_SIZE + 1;
                    const drawY = (this.ghostPiece.y + y) * this.BLOCK_SIZE + 1;

                    // 绘制边框
                    this.ctx.strokeStyle = this.ghostPiece.color;
                    this.ctx.lineWidth = 2;
                    this.ctx.strokeRect(drawX, drawY, this.BLOCK_SIZE - 2, this.BLOCK_SIZE - 2);

                    // 绘制半透明填充
                    this.ctx.fillRect(drawX, drawY, this.BLOCK_SIZE - 2, this.BLOCK_SIZE - 2);
                }
            }
        }
    }

    drawNext() {
        if (!this.nextPiece) return;

        // 清空画布
        this.nextCtx.fillStyle = '#f7fafc';
        this.nextCtx.fillRect(0, 0, this.nextCanvas.width, this.nextCanvas.height);

        // 计算居中位置
        const blockSize = 18;
        const offsetX = (this.nextCanvas.width - this.nextPiece.shape[0].length * blockSize) / 2;
        const offsetY = (this.nextCanvas.height - this.nextPiece.shape.length * blockSize) / 2;

        for (let y = 0; y < this.nextPiece.shape.length; y++) {
            for (let x = 0; x < this.nextPiece.shape[y].length; x++) {
                if (this.nextPiece.shape[y][x]) {
                    const drawX = offsetX + x * blockSize + 1;
                    const drawY = offsetY + y * blockSize + 1;

                    // 主要颜色
                    this.nextCtx.fillStyle = this.nextPiece.color;
                    this.nextCtx.fillRect(drawX, drawY, blockSize - 2, blockSize - 2);

                    // 高光效果
                    this.nextCtx.fillStyle = 'rgba(255, 255, 255, 0.4)';
                    this.nextCtx.fillRect(drawX, drawY, blockSize - 2, 3);
                }
            }
        }
    }

    drawHold() {
        // 清空画布
        this.holdCtx.fillStyle = '#fef5e7';
        this.holdCtx.fillRect(0, 0, this.holdCanvas.width, this.holdCanvas.height);

        if (!this.holdPiece) return;

        // 计算居中位置
        const blockSize = 15;
        const offsetX = (this.holdCanvas.width - this.holdPiece.shape[0].length * blockSize) / 2;
        const offsetY = (this.holdCanvas.height - this.holdPiece.shape.length * blockSize) / 2;

        // 如果不能保持，则显示为灰色
        const alpha = this.canHold ? 1 : 0.5;

        for (let y = 0; y < this.holdPiece.shape.length; y++) {
            for (let x = 0; x < this.holdPiece.shape[y].length; x++) {
                if (this.holdPiece.shape[y][x]) {
                    const drawX = offsetX + x * blockSize + 1;
                    const drawY = offsetY + y * blockSize + 1;

                    // 主要颜色
                    this.holdCtx.fillStyle = this.holdPiece.color;
                    this.holdCtx.globalAlpha = alpha;
                    this.holdCtx.fillRect(drawX, drawY, blockSize - 2, blockSize - 2);

                    // 高光效果
                    this.holdCtx.fillStyle = 'rgba(255, 255, 255, 0.4)';
                    this.holdCtx.fillRect(drawX, drawY, blockSize - 2, 2);
                }
            }
        }

        this.holdCtx.globalAlpha = 1;
    }

    showOverlay(title, message) {
        this.overlayTitle.textContent = title;
        this.overlayMessage.textContent = message;
        this.gameOverlay.classList.remove('hidden');
    }

    hideOverlay() {
        this.gameOverlay.classList.add('hidden');
    }

    gameLoop() {
        if (this.gameState !== 'playing') return;

        const currentTime = Date.now();

        // 更新显示
        this.updateDisplay();

        // 自动下降
        if (currentTime - this.dropTime > this.dropInterval) {
            this.movePiece(0, 1);
            this.dropTime = currentTime;
        }

        requestAnimationFrame(() => this.gameLoop());
    }
}

// 启动游戏
document.addEventListener('DOMContentLoaded', () => {
    new EnhancedTetrisGame();
});
