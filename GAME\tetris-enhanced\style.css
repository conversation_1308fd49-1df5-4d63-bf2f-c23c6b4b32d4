* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #333;
    animation: backgroundShift 10s ease-in-out infinite alternate;
}

@keyframes backgroundShift {
    0% { background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%); }
    100% { background: linear-gradient(135deg, #f093fb 0%, #667eea 50%, #764ba2 100%); }
}

.game-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 25px;
    padding: 25px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    max-width: 900px;
    width: 100%;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.game-header {
    text-align: center;
    margin-bottom: 25px;
}

.game-header h1 {
    color: #4a5568;
    font-size: 2.8em;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 15px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.game-stats {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 10px;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    background: linear-gradient(135deg, #f7fafc, #edf2f7);
    padding: 10px 20px;
    border-radius: 15px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.stat-label {
    font-size: 0.9em;
    color: #718096;
    margin-bottom: 5px;
}

.stat-value {
    font-size: 1.4em;
    font-weight: bold;
    color: #2d3748;
}

.game-main {
    display: flex;
    gap: 25px;
    justify-content: center;
    align-items: flex-start;
}

.game-info {
    display: flex;
    flex-direction: column;
    gap: 20px;
    min-width: 140px;
}

.info-section {
    background: linear-gradient(135deg, #f7fafc, #edf2f7);
    padding: 18px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.5);
}

.info-section h3 {
    color: #2d3748;
    margin-bottom: 12px;
    font-size: 1.1em;
    font-weight: 600;
}

.score-display, .level-display, .lines-display {
    font-size: 2em;
    font-weight: bold;
    color: #4a5568;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.next-piece-section {
    background: linear-gradient(135deg, #e6fffa, #b2f5ea);
}

#nextCanvas, #holdCanvas {
    border: 2px solid #4fd1c7;
    border-radius: 8px;
    background: #fff;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stats-grid {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.stat-row {
    display: flex;
    justify-content: space-between;
    font-size: 0.9em;
    color: #4a5568;
}

.game-area {
    position: relative;
    display: flex;
    justify-content: center;
}

#gameCanvas {
    border: 4px solid #4a5568;
    border-radius: 15px;
    background: #2d3748;
    display: block;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.game-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.85);
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 15px;
    color: white;
    z-index: 10;
    backdrop-filter: blur(5px);
}

.overlay-content {
    text-align: center;
    padding: 30px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.overlay-content h2 {
    font-size: 2.2em;
    margin-bottom: 15px;
    color: #fff;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.overlay-content p {
    font-size: 1.2em;
    margin-bottom: 25px;
    color: #cbd5e0;
}

.overlay-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.game-button {
    border: none;
    padding: 12px 24px;
    font-size: 1.1em;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.game-button.primary {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
    box-shadow: 0 4px 8px rgba(72, 187, 120, 0.3);
}

.game-button.primary:hover {
    background: linear-gradient(135deg, #38a169, #2f855a);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(72, 187, 120, 0.4);
}

.game-button.secondary {
    background: linear-gradient(135deg, #718096, #4a5568);
    color: white;
    box-shadow: 0 4px 8px rgba(113, 128, 150, 0.3);
}

.game-button.secondary:hover {
    background: linear-gradient(135deg, #4a5568, #2d3748);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(113, 128, 150, 0.4);
}

.settings-panel {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.9);
    display: none;
    justify-content: center;
    align-items: center;
    border-radius: 15px;
    z-index: 15;
    backdrop-filter: blur(10px);
}

.settings-content {
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 20px;
    color: #2d3748;
    min-width: 300px;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
}

.settings-content h3 {
    text-align: center;
    margin-bottom: 20px;
    font-size: 1.5em;
    color: #4a5568;
}

.setting-item {
    margin-bottom: 15px;
}

.setting-item label {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.1em;
    cursor: pointer;
}

.setting-item input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: #48bb78;
}

.setting-item select {
    padding: 8px 12px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1em;
    background: white;
    cursor: pointer;
}

.settings-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 25px;
}

.game-controls {
    background: linear-gradient(135deg, #f7fafc, #edf2f7);
    padding: 20px;
    border-radius: 15px;
    min-width: 140px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.5);
}

.game-controls h3 {
    color: #2d3748;
    margin-bottom: 15px;
    font-size: 1.1em;
    text-align: center;
    font-weight: 600;
}

.controls-grid {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 20px;
}

.control-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
    padding: 10px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.8);
}

.key {
    background: linear-gradient(135deg, #4a5568, #2d3748);
    color: white;
    padding: 6px 10px;
    border-radius: 6px;
    font-size: 0.9em;
    font-weight: bold;
    min-width: 35px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.hold-section {
    background: linear-gradient(135deg, #fef5e7, #fed7aa);
    padding: 15px;
    border-radius: 10px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.5);
}

.hold-section h4 {
    color: #2d3748;
    margin-bottom: 10px;
    font-size: 1em;
}

.game-footer {
    text-align: center;
    margin-top: 25px;
    color: #718096;
    font-size: 0.95em;
    font-style: italic;
}

.hidden {
    display: none !important;
}

/* 动画效果 */
@keyframes scoreIncrease {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); color: #48bb78; }
    100% { transform: scale(1); }
}

.score-animation {
    animation: scoreIncrease 0.3s ease-in-out;
}

@keyframes levelUp {
    0% { transform: scale(1) rotate(0deg); }
    25% { transform: scale(1.1) rotate(-5deg); }
    50% { transform: scale(1.2) rotate(5deg); }
    75% { transform: scale(1.1) rotate(-2deg); }
    100% { transform: scale(1) rotate(0deg); }
}

.level-animation {
    animation: levelUp 0.6s ease-in-out;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .game-main {
        flex-direction: column;
        align-items: center;
    }
    
    .game-info, .game-controls {
        width: 100%;
        max-width: 320px;
    }
    
    .game-info {
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: space-around;
    }
    
    .info-section {
        flex: 1;
        margin: 5px;
        min-width: 120px;
    }
    
    .game-stats {
        flex-direction: column;
        gap: 10px;
    }
    
    #gameCanvas {
        width: 100%;
        max-width: 300px;
        height: auto;
    }
    
    .game-header h1 {
        font-size: 2.2em;
    }
}
