<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>俄罗斯方块</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="game-container">
        <div class="game-header">
            <h1>俄罗斯方块</h1>
        </div>
        
        <div class="game-main">
            <div class="game-info">
                <div class="info-section">
                    <h3>分数</h3>
                    <div class="score-display" id="score">0</div>
                </div>
                
                <div class="info-section">
                    <h3>等级</h3>
                    <div class="level-display" id="level">1</div>
                </div>
                
                <div class="info-section">
                    <h3>行数</h3>
                    <div class="lines-display" id="lines">0</div>
                </div>
                
                <div class="info-section">
                    <h3>下一个</h3>
                    <canvas id="nextCanvas" width="80" height="80"></canvas>
                </div>
            </div>
            
            <div class="game-area">
                <canvas id="gameCanvas" width="300" height="600"></canvas>
                <div class="game-overlay" id="gameOverlay">
                    <div class="overlay-content">
                        <h2 id="overlayTitle">开始游戏</h2>
                        <p id="overlayMessage">准备好挑战俄罗斯方块了吗？</p>
                        <button type="button" id="startButton">开始游戏</button>
                    </div>
                </div>
            </div>
            
            <div class="game-controls">
                <h3>游戏控制</h3>
                <div class="controls-grid">
                    <div class="control-item">
                        <span class="key">←</span>
                        <span>左移</span>
                    </div>
                    <div class="control-item">
                        <span class="key">→</span>
                        <span>右移</span>
                    </div>
                    <div class="control-item">
                        <span class="key">↓</span>
                        <span>快速下降</span>
                    </div>
                    <div class="control-item">
                        <span class="key">↑</span>
                        <span>旋转</span>
                    </div>
                    <div class="control-item">
                        <span class="key">空格</span>
                        <span>暂停/继续</span>
                    </div>
                    <div class="control-item">
                        <span class="key">R</span>
                        <span>重新开始</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="game-footer">
            <p>经典俄罗斯方块游戏 - 消除完整行获得分数！</p>
        </div>
    </div>
    
    <script src="script.js"></script>
</body>
</html>
