class TetrisGame {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.nextCanvas = document.getElementById('nextCanvas');
        this.nextCtx = this.nextCanvas.getContext('2d');
        
        this.scoreElement = document.getElementById('score');
        this.levelElement = document.getElementById('level');
        this.linesElement = document.getElementById('lines');
        this.gameOverlay = document.getElementById('gameOverlay');
        this.overlayTitle = document.getElementById('overlayTitle');
        this.overlayMessage = document.getElementById('overlayMessage');
        this.startButton = document.getElementById('startButton');
        
        // 游戏配置
        this.BOARD_WIDTH = 10;
        this.BOARD_HEIGHT = 20;
        this.BLOCK_SIZE = 30;
        
        // 游戏状态
        this.gameState = 'waiting'; // waiting, playing, paused, gameOver
        this.score = 0;
        this.level = 1;
        this.lines = 0;
        this.dropTime = 0;
        this.dropInterval = 1000; // 毫秒
        
        // 游戏板面
        this.board = this.createBoard();
        
        // 当前和下一个方块
        this.currentPiece = null;
        this.nextPiece = null;
        
        // 方块定义
        this.pieces = {
            I: {
                shape: [
                    [0, 0, 0, 0],
                    [1, 1, 1, 1],
                    [0, 0, 0, 0],
                    [0, 0, 0, 0]
                ],
                color: '#00f5ff'
            },
            O: {
                shape: [
                    [1, 1],
                    [1, 1]
                ],
                color: '#ffff00'
            },
            T: {
                shape: [
                    [0, 1, 0],
                    [1, 1, 1],
                    [0, 0, 0]
                ],
                color: '#800080'
            },
            S: {
                shape: [
                    [0, 1, 1],
                    [1, 1, 0],
                    [0, 0, 0]
                ],
                color: '#00ff00'
            },
            Z: {
                shape: [
                    [1, 1, 0],
                    [0, 1, 1],
                    [0, 0, 0]
                ],
                color: '#ff0000'
            },
            J: {
                shape: [
                    [1, 0, 0],
                    [1, 1, 1],
                    [0, 0, 0]
                ],
                color: '#0000ff'
            },
            L: {
                shape: [
                    [0, 0, 1],
                    [1, 1, 1],
                    [0, 0, 0]
                ],
                color: '#ffa500'
            }
        };
        
        this.pieceTypes = Object.keys(this.pieces);
        
        this.init();
    }
    
    createBoard() {
        return Array(this.BOARD_HEIGHT).fill().map(() => Array(this.BOARD_WIDTH).fill(0));
    }
    
    init() {
        this.setupEventListeners();
        this.draw();
        this.showOverlay('开始游戏', '准备好挑战俄罗斯方块了吗？');
    }
    
    setupEventListeners() {
        // 键盘事件
        document.addEventListener('keydown', (e) => this.handleKeyPress(e));
        
        // 开始按钮
        this.startButton.addEventListener('click', (e) => {
            e.preventDefault();
            this.handleStartButton();
        });
        
        // 防止方向键滚动页面
        window.addEventListener('keydown', (e) => {
            if(['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', ' '].includes(e.key)) {
                e.preventDefault();
            }
        });
    }
    
    handleKeyPress(e) {
        if (this.gameState === 'waiting') {
            if (e.key === ' ') {
                this.startGame();
            }
            return;
        }
        
        if (this.gameState === 'playing') {
            switch(e.key) {
                case 'ArrowLeft':
                    this.movePiece(-1, 0);
                    break;
                case 'ArrowRight':
                    this.movePiece(1, 0);
                    break;
                case 'ArrowDown':
                    this.movePiece(0, 1);
                    break;
                case 'ArrowUp':
                    this.rotatePiece();
                    break;
                case ' ':
                    this.pauseGame();
                    break;
            }
        }
        
        if (e.key === 'r' || e.key === 'R') {
            this.resetGame();
        }
        
        if (this.gameState === 'paused' && e.key === ' ') {
            this.resumeGame();
        }
        
        if (this.gameState === 'gameOver' && e.key === ' ') {
            this.resetGame();
        }
    }
    
    handleStartButton() {
        if (this.gameState === 'waiting') {
            this.startGame();
        } else if (this.gameState === 'gameOver') {
            this.resetGame();
        }
    }
    
    startGame() {
        this.gameState = 'playing';
        this.hideOverlay();
        this.dropTime = Date.now();
        this.spawnPiece();
        this.gameLoop();
    }
    
    pauseGame() {
        this.gameState = 'paused';
        this.showOverlay('游戏暂停', '按空格键继续游戏');
    }
    
    resumeGame() {
        this.gameState = 'playing';
        this.hideOverlay();
        this.dropTime = Date.now();
        this.gameLoop();
    }
    
    resetGame() {
        this.gameState = 'waiting';
        this.score = 0;
        this.level = 1;
        this.lines = 0;
        this.dropInterval = 1000;
        this.board = this.createBoard();
        this.currentPiece = null;
        this.nextPiece = null;
        this.updateDisplay();
        this.draw();
        this.showOverlay('开始游戏', '准备好挑战俄罗斯方块了吗？');
    }
    
    gameOver() {
        this.gameState = 'gameOver';
        this.showOverlay('游戏结束', `你的分数：${this.score}分\n按空格键重新开始`);
    }
    
    spawnPiece() {
        if (!this.nextPiece) {
            this.nextPiece = this.getRandomPiece();
        }
        
        this.currentPiece = this.nextPiece;
        this.nextPiece = this.getRandomPiece();
        
        // 设置初始位置
        this.currentPiece.x = Math.floor((this.BOARD_WIDTH - this.currentPiece.shape[0].length) / 2);
        this.currentPiece.y = 0;
        
        // 检查游戏是否结束
        if (this.checkCollision(this.currentPiece, 0, 0)) {
            this.gameOver();
            return;
        }

        this.drawNext();
        this.draw(); // 绘制主游戏区域
    }
    
    getRandomPiece() {
        const type = this.pieceTypes[Math.floor(Math.random() * this.pieceTypes.length)];
        const piece = this.pieces[type];
        return {
            shape: piece.shape.map(row => [...row]),
            color: piece.color,
            x: 0,
            y: 0
        };
    }

    movePiece(dx, dy) {
        if (!this.currentPiece) return;

        if (!this.checkCollision(this.currentPiece, dx, dy)) {
            this.currentPiece.x += dx;
            this.currentPiece.y += dy;
            this.draw();
        } else if (dy > 0) {
            // 方块无法继续下降，固定到板面
            this.placePiece();
        }
    }

    rotatePiece() {
        if (!this.currentPiece) return;

        const rotated = this.rotateMatrix(this.currentPiece.shape);
        const originalShape = this.currentPiece.shape;

        this.currentPiece.shape = rotated;

        // 检查旋转后是否有碰撞
        if (this.checkCollision(this.currentPiece, 0, 0)) {
            // 尝试向左或向右移动来避免碰撞
            if (!this.checkCollision(this.currentPiece, -1, 0)) {
                this.currentPiece.x -= 1;
            } else if (!this.checkCollision(this.currentPiece, 1, 0)) {
                this.currentPiece.x += 1;
            } else {
                // 无法旋转，恢复原状
                this.currentPiece.shape = originalShape;
                return;
            }
        }

        this.draw();
    }

    rotateMatrix(matrix) {
        const rows = matrix.length;
        const cols = matrix[0].length;
        const rotated = Array(cols).fill().map(() => Array(rows).fill(0));

        for (let i = 0; i < rows; i++) {
            for (let j = 0; j < cols; j++) {
                rotated[j][rows - 1 - i] = matrix[i][j];
            }
        }

        return rotated;
    }

    checkCollision(piece, dx, dy) {
        const newX = piece.x + dx;
        const newY = piece.y + dy;

        for (let y = 0; y < piece.shape.length; y++) {
            for (let x = 0; x < piece.shape[y].length; x++) {
                if (piece.shape[y][x]) {
                    const boardX = newX + x;
                    const boardY = newY + y;

                    // 检查边界
                    if (boardX < 0 || boardX >= this.BOARD_WIDTH ||
                        boardY >= this.BOARD_HEIGHT) {
                        return true;
                    }

                    // 检查与已有方块的碰撞
                    if (boardY >= 0 && this.board[boardY][boardX]) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    placePiece() {
        if (!this.currentPiece) return;

        // 将当前方块放置到板面上
        for (let y = 0; y < this.currentPiece.shape.length; y++) {
            for (let x = 0; x < this.currentPiece.shape[y].length; x++) {
                if (this.currentPiece.shape[y][x]) {
                    const boardX = this.currentPiece.x + x;
                    const boardY = this.currentPiece.y + y;

                    if (boardY >= 0 && boardX >= 0 && boardX < this.BOARD_WIDTH && boardY < this.BOARD_HEIGHT) {
                        this.board[boardY][boardX] = this.currentPiece.color;
                    }
                }
            }
        }

        // 检查并清除完整的行
        this.clearLines();

        // 生成新方块
        this.spawnPiece();
    }

    clearLines() {
        let linesCleared = 0;

        for (let y = this.BOARD_HEIGHT - 1; y >= 0; y--) {
            if (this.board[y].every(cell => cell !== 0)) {
                // 移除这一行
                this.board.splice(y, 1);
                // 在顶部添加新的空行
                this.board.unshift(Array(this.BOARD_WIDTH).fill(0));
                linesCleared++;
                y++; // 重新检查这一行
            }
        }

        if (linesCleared > 0) {
            this.updateScore(linesCleared);
        }
    }

    updateScore(linesCleared) {
        // 计分规则：单行100分，双行300分，三行500分，四行800分
        const points = [0, 100, 300, 500, 800];
        this.score += points[linesCleared] * this.level;
        this.lines += linesCleared;

        // 每10行提升一个等级
        const newLevel = Math.floor(this.lines / 10) + 1;
        if (newLevel > this.level) {
            this.level = newLevel;
            // 提升速度
            this.dropInterval = Math.max(100, 1000 - (this.level - 1) * 100);
        }

        this.updateDisplay();
    }

    updateDisplay() {
        this.scoreElement.textContent = this.score;
        this.levelElement.textContent = this.level;
        this.linesElement.textContent = this.lines;
    }

    draw() {
        // 清空画布
        this.ctx.fillStyle = '#2d3748';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // 绘制网格
        this.drawGrid();

        // 绘制已放置的方块
        this.drawBoard();

        // 绘制当前方块
        if (this.currentPiece) {
            this.drawPiece(this.currentPiece, this.ctx);
        }
    }

    drawGrid() {
        this.ctx.strokeStyle = '#4a5568';
        this.ctx.lineWidth = 1;

        // 绘制垂直线
        for (let x = 0; x <= this.BOARD_WIDTH; x++) {
            this.ctx.beginPath();
            this.ctx.moveTo(x * this.BLOCK_SIZE, 0);
            this.ctx.lineTo(x * this.BLOCK_SIZE, this.canvas.height);
            this.ctx.stroke();
        }

        // 绘制水平线
        for (let y = 0; y <= this.BOARD_HEIGHT; y++) {
            this.ctx.beginPath();
            this.ctx.moveTo(0, y * this.BLOCK_SIZE);
            this.ctx.lineTo(this.canvas.width, y * this.BLOCK_SIZE);
            this.ctx.stroke();
        }
    }

    drawBoard() {
        for (let y = 0; y < this.BOARD_HEIGHT; y++) {
            for (let x = 0; x < this.BOARD_WIDTH; x++) {
                if (this.board[y][x]) {
                    this.ctx.fillStyle = this.board[y][x];
                    this.ctx.fillRect(
                        x * this.BLOCK_SIZE + 1,
                        y * this.BLOCK_SIZE + 1,
                        this.BLOCK_SIZE - 2,
                        this.BLOCK_SIZE - 2
                    );
                }
            }
        }
    }

    drawPiece(piece, context) {
        context.fillStyle = piece.color;

        for (let y = 0; y < piece.shape.length; y++) {
            for (let x = 0; x < piece.shape[y].length; x++) {
                if (piece.shape[y][x]) {
                    const drawX = (piece.x + x) * this.BLOCK_SIZE + 1;
                    const drawY = (piece.y + y) * this.BLOCK_SIZE + 1;

                    context.fillRect(
                        drawX,
                        drawY,
                        this.BLOCK_SIZE - 2,
                        this.BLOCK_SIZE - 2
                    );
                }
            }
        }
    }

    drawNext() {
        if (!this.nextPiece) return;

        // 清空下一个方块的画布
        this.nextCtx.fillStyle = '#fff';
        this.nextCtx.fillRect(0, 0, this.nextCanvas.width, this.nextCanvas.height);

        // 计算居中位置
        const blockSize = 15;
        const offsetX = (this.nextCanvas.width - this.nextPiece.shape[0].length * blockSize) / 2;
        const offsetY = (this.nextCanvas.height - this.nextPiece.shape.length * blockSize) / 2;

        this.nextCtx.fillStyle = this.nextPiece.color;

        for (let y = 0; y < this.nextPiece.shape.length; y++) {
            for (let x = 0; x < this.nextPiece.shape[y].length; x++) {
                if (this.nextPiece.shape[y][x]) {
                    this.nextCtx.fillRect(
                        offsetX + x * blockSize + 1,
                        offsetY + y * blockSize + 1,
                        blockSize - 2,
                        blockSize - 2
                    );
                }
            }
        }
    }

    showOverlay(title, message) {
        this.overlayTitle.textContent = title;
        this.overlayMessage.textContent = message;
        this.gameOverlay.classList.remove('hidden');
    }

    hideOverlay() {
        this.gameOverlay.classList.add('hidden');
    }

    gameLoop() {
        if (this.gameState !== 'playing') return;

        const currentTime = Date.now();

        if (currentTime - this.dropTime > this.dropInterval) {
            this.movePiece(0, 1);
            this.dropTime = currentTime;
        }

        requestAnimationFrame(() => this.gameLoop());
    }
}

// 启动游戏
document.addEventListener('DOMContentLoaded', () => {
    new TetrisGame();
});
