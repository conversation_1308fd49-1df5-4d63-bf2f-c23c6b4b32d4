* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #333;
}

.game-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    max-width: 800px;
    width: 100%;
}

.game-header {
    text-align: center;
    margin-bottom: 20px;
}

.game-header h1 {
    color: #4a5568;
    font-size: 2.5em;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.game-main {
    display: flex;
    gap: 20px;
    justify-content: center;
    align-items: flex-start;
}

.game-info {
    display: flex;
    flex-direction: column;
    gap: 20px;
    min-width: 120px;
}

.info-section {
    background: #f7fafc;
    padding: 15px;
    border-radius: 10px;
    text-align: center;
}

.info-section h3 {
    color: #2d3748;
    margin-bottom: 10px;
    font-size: 1.1em;
}

.score-display, .level-display, .lines-display {
    font-size: 1.8em;
    font-weight: bold;
    color: #4a5568;
}

#nextCanvas {
    border: 2px solid #e2e8f0;
    border-radius: 5px;
    background: #fff;
}

.game-area {
    position: relative;
    display: flex;
    justify-content: center;
}

#gameCanvas {
    border: 4px solid #4a5568;
    border-radius: 10px;
    background: #2d3748;
    display: block;
}

.game-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 10px;
    color: white;
    z-index: 2;
}

.overlay-content {
    text-align: center;
    padding: 20px;
}

.overlay-content h2 {
    font-size: 2em;
    margin-bottom: 10px;
    color: #fff;
}

.overlay-content p {
    font-size: 1.2em;
    margin-bottom: 20px;
    color: #cbd5e0;
}

#startButton {
    background: #48bb78;
    color: white;
    border: none;
    padding: 12px 24px;
    font-size: 1.1em;
    border-radius: 8px;
    cursor: pointer;
    transition: background 0.3s ease;
}

#startButton:hover {
    background: #38a169;
}

.game-controls {
    background: #f7fafc;
    padding: 20px;
    border-radius: 10px;
    min-width: 120px;
}

.game-controls h3 {
    color: #2d3748;
    margin-bottom: 15px;
    font-size: 1.1em;
    text-align: center;
}

.controls-grid {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.control-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
    padding: 8px;
    background: white;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.key {
    background: #4a5568;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.9em;
    font-weight: bold;
    min-width: 30px;
    text-align: center;
}

.game-footer {
    text-align: center;
    margin-top: 20px;
    color: #718096;
    font-size: 0.9em;
}

.hidden {
    display: none !important;
}

@media (max-width: 768px) {
    .game-main {
        flex-direction: column;
        align-items: center;
    }
    
    .game-info, .game-controls {
        width: 100%;
        max-width: 300px;
    }
    
    .game-info {
        flex-direction: row;
        justify-content: space-around;
    }
    
    .info-section {
        flex: 1;
        margin: 0 5px;
    }
    
    #nextCanvas {
        width: 60px;
        height: 60px;
    }
    
    #gameCanvas {
        width: 100%;
        max-width: 300px;
        height: auto;
    }
}
