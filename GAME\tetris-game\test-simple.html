<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>俄罗斯方块测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            padding: 20px;
            background: #f0f0f0;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        canvas {
            border: 2px solid #333;
            background: #222;
            margin: 10px;
        }
        .controls {
            margin: 20px 0;
        }
        button {
            margin: 5px;
            padding: 10px 20px;
            font-size: 16px;
            cursor: pointer;
        }
        .info {
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
        }
        .info div {
            padding: 10px;
            background: #f8f8f8;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>俄罗斯方块测试</h1>
        
        <div class="info">
            <div>分数: <span id="score">0</span></div>
            <div>等级: <span id="level">1</span></div>
            <div>行数: <span id="lines">0</span></div>
        </div>
        
        <canvas id="gameCanvas" width="300" height="600"></canvas>
        <canvas id="nextCanvas" width="80" height="80"></canvas>
        
        <div class="controls">
            <button onclick="startGame()">开始游戏</button>
            <button onclick="pauseGame()">暂停</button>
            <button onclick="resetGame()">重置</button>
        </div>
        
        <div>
            <p>使用方向键控制：← → ↓ ↑ (旋转)</p>
            <p>空格键暂停/继续</p>
        </div>
    </div>
    
    <script>
        // 简化的俄罗斯方块测试
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        const nextCanvas = document.getElementById('nextCanvas');
        const nextCtx = nextCanvas.getContext('2d');
        
        let gameRunning = false;
        let score = 0;
        let level = 1;
        let lines = 0;
        
        // 简单的方块定义
        const pieces = [
            { shape: [[1,1,1,1]], color: '#00f5ff' }, // I
            { shape: [[1,1],[1,1]], color: '#ffff00' }, // O
            { shape: [[0,1,0],[1,1,1]], color: '#800080' }, // T
        ];
        
        let currentPiece = null;
        let currentX = 4;
        let currentY = 0;
        
        function startGame() {
            gameRunning = true;
            spawnPiece();
            gameLoop();
            console.log('游戏开始');
        }
        
        function pauseGame() {
            gameRunning = !gameRunning;
            if (gameRunning) {
                gameLoop();
            }
            console.log('游戏暂停/继续');
        }
        
        function resetGame() {
            gameRunning = false;
            score = 0;
            level = 1;
            lines = 0;
            currentPiece = null;
            updateDisplay();
            draw();
            console.log('游戏重置');
        }
        
        function spawnPiece() {
            currentPiece = pieces[Math.floor(Math.random() * pieces.length)];
            currentX = 4;
            currentY = 0;
            console.log('生成新方块:', currentPiece);
        }
        
        function draw() {
            // 清空画布
            ctx.fillStyle = '#222';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 绘制网格
            ctx.strokeStyle = '#444';
            ctx.lineWidth = 1;
            for (let x = 0; x <= 10; x++) {
                ctx.beginPath();
                ctx.moveTo(x * 30, 0);
                ctx.lineTo(x * 30, canvas.height);
                ctx.stroke();
            }
            for (let y = 0; y <= 20; y++) {
                ctx.beginPath();
                ctx.moveTo(0, y * 30);
                ctx.lineTo(canvas.width, y * 30);
                ctx.stroke();
            }
            
            // 绘制当前方块
            if (currentPiece) {
                ctx.fillStyle = currentPiece.color;
                for (let y = 0; y < currentPiece.shape.length; y++) {
                    for (let x = 0; x < currentPiece.shape[y].length; x++) {
                        if (currentPiece.shape[y][x]) {
                            ctx.fillRect(
                                (currentX + x) * 30 + 1,
                                (currentY + y) * 30 + 1,
                                28, 28
                            );
                        }
                    }
                }
            }
        }
        
        function updateDisplay() {
            document.getElementById('score').textContent = score;
            document.getElementById('level').textContent = level;
            document.getElementById('lines').textContent = lines;
        }
        
        function gameLoop() {
            if (!gameRunning) return;
            
            // 简单的下降逻辑
            currentY++;
            if (currentY > 18) {
                score += 10;
                spawnPiece();
            }
            
            draw();
            updateDisplay();
            
            setTimeout(gameLoop, 1000);
        }
        
        // 键盘控制
        document.addEventListener('keydown', (e) => {
            if (!gameRunning) return;
            
            switch(e.key) {
                case 'ArrowLeft':
                    if (currentX > 0) currentX--;
                    break;
                case 'ArrowRight':
                    if (currentX < 8) currentX++;
                    break;
                case 'ArrowDown':
                    currentY++;
                    break;
                case 'ArrowUp':
                    console.log('旋转');
                    break;
                case ' ':
                    pauseGame();
                    break;
            }
            draw();
            e.preventDefault();
        });
        
        // 初始化
        updateDisplay();
        draw();
        console.log('俄罗斯方块测试页面加载完成');
    </script>
</body>
</html>
