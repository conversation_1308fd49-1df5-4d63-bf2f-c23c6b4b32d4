# 俄罗斯方块游戏手柄支持测试

## 功能概述
俄罗斯方块游戏现在支持游戏手柄控制，提供更好的游戏体验。

## 支持的手柄类型
- Xbox 控制器
- PlayStation 控制器
- 其他标准游戏手柄（支持Gamepad API）

## 手柄控制映射

### 方向键/左摇杆
- **左/右**: 移动方块
- **下**: 快速下降（+1分）
- **上**: 旋转方块

### 按钮控制
- **A键**: 旋转方块
- **B键**: 硬降（直接降到底部，+2分/格）
- **Start键**: 暂停/继续游戏
- **Select键**: 重新开始游戏

## 手柄状态显示
游戏界面右侧显示当前手柄连接状态：
- **未连接**: 灰色显示
- **已连接**: 绿色显示

## 连接手柄
1. 将手柄连接到电脑（USB或蓝牙）
2. 打开游戏页面
3. 游戏会自动检测已连接的手柄
4. 右上角会显示连接提示
5. 手柄状态会更新为"已连接"

## 测试步骤
1. 打开 `tetris.html` 文件
2. 连接游戏手柄
3. 观察手柄状态变化
4. 测试各种手柄控制功能：
   - 方向键移动
   - A键旋转
   - B键硬降
   - Start键暂停
   - Select键重新开始

## 技术特性
- 使用 Gamepad API 进行手柄检测
- 支持热插拔（运行时连接/断开）
- 防止按键重复触发（150ms间隔）
- 实时状态显示
- 兼容多种手柄类型

## 注意事项
- 需要现代浏览器支持 Gamepad API
- 某些浏览器可能需要用户交互后才能检测手柄
- 手柄按键映射可能因品牌而略有不同
- 建议使用Chrome、Firefox或Edge浏览器

## 故障排除
如果手柄无法正常工作：
1. 确认手柄已正确连接
2. 检查浏览器是否支持 Gamepad API
3. 尝试刷新页面
4. 查看浏览器控制台是否有错误信息
