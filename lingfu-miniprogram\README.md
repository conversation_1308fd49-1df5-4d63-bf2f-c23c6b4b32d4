# 灵符驾到 - 微信小程序

一个融合传统文化与现代技术的灵符抽签小程序，让用户通过摇一摇或分类选择的方式抽取专属灵符，获得心灵慰藉和精神寄托。

## 📱 功能特色

### 🎯 核心功能
- **摇一摇抽签** - 使用重力感应，摇动手机即可抽取灵符
- **分类抽签** - 根据不同需求（趋吉避凶、升学就业、生意创意、身体康健、感情姻缘、家庭和睦）精准抽取
- **灵符详情** - 详细展示灵符的寓意、使用方法和祝福语
- **收藏功能** - 收藏喜爱的灵符，随时查看
- **分享功能** - 分享灵符给朋友，传递美好祝愿

### ✨ 特色体验
- **传统美学** - 采用中国传统色彩和古典设计风格
- **动画效果** - 丰富的翻牌、发光、粒子动画效果
- **音效反馈** - 古典音乐和震动反馈增强仪式感
- **稀有度系统** - 普通、稀有、传说三个等级，增加抽取乐趣
- **每日限制** - 每日限抽3次，珍惜每次机缘

## 🏗️ 技术架构

### 前端技术
- **微信小程序原生开发**
- **WXML + WXSS + JavaScript**
- **微信小程序云开发**
- **Canvas 动画绘制**
- **重力感应 API**

### 数据结构
```javascript
// 灵符数据结构
{
  id: "lingfu_001",
  name: "太上老君急急如律令",
  category: ["luck", "health"],
  image: "/images/lingfu/taishang.png",
  description: "此符乃太上老君亲传，能驱邪避凶，护佑平安",
  usage: "随身携带或贴于门上，诚心念诵三遍",
  blessing: "愿此符护佑您平安吉祥，邪不侵身",
  rarity: "legendary", // common, rare, legendary
  power: 95
}
```

### 分类系统
- **趋吉避凶** (luck) - 平安符、辟邪符
- **升学就业** (study) - 文昌符、智慧符  
- **生意创意** (business) - 财运符、招财符
- **身体康健** (health) - 健康符、长寿符
- **感情姻缘** (love) - 桃花符、和合符
- **家庭和睦** (family) - 家和符、子孙符

## 📁 项目结构

```
lingfu-miniprogram/
├── pages/                 # 页面文件
│   ├── index/             # 首页（抽签页）
│   ├── detail/            # 灵符详情页
│   ├── library/           # 灵符库页面
│   ├── collection/        # 我的收藏页
│   └── about/             # 关于页面
├── components/            # 自定义组件
│   ├── lingfu-card/       # 灵符卡片组件
│   ├── category-selector/ # 分类选择器
│   └── shake-detector/    # 摇一摇检测器
├── utils/                 # 工具函数
│   ├── draw.js           # 抽签逻辑
│   ├── api.js            # API接口
│   └── animation.js      # 动画工具
├── images/               # 静态图片资源
├── audio/                # 音频文件
├── styles/               # 公共样式
├── data/                 # 本地数据
├── cloudfunctions/       # 云函数
├── app.js                # 小程序入口
├── app.json              # 小程序配置
└── app.wxss              # 全局样式
```

## 🚀 快速开始

### 环境要求
- 微信开发者工具
- 微信小程序账号
- 云开发环境

### 安装步骤

1. **克隆项目**
```bash
git clone [项目地址]
cd lingfu-miniprogram
```

2. **导入微信开发者工具**
- 打开微信开发者工具
- 选择"导入项目"
- 选择项目目录
- 填写 AppID

3. **配置云开发**
- 在 `app.js` 中配置云开发环境ID
- 上传云函数
- 初始化云数据库

4. **上传资源**
- 将灵符图片上传到云存储
- 更新图片路径配置

5. **预览测试**
- 点击"预览"生成二维码
- 使用微信扫码在真机上测试

## 🎨 UI设计说明

### 色彩方案
- **主色调**: 朱红色 (#DC143C) - 传统吉祥色
- **辅助色**: 金黄色 (#DAA520) - 富贵华丽
- **背景色**: 米黄色 (#FFF8DC) - 古典雅致
- **文字色**: 棕色 (#8B4513) - 沉稳庄重

### 设计元素
- **圆形设计** - 寓意圆满和谐
- **渐变效果** - 增加视觉层次
- **阴影效果** - 营造立体感
- **古典纹理** - 体现传统文化

## 🔧 核心功能实现

### 摇一摇检测
```javascript
// 监听重力感应
wx.onAccelerometerChange((res) => {
  const speed = Math.abs(deltaX + deltaY + deltaZ) / deltaTime * 10000
  if (speed > 300) {
    this.onShake() // 触发抽签
  }
})
```

### 抽签算法
```javascript
// 加权随机算法
const weightedLingfu = []
availableLingfu.forEach(lingfu => {
  let weight = lingfu.rarity === 'legendary' ? 10 : 
               lingfu.rarity === 'rare' ? 30 : 50
  for (let i = 0; i < weight; i++) {
    weightedLingfu.push(lingfu)
  }
})
```

### 动画效果
- **CSS3 Transform** - 翻牌动画
- **Canvas 绘制** - 粒子效果
- **关键帧动画** - 发光效果

## 📊 数据管理

### 本地存储
- 用户收藏列表
- 抽签历史记录
- 每日抽签次数
- 用户偏好设置

### 云端存储
- 灵符基础数据
- 用户行为统计
- 图片资源文件

## 🎯 未来规划

### 功能扩展
- [ ] 更多灵符类型和分类
- [ ] 用户自定义祈愿
- [ ] 灵符合成系统
- [ ] 社区分享功能
- [ ] 每日签到奖励

### 技术优化
- [ ] 性能优化和缓存策略
- [ ] 离线功能支持
- [ ] 多语言国际化
- [ ] 无障碍访问支持

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request 来帮助改进项目！

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 邮箱: [<EMAIL>]
- 微信: [your-wechat-id]

---

**愿灵符护佑，诸事顺遂！** ✨🙏
