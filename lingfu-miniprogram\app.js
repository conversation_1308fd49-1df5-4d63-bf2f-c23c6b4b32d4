// app.js
App({
  onLaunch() {
    // 初始化云开发
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力')
    } else {
      wx.cloud.init({
        env: 'lingfu-prod', // 云开发环境ID
        traceUser: true,
      })
    }

    // 检查更新
    this.checkUpdate()
    
    // 初始化用户数据
    this.initUserData()
  },

  onShow() {
    // 小程序显示时的处理
    console.log('灵符驾到小程序启动')
  },

  checkUpdate() {
    const updateManager = wx.getUpdateManager()
    
    updateManager.onCheckForUpdate((res) => {
      if (res.hasUpdate) {
        updateManager.onUpdateReady(() => {
          wx.showModal({
            title: '更新提示',
            content: '新版本已经准备好，是否重启应用？',
            success: (res) => {
              if (res.confirm) {
                updateManager.applyUpdate()
              }
            }
          })
        })
      }
    })
  },

  initUserData() {
    // 初始化用户收藏数据
    const collections = wx.getStorageSync('collections') || []
    const drawHistory = wx.getStorageSync('drawHistory') || []
    
    this.globalData.collections = collections
    this.globalData.drawHistory = drawHistory
  },

  globalData: {
    userInfo: null,
    collections: [], // 用户收藏的灵符
    drawHistory: [], // 抽签历史
    categories: [
      { id: 'luck', name: '趋吉避凶', icon: '🛡️', color: '#FF6B6B' },
      { id: 'study', name: '升学就业', icon: '📚', color: '#4ECDC4' },
      { id: 'business', name: '生意创意', icon: '💰', color: '#45B7D1' },
      { id: 'health', name: '身体康健', icon: '🌿', color: '#96CEB4' },
      { id: 'love', name: '感情姻缘', icon: '💕', color: '#FFEAA7' },
      { id: 'family', name: '家庭和睦', icon: '🏠', color: '#DDA0DD' }
    ]
  }
})
