/* app.wxss */
@import './styles/common.wxss';

/* 全局样式 */
page {
  background: linear-gradient(135deg, #FFF8DC 0%, #F5DEB3 100%);
  font-family: 'PingFang SC', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  color: #8B4513;
}

/* 容器样式 */
.container {
  padding: 20rpx;
  min-height: 100vh;
}

/* 卡片样式 */
.card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(139, 69, 19, 0.1);
  margin: 20rpx 0;
  padding: 30rpx;
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(220, 20, 60, 0.1);
}

/* 按钮样式 */
.btn-primary {
  background: linear-gradient(135deg, #DC143C 0%, #B22222 100%);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 8rpx 16rpx rgba(220, 20, 60, 0.3);
  transition: all 0.3s ease;
}

.btn-primary:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 8rpx rgba(220, 20, 60, 0.3);
}

.btn-secondary {
  background: linear-gradient(135deg, #DAA520 0%, #B8860B 100%);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
  box-shadow: 0 6rpx 12rpx rgba(218, 165, 32, 0.3);
}

/* 文本样式 */
.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #8B4513;
  text-align: center;
  margin: 40rpx 0;
  text-shadow: 2rpx 2rpx 4rpx rgba(139, 69, 19, 0.1);
}

.subtitle {
  font-size: 36rpx;
  font-weight: 600;
  color: #DC143C;
  margin: 30rpx 0 20rpx 0;
}

.text-center {
  text-align: center;
}

.text-gold {
  color: #DAA520;
  font-weight: bold;
}

.text-red {
  color: #DC143C;
  font-weight: bold;
}

/* 布局样式 */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  justify-content: center;
  align-items: center;
}

.flex-between {
  justify-content: space-between;
  align-items: center;
}

.flex-wrap {
  flex-wrap: wrap;
}

/* 动画样式 */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

.bounce {
  animation: bounce 0.6s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    transform: translateY(100rpx);
    opacity: 0;
  }
  to { 
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes bounce {
  0%, 20%, 60%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-20rpx);
  }
  80% {
    transform: translateY(-10rpx);
  }
}

/* 加载动画 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #DC143C;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
