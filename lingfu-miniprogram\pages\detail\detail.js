// pages/detail/detail.js
const app = getApp()
const drawUtil = require('../../utils/draw.js')

Page({
  data: {
    lingfu: null,
    isCollected: false,
    showShareModal: false,
    recommendLingfu: [],
    rarityText: '',
    categoryNames: []
  },

  onLoad(options) {
    const { id } = options
    if (id) {
      this.loadLingfuDetail(id)
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  onShow() {
    // 检查收藏状态
    if (this.data.lingfu) {
      this.checkCollectionStatus()
    }
  },

  // 加载灵符详情
  loadLingfuDetail(id) {
    wx.showLoading({
      title: '加载中...'
    })

    try {
      const lingfu = drawUtil.getLingfuById(id)
      
      if (lingfu) {
        // 处理稀有度文本
        const rarityMap = {
          'common': '普通',
          'rare': '稀有',
          'legendary': '传说'
        }
        
        // 处理分类名称
        const categoryNames = lingfu.category.map(catId => {
          const category = app.globalData.categories.find(cat => cat.id === catId)
          return category ? category.name : catId
        })

        this.setData({
          lingfu,
          rarityText: rarityMap[lingfu.rarity] || '未知',
          categoryNames
        })

        // 检查收藏状态
        this.checkCollectionStatus()
        
        // 加载相关推荐
        this.loadRecommendLingfu()
        
        // 设置页面标题
        wx.setNavigationBarTitle({
          title: lingfu.name
        })
      } else {
        wx.showToast({
          title: '灵符不存在',
          icon: 'none'
        })
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      }
    } catch (error) {
      console.error('加载灵符详情失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
    }
  },

  // 检查收藏状态
  checkCollectionStatus() {
    const collections = wx.getStorageSync('collections') || []
    const isCollected = collections.some(item => item.id === this.data.lingfu.id)
    this.setData({ isCollected })
  },

  // 加载相关推荐
  loadRecommendLingfu() {
    const currentLingfu = this.data.lingfu
    if (!currentLingfu) return

    // 获取同分类的其他灵符
    let recommendList = []
    
    currentLingfu.category.forEach(catId => {
      const categoryLingfu = drawUtil.getLingfuByCategory(catId)
      recommendList = recommendList.concat(
        categoryLingfu.filter(item => item.id !== currentLingfu.id)
      )
    })

    // 去重并限制数量
    const uniqueRecommend = recommendList.filter((item, index, self) => 
      index === self.findIndex(t => t.id === item.id)
    ).slice(0, 5)

    // 添加稀有度文本
    const rarityMap = {
      'common': '普通',
      'rare': '稀有', 
      'legendary': '传说'
    }

    const processedRecommend = uniqueRecommend.map(item => ({
      ...item,
      rarityText: rarityMap[item.rarity] || '未知'
    }))

    this.setData({
      recommendLingfu: processedRecommend
    })
  },

  // 切换收藏状态
  toggleCollection() {
    const { lingfu, isCollected } = this.data
    let collections = wx.getStorageSync('collections') || []

    if (isCollected) {
      // 取消收藏
      collections = collections.filter(item => item.id !== lingfu.id)
      wx.showToast({
        title: '已取消收藏',
        icon: 'success'
      })
    } else {
      // 添加收藏
      collections.unshift(lingfu)
      wx.showToast({
        title: '收藏成功',
        icon: 'success'
      })
    }

    wx.setStorageSync('collections', collections)
    app.globalData.collections = collections
    
    this.setData({
      isCollected: !isCollected
    })
  },

  // 分享给朋友
  shareToFriend() {
    this.setData({
      showShareModal: true
    })
  },

  // 关闭分享弹窗
  closeShareModal() {
    this.setData({
      showShareModal: false
    })
  },

  // 分享到朋友圈
  shareToMoments() {
    // 微信小程序不支持直接分享到朋友圈，这里可以提示用户截图分享
    wx.showModal({
      title: '分享提示',
      content: '请截图保存后分享到朋友圈，让更多朋友感受灵符的神奇力量！',
      confirmText: '知道了',
      showCancel: false
    })
    this.closeShareModal()
  },

  // 保存到相册
  saveToAlbum() {
    const { lingfu } = this.data
    if (!lingfu || !lingfu.image) {
      wx.showToast({
        title: '图片加载中',
        icon: 'none'
      })
      return
    }

    wx.showLoading({
      title: '保存中...'
    })

    // 下载图片
    wx.downloadFile({
      url: lingfu.image,
      success: (res) => {
        if (res.statusCode === 200) {
          // 保存到相册
          wx.saveImageToPhotosAlbum({
            filePath: res.tempFilePath,
            success: () => {
              wx.showToast({
                title: '保存成功',
                icon: 'success'
              })
            },
            fail: (error) => {
              if (error.errMsg.includes('auth deny')) {
                wx.showModal({
                  title: '提示',
                  content: '需要授权访问相册才能保存图片',
                  confirmText: '去授权',
                  success: (res) => {
                    if (res.confirm) {
                      wx.openSetting()
                    }
                  }
                })
              } else {
                wx.showToast({
                  title: '保存失败',
                  icon: 'none'
                })
              }
            }
          })
        } else {
          wx.showToast({
            title: '下载失败',
            icon: 'none'
          })
        }
      },
      fail: () => {
        wx.showToast({
          title: '下载失败',
          icon: 'none'
        })
      },
      complete: () => {
        wx.hideLoading()
      }
    })
  },

  // 查看推荐灵符
  viewRecommend(e) {
    const { id } = e.currentTarget.dataset
    wx.redirectTo({
      url: `/pages/detail/detail?id=${id}`
    })
  },

  // 页面分享
  onShareAppMessage() {
    const { lingfu } = this.data
    return {
      title: `${lingfu.name} - 灵符驾到`,
      path: `/pages/detail/detail?id=${lingfu.id}`,
      imageUrl: lingfu.image
    }
  },

  // 分享到朋友圈
  onShareTimeline() {
    const { lingfu } = this.data
    return {
      title: `我抽到了${lingfu.name}，快来灵符驾到抽取你的专属灵符吧！`,
      imageUrl: lingfu.image
    }
  }
})
