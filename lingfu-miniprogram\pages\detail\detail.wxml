<!--pages/detail/detail.wxml-->
<view class="container">
  <view wx:if="{{lingfu}}" class="detail-content">
    <!-- 灵符图片 -->
    <view class="image-section">
      <image class="lingfu-image" src="{{lingfu.image}}" mode="aspectFit"></image>
      <view class="rarity-badge rarity-{{lingfu.rarity}}">
        <text class="rarity-text">{{rarityText}}</text>
      </view>
    </view>

    <!-- 基本信息 -->
    <view class="info-section">
      <text class="lingfu-name">{{lingfu.name}}</text>
      <view class="category-tags">
        <text 
          wx:for="{{categoryNames}}" 
          wx:key="*this"
          class="category-tag"
        >
          {{item}}
        </text>
      </view>
      <view class="power-bar">
        <text class="power-label">灵力值</text>
        <view class="power-progress">
          <view class="power-fill" style="width: {{lingfu.power}}%"></view>
        </view>
        <text class="power-value">{{lingfu.power}}</text>
      </view>
    </view>

    <!-- 详细描述 -->
    <view class="description-section card">
      <text class="section-title">符咒详解</text>
      <text class="description-text">{{lingfu.description}}</text>
    </view>

    <!-- 使用方法 -->
    <view class="usage-section card">
      <text class="section-title">使用方法</text>
      <text class="usage-text">{{lingfu.usage}}</text>
    </view>

    <!-- 祝福语 -->
    <view class="blessing-section card">
      <text class="section-title">祝福寄语</text>
      <text class="blessing-text">{{lingfu.blessing}}</text>
    </view>

    <!-- 抽取信息 -->
    <view class="draw-info-section card" wx:if="{{lingfu.drawTime}}">
      <text class="section-title">抽取信息</text>
      <view class="draw-info">
        <view class="info-item">
          <text class="info-label">抽取时间：</text>
          <text class="info-value">{{lingfu.drawTime}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">稀有程度：</text>
          <text class="info-value rarity-{{lingfu.rarity}}">{{rarityText}}</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-section">
      <button 
        class="btn-secondary action-btn" 
        bindtap="toggleCollection"
      >
        {{isCollected ? '取消收藏' : '收藏此符'}}
      </button>
      <button 
        class="btn-primary action-btn" 
        bindtap="shareToFriend"
      >
        分享给朋友
      </button>
      <button 
        class="btn-secondary action-btn" 
        bindtap="saveToAlbum"
      >
        保存到相册
      </button>
    </view>

    <!-- 相关推荐 -->
    <view class="recommend-section" wx:if="{{recommendLingfu.length > 0}}">
      <text class="section-title">相关推荐</text>
      <scroll-view class="recommend-scroll" scroll-x="true">
        <view class="recommend-list">
          <view 
            wx:for="{{recommendLingfu}}" 
            wx:key="id"
            class="recommend-item"
            bindtap="viewRecommend"
            data-id="{{item.id}}"
          >
            <image class="recommend-image" src="{{item.image}}" mode="aspectFit"></image>
            <text class="recommend-name">{{item.name}}</text>
            <text class="recommend-rarity rarity-{{item.rarity}}">{{item.rarityText}}</text>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:else class="loading-section">
    <view class="loading">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在加载灵符详情...</text>
    </view>
  </view>
</view>

<!-- 分享弹窗 -->
<view class="modal {{showShareModal ? 'show' : ''}}" catchtap="closeShareModal">
  <view class="modal-content share-modal" catchtap="">
    <view class="share-header">
      <text class="share-title">分享灵符</text>
      <text class="close-btn" bindtap="closeShareModal">×</text>
    </view>
    <view class="share-body">
      <view class="share-preview">
        <image class="share-image" src="{{lingfu.image}}" mode="aspectFit"></image>
        <text class="share-name">{{lingfu.name}}</text>
      </view>
      <view class="share-options">
        <button class="share-btn" open-type="share">
          <text class="share-icon">📱</text>
          <text class="share-text">分享给好友</text>
        </button>
        <button class="share-btn" bindtap="shareToMoments">
          <text class="share-icon">🌟</text>
          <text class="share-text">分享到朋友圈</text>
        </button>
      </view>
    </view>
  </view>
</view>
