// pages/index/index.js
const app = getApp()
const drawUtil = require('../../utils/draw.js')
const voiceUtil = require('../../utils/voice.js')

Page({
  data: {
    categories: [],
    selectedCategory: '',
    currentMethod: 'shake', // 当前抽签方式: shake, voice, category
    shaking: false,
    drawing: false,
    showResult: false,
    currentLingfu: null,
    recentDraw: null,
    dailyDrawCount: 0,
    maxDailyDraw: 3,
    // 声控相关
    voiceListening: false,
    currentVolume: 0,
    voiceDetector: null
  },

  onLoad() {
    this.setData({
      categories: app.globalData.categories
    })
    this.loadRecentDraw()
    this.checkDailyDrawCount()
    this.initShakeDetector()
    this.initVoiceDetector()
  },

  onShow() {
    this.loadRecentDraw()
  },

  onHide() {
    // 页面隐藏时停止声控监听
    this.stopVoiceListening()
  },

  onUnload() {
    // 页面卸载时停止声控监听
    this.stopVoiceListening()
  },

  // 初始化摇一摇检测
  initShakeDetector() {
    let lastTime = 0
    let lastX = 0, lastY = 0, lastZ = 0
    
    wx.onAccelerometerChange((res) => {
      const currentTime = Date.now()
      if (currentTime - lastTime > 100) {
        const deltaTime = currentTime - lastTime
        lastTime = currentTime
        
        const deltaX = res.x - lastX
        const deltaY = res.y - lastY
        const deltaZ = res.z - lastZ
        
        const speed = Math.abs(deltaX + deltaY + deltaZ) / deltaTime * 10000
        
        if (speed > 300 && !this.data.drawing) {
          this.onShake()
        }
        
        lastX = res.x
        lastY = res.y
        lastZ = res.z
      }
    })
    
    wx.startAccelerometer({
      interval: 'normal'
    })
  },

  // 初始化声控检测
  initVoiceDetector() {
    this.voiceDetector = voiceUtil.initVoiceDetector()
  },

  // 切换抽签方式
  switchMethod(e) {
    const method = e.currentTarget.dataset.method
    this.setData({ currentMethod: method })

    // 切换时停止当前的监听
    if (method !== 'voice') {
      this.stopVoiceListening()
    }
  },

  // 摇一摇触发
  onShake() {
    if (this.data.currentMethod !== 'shake') return
    if (this.data.drawing || this.data.dailyDrawCount >= this.data.maxDailyDraw) {
      if (this.data.dailyDrawCount >= this.data.maxDailyDraw) {
        wx.showToast({
          title: '今日抽签次数已用完',
          icon: 'none'
        })
      }
      return
    }

    this.setData({ shaking: true })

    // 震动反馈
    wx.vibrateShort()

    setTimeout(() => {
      this.setData({ shaking: false })
      this.drawLingfu('shake')
    }, 1000)
  },

  // 切换声控监听
  toggleVoiceListening() {
    if (this.data.voiceListening) {
      this.stopVoiceListening()
    } else {
      this.startVoiceListening()
    }
  },

  // 开始声控监听
  startVoiceListening() {
    if (this.data.drawing || this.data.dailyDrawCount >= this.data.maxDailyDraw) {
      if (this.data.dailyDrawCount >= this.data.maxDailyDraw) {
        wx.showToast({
          title: '今日抽签次数已用完',
          icon: 'none'
        })
      }
      return
    }

    voiceUtil.startVoiceListening(
      // 吹气检测回调
      () => {
        if (!this.data.drawing) {
          wx.vibrateShort()
          this.drawLingfu('voice')
        }
      },
      // 音量变化回调
      (volume) => {
        this.setData({ currentVolume: volume })
      }
    )

    this.setData({ voiceListening: true })

    wx.showToast({
      title: '声控已开启，请吹气',
      icon: 'none'
    })
  },

  // 停止声控监听
  stopVoiceListening() {
    voiceUtil.stopVoiceListening()
    this.setData({
      voiceListening: false,
      currentVolume: 0
    })
  },

  // 选择分类
  selectCategory(e) {
    const category = e.currentTarget.dataset.category
    const newCategory = this.data.selectedCategory === category ? '' : category
    this.setData({
      selectedCategory: newCategory
    })

    // 如果是分类模式且选择了分类，自动抽签
    if (this.data.currentMethod === 'category' && newCategory) {
      setTimeout(() => {
        this.drawLingfu('category')
      }, 500)
    }
  },

  // 抽取灵符
  async drawLingfu(method = 'manual') {
    if (this.data.drawing || this.data.dailyDrawCount >= this.data.maxDailyDraw) {
      return
    }

    this.setData({ drawing: true })

    // 根据抽签方式确定分类
    let category = ''
    if (method === 'category' || this.data.currentMethod === 'category') {
      category = this.data.selectedCategory
    }

    try {
      // 播放音效
      this.playDrawSound()

      // 调用抽签逻辑
      const lingfu = await drawUtil.drawRandomLingfu(category)

      if (lingfu) {
        this.setData({
          currentLingfu: lingfu,
          showResult: true,
          dailyDrawCount: this.data.dailyDrawCount + 1
        })

        // 保存抽签记录
        this.saveDrawRecord(lingfu, method)
        this.saveDailyDrawCount()

        // 如果是声控模式，停止监听
        if (method === 'voice') {
          this.stopVoiceListening()
        }
      } else {
        wx.showToast({
          title: '抽签失败，请重试',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('抽签失败:', error)
      wx.showToast({
        title: '抽签失败，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({ drawing: false })
    }
  },

  // 播放抽签音效
  playDrawSound() {
    const audioContext = wx.createInnerAudioContext()
    audioContext.src = '../../audio/draw.mp3'
    audioContext.play()
  },

  // 保存抽签记录
  saveDrawRecord(lingfu, method = 'manual') {
    const methodNames = {
      'shake': '摇一摇',
      'voice': '声控',
      'category': '分类选择',
      'manual': '手动'
    }

    const drawRecord = {
      ...lingfu,
      drawTime: new Date().toLocaleString(),
      drawMethod: methodNames[method] || '未知',
      categoryName: this.getCategoryName(lingfu.category[0])
    }

    // 保存到本地存储
    let history = wx.getStorageSync('drawHistory') || []
    history.unshift(drawRecord)
    if (history.length > 50) {
      history = history.slice(0, 50) // 只保留最近50条记录
    }
    wx.setStorageSync('drawHistory', history)

    // 更新全局数据
    app.globalData.drawHistory = history

    this.setData({ recentDraw: drawRecord })
  },

  // 获取分类名称
  getCategoryName(categoryId) {
    const category = this.data.categories.find(cat => cat.id === categoryId)
    return category ? category.name : '未知分类'
  },

  // 加载最近抽取记录
  loadRecentDraw() {
    const history = wx.getStorageSync('drawHistory') || []
    if (history.length > 0) {
      this.setData({ recentDraw: history[0] })
    }
  },

  // 检查每日抽签次数
  checkDailyDrawCount() {
    const today = new Date().toDateString()
    const lastDrawDate = wx.getStorageSync('lastDrawDate')
    
    if (lastDrawDate !== today) {
      // 新的一天，重置次数
      this.setData({ dailyDrawCount: 0 })
      wx.setStorageSync('lastDrawDate', today)
      wx.setStorageSync('dailyDrawCount', 0)
    } else {
      // 同一天，读取已抽次数
      const count = wx.getStorageSync('dailyDrawCount') || 0
      this.setData({ dailyDrawCount: count })
    }
  },

  // 保存每日抽签次数
  saveDailyDrawCount() {
    wx.setStorageSync('dailyDrawCount', this.data.dailyDrawCount)
  },

  // 关闭结果弹窗
  closeResult() {
    this.setData({ showResult: false })
  },

  // 收藏灵符
  collectLingfu() {
    if (!this.data.currentLingfu) return
    
    let collections = wx.getStorageSync('collections') || []
    const exists = collections.find(item => item.id === this.data.currentLingfu.id)
    
    if (!exists) {
      collections.unshift(this.data.currentLingfu)
      wx.setStorageSync('collections', collections)
      app.globalData.collections = collections
      
      wx.showToast({
        title: '收藏成功',
        icon: 'success'
      })
    } else {
      wx.showToast({
        title: '已收藏过此灵符',
        icon: 'none'
      })
    }
  },

  // 查看详情
  viewDetail() {
    const lingfu = this.data.currentLingfu || this.data.recentDraw
    if (lingfu) {
      wx.navigateTo({
        url: `/pages/detail/detail?id=${lingfu.id}`
      })
    }
  },

  // 分享灵符
  shareLingfu() {
    if (!this.data.currentLingfu) return
    
    wx.showShareMenu({
      withShareTicket: true
    })
  },

  // 页面分享
  onShareAppMessage() {
    const lingfu = this.data.currentLingfu
    if (lingfu) {
      return {
        title: `我抽到了${lingfu.name}，一起来抽灵符吧！`,
        path: '/pages/index/index',
        imageUrl: lingfu.image
      }
    }
    return {
      title: '灵符驾到 - 诚心祈愿，灵符护佑',
      path: '/pages/index/index'
    }
  }
})
