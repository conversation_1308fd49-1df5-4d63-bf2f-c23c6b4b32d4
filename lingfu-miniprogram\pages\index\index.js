// pages/index/index.js
const app = getApp()
const drawUtil = require('../../utils/draw.js')

Page({
  data: {
    categories: [],
    selectedCategory: '',
    shaking: false,
    drawing: false,
    showResult: false,
    currentLingfu: null,
    recentDraw: null,
    dailyDrawCount: 0,
    maxDailyDraw: 3
  },

  onLoad() {
    this.setData({
      categories: app.globalData.categories
    })
    this.loadRecentDraw()
    this.checkDailyDrawCount()
    this.initShakeDetector()
  },

  onShow() {
    this.loadRecentDraw()
  },

  // 初始化摇一摇检测
  initShakeDetector() {
    let lastTime = 0
    let lastX = 0, lastY = 0, lastZ = 0
    
    wx.onAccelerometerChange((res) => {
      const currentTime = Date.now()
      if (currentTime - lastTime > 100) {
        const deltaTime = currentTime - lastTime
        lastTime = currentTime
        
        const deltaX = res.x - lastX
        const deltaY = res.y - lastY
        const deltaZ = res.z - lastZ
        
        const speed = Math.abs(deltaX + deltaY + deltaZ) / deltaTime * 10000
        
        if (speed > 300 && !this.data.drawing) {
          this.onShake()
        }
        
        lastX = res.x
        lastY = res.y
        lastZ = res.z
      }
    })
    
    wx.startAccelerometer({
      interval: 'normal'
    })
  },

  // 摇一摇触发
  onShake() {
    if (this.data.drawing || this.data.dailyDrawCount >= this.data.maxDailyDraw) {
      if (this.data.dailyDrawCount >= this.data.maxDailyDraw) {
        wx.showToast({
          title: '今日抽签次数已用完',
          icon: 'none'
        })
      }
      return
    }

    this.setData({ shaking: true })
    
    // 震动反馈
    wx.vibrateShort()
    
    setTimeout(() => {
      this.setData({ shaking: false })
      this.drawLingfu()
    }, 1000)
  },

  // 选择分类
  selectCategory(e) {
    const category = e.currentTarget.dataset.category
    this.setData({
      selectedCategory: this.data.selectedCategory === category ? '' : category
    })
  },

  // 抽取灵符
  async drawLingfu() {
    if (this.data.drawing || this.data.dailyDrawCount >= this.data.maxDailyDraw) {
      return
    }

    this.setData({ drawing: true })

    try {
      // 播放音效
      this.playDrawSound()
      
      // 调用抽签逻辑
      const lingfu = await drawUtil.drawRandomLingfu(this.data.selectedCategory)
      
      if (lingfu) {
        this.setData({
          currentLingfu: lingfu,
          showResult: true,
          dailyDrawCount: this.data.dailyDrawCount + 1
        })
        
        // 保存抽签记录
        this.saveDrawRecord(lingfu)
        this.saveDailyDrawCount()
      } else {
        wx.showToast({
          title: '抽签失败，请重试',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('抽签失败:', error)
      wx.showToast({
        title: '抽签失败，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({ drawing: false })
    }
  },

  // 播放抽签音效
  playDrawSound() {
    const audioContext = wx.createInnerAudioContext()
    audioContext.src = '../../audio/draw.mp3'
    audioContext.play()
  },

  // 保存抽签记录
  saveDrawRecord(lingfu) {
    const drawRecord = {
      ...lingfu,
      drawTime: new Date().toLocaleString(),
      categoryName: this.getCategoryName(lingfu.category[0])
    }
    
    // 保存到本地存储
    let history = wx.getStorageSync('drawHistory') || []
    history.unshift(drawRecord)
    if (history.length > 50) {
      history = history.slice(0, 50) // 只保留最近50条记录
    }
    wx.setStorageSync('drawHistory', history)
    
    // 更新全局数据
    app.globalData.drawHistory = history
    
    this.setData({ recentDraw: drawRecord })
  },

  // 获取分类名称
  getCategoryName(categoryId) {
    const category = this.data.categories.find(cat => cat.id === categoryId)
    return category ? category.name : '未知分类'
  },

  // 加载最近抽取记录
  loadRecentDraw() {
    const history = wx.getStorageSync('drawHistory') || []
    if (history.length > 0) {
      this.setData({ recentDraw: history[0] })
    }
  },

  // 检查每日抽签次数
  checkDailyDrawCount() {
    const today = new Date().toDateString()
    const lastDrawDate = wx.getStorageSync('lastDrawDate')
    
    if (lastDrawDate !== today) {
      // 新的一天，重置次数
      this.setData({ dailyDrawCount: 0 })
      wx.setStorageSync('lastDrawDate', today)
      wx.setStorageSync('dailyDrawCount', 0)
    } else {
      // 同一天，读取已抽次数
      const count = wx.getStorageSync('dailyDrawCount') || 0
      this.setData({ dailyDrawCount: count })
    }
  },

  // 保存每日抽签次数
  saveDailyDrawCount() {
    wx.setStorageSync('dailyDrawCount', this.data.dailyDrawCount)
  },

  // 关闭结果弹窗
  closeResult() {
    this.setData({ showResult: false })
  },

  // 收藏灵符
  collectLingfu() {
    if (!this.data.currentLingfu) return
    
    let collections = wx.getStorageSync('collections') || []
    const exists = collections.find(item => item.id === this.data.currentLingfu.id)
    
    if (!exists) {
      collections.unshift(this.data.currentLingfu)
      wx.setStorageSync('collections', collections)
      app.globalData.collections = collections
      
      wx.showToast({
        title: '收藏成功',
        icon: 'success'
      })
    } else {
      wx.showToast({
        title: '已收藏过此灵符',
        icon: 'none'
      })
    }
  },

  // 查看详情
  viewDetail() {
    const lingfu = this.data.currentLingfu || this.data.recentDraw
    if (lingfu) {
      wx.navigateTo({
        url: `/pages/detail/detail?id=${lingfu.id}`
      })
    }
  },

  // 分享灵符
  shareLingfu() {
    if (!this.data.currentLingfu) return
    
    wx.showShareMenu({
      withShareTicket: true
    })
  },

  // 页面分享
  onShareAppMessage() {
    const lingfu = this.data.currentLingfu
    if (lingfu) {
      return {
        title: `我抽到了${lingfu.name}，一起来抽灵符吧！`,
        path: '/pages/index/index',
        imageUrl: lingfu.image
      }
    }
    return {
      title: '灵符驾到 - 诚心祈愿，灵符护佑',
      path: '/pages/index/index'
    }
  }
})
