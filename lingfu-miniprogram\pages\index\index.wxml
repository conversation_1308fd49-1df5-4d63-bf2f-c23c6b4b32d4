<!--pages/index/index.wxml-->
<view class="container">
  <!-- 顶部标题 -->
  <view class="header">
    <text class="title">灵符驾到</text>
    <text class="subtitle">诚心祈愿，灵符护佑</text>
  </view>

  <!-- 抽签方式选择 -->
  <view class="draw-methods">
    <view class="method-tabs">
      <view
        class="method-tab {{currentMethod === 'shake' ? 'active' : ''}}"
        bindtap="switchMethod"
        data-method="shake"
      >
        摇一摇
      </view>
      <view
        class="method-tab {{currentMethod === 'voice' ? 'active' : ''}}"
        bindtap="switchMethod"
        data-method="voice"
      >
        声控
      </view>
      <view
        class="method-tab {{currentMethod === 'category' ? 'active' : ''}}"
        bindtap="switchMethod"
        data-method="category"
      >
        分类
      </view>
    </view>
  </view>

  <!-- 摇一摇区域 -->
  <view class="shake-area {{shaking ? 'shaking' : ''}} {{currentMethod === 'shake' ? 'show' : 'hide'}}" bindtap="onShake">
    <view class="shake-circle">
      <text class="shake-icon">🎲</text>
      <text class="shake-text">{{shaking ? '正在抽取...' : '摇一摇抽灵符'}}</text>
    </view>
    <view class="shake-hint">
      <text>轻摇手机或点击上方图标</text>
    </view>
  </view>

  <!-- 声控区域 -->
  <view class="voice-area {{voiceListening ? 'listening' : ''}} {{currentMethod === 'voice' ? 'show' : 'hide'}}">
    <view class="voice-circle" bindtap="toggleVoiceListening">
      <text class="voice-icon">🎤</text>
      <text class="voice-text">{{voiceListening ? '对着手机吹口仙气' : '点击开启声控'}}</text>
      <view class="volume-indicator" wx:if="{{voiceListening}}">
        <view class="volume-bar">
          <view class="volume-fill" style="width: {{currentVolume * 100}}%"></view>
        </view>
      </view>
    </view>
    <view class="voice-hint">
      <text>{{voiceListening ? '轻轻吹气即可抽取灵符' : '需要录音权限'}}</text>
    </view>
  </view>

  <!-- 分类选择 -->
  <view class="category-section {{currentMethod === 'category' ? 'show' : 'hide'}}">
    <text class="section-title">选择祈愿类别</text>
    <view class="category-grid">
      <view
        wx:for="{{categories}}"
        wx:key="id"
        class="category-item {{selectedCategory === item.id ? 'selected' : ''}}"
        style="border-color: {{item.color}}"
        bindtap="selectCategory"
        data-category="{{item.id}}"
      >
        <text class="category-icon">{{item.icon}}</text>
        <text class="category-name">{{item.name}}</text>
      </view>
    </view>
  </view>

  <!-- 抽签按钮 -->
  <view class="draw-section">
    <button 
      class="btn-primary draw-btn {{drawing ? 'drawing' : ''}}" 
      bindtap="drawLingfu"
      disabled="{{drawing}}"
    >
      {{drawing ? '正在抽取灵符...' : '抽取灵符'}}
    </button>
  </view>

  <!-- 最近抽取 -->
  <view class="recent-section" wx:if="{{recentDraw}}">
    <text class="section-title">最近抽取</text>
    <view class="recent-card" bindtap="viewDetail" data-lingfu="{{recentDraw}}">
      <image class="recent-image" src="{{recentDraw.image}}" mode="aspectFit"></image>
      <view class="recent-info">
        <text class="recent-name">{{recentDraw.name}}</text>
        <text class="recent-category">{{recentDraw.categoryName}}</text>
        <text class="recent-time">{{recentDraw.drawTime}}</text>
      </view>
    </view>
  </view>

  <!-- 使用说明 -->
  <view class="instruction-section">
    <text class="section-title">使用说明</text>
    <view class="instruction-list">
      <view class="instruction-item">
        <text class="instruction-icon">🙏</text>
        <text class="instruction-text">诚心祈愿，心诚则灵</text>
      </view>
      <view class="instruction-item">
        <text class="instruction-icon">📱</text>
        <text class="instruction-text">摇动手机或选择类别抽取</text>
      </view>
      <view class="instruction-item">
        <text class="instruction-icon">✨</text>
        <text class="instruction-text">每日限抽3次，珍惜机缘</text>
      </view>
    </view>
  </view>
</view>

<!-- 抽签结果弹窗 -->
<view class="modal {{showResult ? 'show' : ''}}" catchtap="closeResult">
  <view class="modal-content" catchtap="">
    <view class="result-header">
      <text class="result-title">灵符显现</text>
      <text class="close-btn" bindtap="closeResult">×</text>
    </view>
    <view class="result-body" wx:if="{{currentLingfu}}">
      <image class="result-image" src="{{currentLingfu.image}}" mode="aspectFit"></image>
      <text class="result-name">{{currentLingfu.name}}</text>
      <text class="result-description">{{currentLingfu.description}}</text>
      <text class="result-blessing">{{currentLingfu.blessing}}</text>
    </view>
    <view class="result-actions">
      <button class="btn-secondary" bindtap="collectLingfu">收藏</button>
      <button class="btn-primary" bindtap="viewDetail">查看详情</button>
      <button class="btn-secondary" bindtap="shareLingfu">分享</button>
    </view>
  </view>
</view>
