/* pages/index/index.wxss */

.header {
  text-align: center;
  margin: 40rpx 0 60rpx 0;
}

.title {
  display: block;
  font-size: 56rpx;
  font-weight: bold;
  color: #8B4513;
  margin-bottom: 20rpx;
  text-shadow: 2rpx 2rpx 4rpx rgba(139, 69, 19, 0.2);
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: #DC143C;
  opacity: 0.8;
}

/* 摇一摇区域 */
.shake-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 60rpx 0;
  transition: all 0.3s ease;
}

.shake-area.shaking {
  animation: shake 0.5s ease-in-out infinite;
}

.shake-circle {
  width: 300rpx;
  height: 300rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 20rpx 40rpx rgba(255, 215, 0, 0.3);
  border: 8rpx solid rgba(220, 20, 60, 0.2);
  position: relative;
  overflow: hidden;
}

.shake-circle::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: rotate 3s linear infinite;
}

.shake-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  z-index: 1;
}

.shake-text {
  font-size: 28rpx;
  color: #8B4513;
  font-weight: bold;
  z-index: 1;
}

.shake-hint {
  margin-top: 30rpx;
  font-size: 24rpx;
  color: #999;
}

/* 分类选择 */
.category-section {
  margin: 60rpx 0;
}

.section-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #8B4513;
  margin-bottom: 30rpx;
  text-align: center;
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.category-item {
  background: rgba(255, 255, 255, 0.8);
  border: 3rpx solid #ddd;
  border-radius: 20rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(10rpx);
}

.category-item.selected {
  border-color: #DC143C;
  background: rgba(220, 20, 60, 0.1);
  transform: scale(1.05);
}

.category-icon {
  display: block;
  font-size: 48rpx;
  margin-bottom: 15rpx;
}

.category-name {
  display: block;
  font-size: 28rpx;
  color: #8B4513;
  font-weight: 600;
}

/* 抽签按钮 */
.draw-section {
  text-align: center;
  margin: 60rpx 0;
}

.draw-btn {
  width: 400rpx;
  height: 100rpx;
  font-size: 36rpx;
  position: relative;
  overflow: hidden;
}

.draw-btn.drawing {
  animation: pulse 1s ease-in-out infinite;
}

/* 最近抽取 */
.recent-section {
  margin: 60rpx 0;
}

.recent-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 8rpx 32rpx rgba(139, 69, 19, 0.1);
  border: 2rpx solid rgba(220, 20, 60, 0.1);
}

.recent-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 15rpx;
  margin-right: 30rpx;
}

.recent-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.recent-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #8B4513;
  margin-bottom: 10rpx;
}

.recent-category {
  font-size: 24rpx;
  color: #DC143C;
  margin-bottom: 10rpx;
}

.recent-time {
  font-size: 22rpx;
  color: #999;
}

/* 使用说明 */
.instruction-section {
  margin: 60rpx 0;
}

.instruction-list {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 20rpx;
  padding: 30rpx;
}

.instruction-item {
  display: flex;
  align-items: center;
  margin-bottom: 25rpx;
}

.instruction-item:last-child {
  margin-bottom: 0;
}

.instruction-icon {
  font-size: 36rpx;
  margin-right: 20rpx;
  width: 60rpx;
  text-align: center;
}

.instruction-text {
  font-size: 28rpx;
  color: #8B4513;
  flex: 1;
}

/* 弹窗样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: linear-gradient(135deg, #FFF8DC 0%, #F5DEB3 100%);
  border-radius: 30rpx;
  width: 90%;
  max-width: 600rpx;
  max-height: 80%;
  overflow-y: auto;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
  border: 3rpx solid #DC143C;
  animation: modalSlideIn 0.3s ease-out;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 2rpx solid rgba(220, 20, 60, 0.2);
}

.result-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #8B4513;
}

.close-btn {
  font-size: 48rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.result-body {
  padding: 40rpx;
  text-align: center;
}

.result-image {
  width: 300rpx;
  height: 400rpx;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 10rpx 30rpx rgba(139, 69, 19, 0.2);
}

.result-name {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  color: #DC143C;
  margin-bottom: 20rpx;
}

.result-description {
  display: block;
  font-size: 28rpx;
  color: #8B4513;
  line-height: 1.6;
  margin-bottom: 30rpx;
}

.result-blessing {
  display: block;
  font-size: 32rpx;
  color: #DAA520;
  font-weight: bold;
  font-style: italic;
}

.result-actions {
  display: flex;
  justify-content: space-around;
  padding: 30rpx;
  border-top: 2rpx solid rgba(220, 20, 60, 0.2);
}

.result-actions button {
  flex: 1;
  margin: 0 10rpx;
  height: 80rpx;
  font-size: 28rpx;
}

/* 动画 */
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-10rpx); }
  75% { transform: translateX(10rpx); }
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes modalSlideIn {
  from {
    transform: translateY(100rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
