// pages/library/library.js
const app = getApp()
const drawUtil = require('../../utils/draw.js')

Page({
  data: {
    categories: [],
    allLingfu: [],
    filteredLingfu: [],
    searchKeyword: '',
    selectedFilter: '',
    selectedRarity: '',
    collectionCount: 0,
    totalCount: 0,
    collectionRate: 0
  },

  onLoad() {
    this.setData({
      categories: app.globalData.categories
    })
    this.loadAllLingfu()
  },

  onShow() {
    // 更新收藏状态
    this.updateCollectionStatus()
    this.updateStats()
  },

  // 加载所有灵符
  loadAllLingfu() {
    wx.showLoading({
      title: '加载中...'
    })

    try {
      const allLingfu = drawUtil.getAllLingfu()
      
      // 处理数据
      const processedLingfu = allLingfu.map(lingfu => {
        // 稀有度文本
        const rarityMap = {
          'common': '普通',
          'rare': '稀有',
          'legendary': '传说'
        }
        
        // 分类名称
        const categoryNames = lingfu.category.map(catId => {
          const category = this.data.categories.find(cat => cat.id === catId)
          return category ? category.name : catId
        })

        // 简短描述
        const shortDescription = lingfu.description.length > 30 
          ? lingfu.description.substring(0, 30) + '...'
          : lingfu.description

        return {
          ...lingfu,
          rarityText: rarityMap[lingfu.rarity] || '未知',
          categoryNames,
          shortDescription,
          isCollected: false
        }
      })

      this.setData({
        allLingfu: processedLingfu,
        filteredLingfu: processedLingfu,
        totalCount: processedLingfu.length
      })

      this.updateCollectionStatus()
      this.updateStats()
    } catch (error) {
      console.error('加载灵符失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
    }
  },

  // 更新收藏状态
  updateCollectionStatus() {
    const collections = wx.getStorageSync('collections') || []
    const collectionIds = collections.map(item => item.id)
    
    const updatedLingfu = this.data.allLingfu.map(lingfu => ({
      ...lingfu,
      isCollected: collectionIds.includes(lingfu.id)
    }))

    this.setData({
      allLingfu: updatedLingfu
    })

    // 重新应用筛选
    this.applyFilters()
  },

  // 更新统计信息
  updateStats() {
    const collections = wx.getStorageSync('collections') || []
    const collectionCount = collections.length
    const totalCount = this.data.totalCount
    const collectionRate = totalCount > 0 ? Math.round((collectionCount / totalCount) * 100) : 0

    this.setData({
      collectionCount,
      collectionRate
    })
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    })
  },

  // 执行搜索
  onSearch() {
    this.applyFilters()
  },

  // 选择分类筛选
  selectFilter(e) {
    const filter = e.currentTarget.dataset.filter
    this.setData({
      selectedFilter: filter
    })
    this.applyFilters()
  },

  // 选择稀有度筛选
  selectRarity(e) {
    const rarity = e.currentTarget.dataset.rarity
    this.setData({
      selectedRarity: rarity
    })
    this.applyFilters()
  },

  // 应用筛选条件
  applyFilters() {
    let filtered = [...this.data.allLingfu]

    // 搜索关键词筛选
    if (this.data.searchKeyword) {
      const keyword = this.data.searchKeyword.toLowerCase()
      filtered = filtered.filter(lingfu => 
        lingfu.name.toLowerCase().includes(keyword) ||
        lingfu.description.toLowerCase().includes(keyword) ||
        lingfu.blessing.toLowerCase().includes(keyword)
      )
    }

    // 分类筛选
    if (this.data.selectedFilter) {
      filtered = filtered.filter(lingfu => 
        lingfu.category.includes(this.data.selectedFilter)
      )
    }

    // 稀有度筛选
    if (this.data.selectedRarity) {
      filtered = filtered.filter(lingfu => 
        lingfu.rarity === this.data.selectedRarity
      )
    }

    this.setData({
      filteredLingfu: filtered
    })
  },

  // 清除筛选
  clearFilters() {
    this.setData({
      searchKeyword: '',
      selectedFilter: '',
      selectedRarity: '',
      filteredLingfu: this.data.allLingfu
    })
  },

  // 切换收藏状态
  toggleCollection(e) {
    const { id } = e.currentTarget.dataset
    const lingfu = this.data.allLingfu.find(item => item.id === id)
    
    if (!lingfu) return

    let collections = wx.getStorageSync('collections') || []
    const isCollected = collections.some(item => item.id === id)

    if (isCollected) {
      // 取消收藏
      collections = collections.filter(item => item.id !== id)
      wx.showToast({
        title: '已取消收藏',
        icon: 'success',
        duration: 1000
      })
    } else {
      // 添加收藏
      collections.unshift(lingfu)
      wx.showToast({
        title: '收藏成功',
        icon: 'success',
        duration: 1000
      })
    }

    wx.setStorageSync('collections', collections)
    app.globalData.collections = collections

    // 更新收藏状态
    this.updateCollectionStatus()
    this.updateStats()
  },

  // 查看详情
  viewDetail(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/detail/detail?id=${id}`
    })
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadAllLingfu()
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  },

  // 页面分享
  onShareAppMessage() {
    return {
      title: '灵符宝库 - 发现更多神奇灵符',
      path: '/pages/library/library'
    }
  }
})
