<!--pages/library/library.wxml-->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-section">
    <view class="search-bar">
      <input 
        class="search-input" 
        placeholder="搜索灵符名称或功效"
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        bindconfirm="onSearch"
      />
      <text class="search-icon" bindtap="onSearch">🔍</text>
    </view>
  </view>

  <!-- 分类筛选 -->
  <view class="filter-section">
    <scroll-view class="filter-scroll" scroll-x="true">
      <view class="filter-list">
        <view 
          class="filter-item {{selectedFilter === '' ? 'active' : ''}}"
          bindtap="selectFilter"
          data-filter=""
        >
          全部
        </view>
        <view 
          wx:for="{{categories}}" 
          wx:key="id"
          class="filter-item {{selectedFilter === item.id ? 'active' : ''}}"
          bindtap="selectFilter"
          data-filter="{{item.id}}"
        >
          <text class="filter-icon">{{item.icon}}</text>
          <text class="filter-name">{{item.name}}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 稀有度筛选 -->
  <view class="rarity-section">
    <view class="rarity-filters">
      <view 
        class="rarity-item {{selectedRarity === '' ? 'active' : ''}}"
        bindtap="selectRarity"
        data-rarity=""
      >
        全部
      </view>
      <view 
        class="rarity-item rarity-common {{selectedRarity === 'common' ? 'active' : ''}}"
        bindtap="selectRarity"
        data-rarity="common"
      >
        普通
      </view>
      <view 
        class="rarity-item rarity-rare {{selectedRarity === 'rare' ? 'active' : ''}}"
        bindtap="selectRarity"
        data-rarity="rare"
      >
        稀有
      </view>
      <view 
        class="rarity-item rarity-legendary {{selectedRarity === 'legendary' ? 'active' : ''}}"
        bindtap="selectRarity"
        data-rarity="legendary"
      >
        传说
      </view>
    </view>
  </view>

  <!-- 灵符列表 -->
  <view class="lingfu-section">
    <view class="section-header">
      <text class="section-title">灵符宝库</text>
      <text class="section-count">共 {{filteredLingfu.length}} 张</text>
    </view>
    
    <view class="lingfu-grid" wx:if="{{filteredLingfu.length > 0}}">
      <view 
        wx:for="{{filteredLingfu}}" 
        wx:key="id"
        class="lingfu-card"
        bindtap="viewDetail"
        data-id="{{item.id}}"
      >
        <view class="card-image-wrapper">
          <image class="card-image" src="{{item.image}}" mode="aspectFit"></image>
          <view class="rarity-badge rarity-{{item.rarity}}">
            <text class="rarity-text">{{item.rarityText}}</text>
          </view>
        </view>
        
        <view class="card-content">
          <text class="card-name">{{item.name}}</text>
          <view class="card-categories">
            <text 
              wx:for="{{item.categoryNames}}" 
              wx:for-item="catName"
              wx:key="*this"
              class="card-category"
            >
              {{catName}}
            </text>
          </view>
          <text class="card-description">{{item.shortDescription}}</text>
          
          <view class="card-footer">
            <view class="power-indicator">
              <text class="power-label">灵力</text>
              <view class="power-bar">
                <view class="power-fill" style="width: {{item.power}}%"></view>
              </view>
              <text class="power-value">{{item.power}}</text>
            </view>
            <view class="card-actions">
              <text 
                class="action-btn collect-btn {{item.isCollected ? 'collected' : ''}}"
                bindtap="toggleCollection"
                data-id="{{item.id}}"
                catchtap=""
              >
                {{item.isCollected ? '❤️' : '🤍'}}
              </text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:else>
      <text class="empty-icon">📜</text>
      <text class="empty-title">暂无符合条件的灵符</text>
      <text class="empty-desc">试试调整筛选条件或搜索关键词</text>
      <button class="btn-primary" bindtap="clearFilters">清除筛选</button>
    </view>
  </view>

  <!-- 统计信息 -->
  <view class="stats-section">
    <text class="stats-title">收藏统计</text>
    <view class="stats-grid">
      <view class="stats-item">
        <text class="stats-number">{{collectionCount}}</text>
        <text class="stats-label">已收藏</text>
      </view>
      <view class="stats-item">
        <text class="stats-number">{{totalCount}}</text>
        <text class="stats-label">总数量</text>
      </view>
      <view class="stats-item">
        <text class="stats-number">{{collectionRate}}%</text>
        <text class="stats-label">收藏率</text>
      </view>
    </view>
  </view>
</view>
