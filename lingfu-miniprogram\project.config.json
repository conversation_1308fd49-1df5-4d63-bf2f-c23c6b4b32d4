{"description": "灵符驾到微信小程序项目配置", "packOptions": {"ignore": [{"type": "file", "value": ".eslintrc.js"}, {"type": "file", "value": ".giti<PERSON>re"}, {"type": "file", "value": "README.md"}, {"type": "folder", "value": "docs"}]}, "setting": {"urlCheck": true, "es6": true, "enhance": true, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": false, "coverView": true, "nodeModules": false, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "enableEngineNative": false, "useIsolateContext": false, "userConfirmedBundleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "disableUseStrict": false, "minifyWXML": true, "showES6CompileOption": false, "useCompilerPlugins": false}, "compileType": "miniprogram", "libVersion": "2.19.4", "appid": "wxYourAppIdHere", "projectname": "lingfu-miniprogram", "debugOptions": {"hidedInDevtools": []}, "scripts": {}, "staticServerOptions": {"baseURL": "", "servePath": ""}, "isGameTourist": false, "condition": {"search": {"list": []}, "conversation": {"list": []}, "game": {"list": []}, "plugin": {"list": []}, "gamePlugin": {"list": []}, "miniprogram": {"list": [{"name": "首页", "pathName": "pages/index/index", "query": "", "scene": null}, {"name": "灵符详情", "pathName": "pages/detail/detail", "query": "id=lingfu_001", "scene": null}, {"name": "灵符库", "pathName": "pages/library/library", "query": "", "scene": null}, {"name": "我的收藏", "pathName": "pages/collection/collection", "query": "", "scene": null}]}}}