// utils/card-composer.js - 灵符卡牌动态合成系统

// 灵符符号数据（使用字体和简单图形）
const lingfuSymbols = {
  // 趋吉避凶类
  'taishang': {
    text: '急急如律令',
    symbol: '⚡',
    pattern: 'traditional',
    color: '#DC143C'
  },
  'wulei': {
    text: '五雷正法',
    symbol: '⚡',
    pattern: 'thunder',
    color: '#4169E1'
  },
  'pingan': {
    text: '平安如意',
    symbol: '🕊',
    pattern: 'peace',
    color: '#228B22'
  },
  'bixie': {
    text: '辟邪镇宅',
    symbol: '🛡',
    pattern: 'shield',
    color: '#B22222'
  },

  // 升学就业类
  'wenchang': {
    text: '文昌帝君',
    symbol: '📚',
    pattern: 'study',
    color: '#4682B4'
  },
  'zhihui': {
    text: '智慧开启',
    symbol: '💡',
    pattern: 'wisdom',
    color: '#FFD700'
  },
  'kaoshi': {
    text: '考试顺利',
    symbol: '✏️',
    pattern: 'exam',
    color: '#32CD32'
  },

  // 生意创意类
  'zhaocai': {
    text: '招财进宝',
    symbol: '💰',
    pattern: 'wealth',
    color: '#DAA520'
  },
  'chuangyi': {
    text: '创意灵感',
    symbol: '💡',
    pattern: 'creative',
    color: '#FF6347'
  },
  'shiye': {
    text: '事业腾飞',
    symbol: '🚀',
    pattern: 'career',
    color: '#1E90FF'
  },
  'hezuo': {
    text: '合作共赢',
    symbol: '🤝',
    pattern: 'cooperation',
    color: '#20B2AA'
  },

  // 身体康健类
  'yaoshi': {
    text: '药师佛',
    symbol: '🌿',
    pattern: 'healing',
    color: '#9ACD32'
  },
  'changshou': {
    text: '长寿健康',
    symbol: '🍃',
    pattern: 'longevity',
    color: '#8FBC8F'
  },
  'kangfu': {
    text: '康复平安',
    symbol: '❤️',
    pattern: 'recovery',
    color: '#FF69B4'
  },

  // 感情姻缘类
  'yuelao': {
    text: '月老红线',
    symbol: '💕',
    pattern: 'love',
    color: '#FF1493'
  },
  'taohua': {
    text: '桃花运',
    symbol: '🌸',
    pattern: 'romance',
    color: '#FFB6C1'
  },
  'fuqi': {
    text: '夫妻和合',
    symbol: '👫',
    pattern: 'harmony',
    color: '#DDA0DD'
  },

  // 家庭和睦类
  'jiahe': {
    text: '家和万事兴',
    symbol: '🏡',
    pattern: 'family',
    color: '#CD853F'
  },
  'zisun': {
    text: '子孙满堂',
    symbol: '👶',
    pattern: 'offspring',
    color: '#F0E68C'
  },
  'xiaoshun': {
    text: '孝顺和睦',
    symbol: '👨‍👩‍👧‍👦',
    pattern: 'filial',
    color: '#D2B48C'
  }
}

// 底板样式配置
const cardTemplates = {
  common: {
    bgColor: '#F5F5DC',
    borderColor: '#8B4513',
    borderWidth: 4,
    glowColor: 'rgba(139, 69, 19, 0.3)'
  },
  rare: {
    bgColor: '#E6E6FA',
    borderColor: '#4169E1',
    borderWidth: 6,
    glowColor: 'rgba(65, 105, 225, 0.5)'
  },
  legendary: {
    bgColor: '#FFF8DC',
    borderColor: '#FFD700',
    borderWidth: 8,
    glowColor: 'rgba(255, 215, 0, 0.7)'
  }
}

// 绘制图案的函数
const patternDrawers = {
  traditional: (ctx, x, y, size, color) => {
    ctx.strokeStyle = color
    ctx.lineWidth = 3
    // 绘制传统边框
    ctx.strokeRect(x - size/2, y - size/2, size, size)
    // 绘制内部十字
    ctx.beginPath()
    ctx.moveTo(x, y - size/3)
    ctx.lineTo(x, y + size/3)
    ctx.moveTo(x - size/3, y)
    ctx.lineTo(x + size/3, y)
    ctx.stroke()
  },

  thunder: (ctx, x, y, size, color) => {
    ctx.fillStyle = color
    ctx.beginPath()
    // 绘制闪电形状
    ctx.moveTo(x - size/4, y - size/2)
    ctx.lineTo(x + size/6, y - size/6)
    ctx.lineTo(x - size/6, y)
    ctx.lineTo(x + size/4, y + size/2)
    ctx.lineTo(x - size/6, y + size/6)
    ctx.lineTo(x + size/6, y)
    ctx.closePath()
    ctx.fill()
  },

  peace: (ctx, x, y, size, color) => {
    ctx.strokeStyle = color
    ctx.lineWidth = 2
    // 绘制和平符号
    ctx.beginPath()
    ctx.arc(x, y, size/3, 0, Math.PI * 2)
    ctx.stroke()
    // 内部线条
    ctx.beginPath()
    ctx.moveTo(x, y - size/3)
    ctx.lineTo(x, y + size/3)
    ctx.moveTo(x - size/4, y + size/6)
    ctx.lineTo(x, y)
    ctx.lineTo(x + size/4, y + size/6)
    ctx.stroke()
  },

  // 其他图案可以继续添加...
  default: (ctx, x, y, size, color) => {
    ctx.fillStyle = color
    ctx.beginPath()
    ctx.arc(x, y, size/4, 0, Math.PI * 2)
    ctx.fill()
  }
}

/**
 * 动态合成灵符卡牌
 */
function composeCard(canvasId, lingfuData, options = {}) {
  const canvas = wx.createCanvasContext(canvasId)
  const width = options.width || 300
  const height = options.height || 400
  
  // 获取符号数据
  const symbolData = lingfuSymbols[lingfuData.id.split('_')[1]] || lingfuSymbols.default
  const template = cardTemplates[lingfuData.rarity] || cardTemplates.common
  
  // 清空画布
  canvas.clearRect(0, 0, width, height)
  
  // 绘制背景
  drawBackground(canvas, width, height, template)
  
  // 绘制边框和装饰
  drawBorder(canvas, width, height, template)
  
  // 绘制符咒图案
  drawPattern(canvas, width/2, height/3, symbolData)
  
  // 绘制文字
  drawText(canvas, width, height, lingfuData, symbolData)
  
  // 绘制稀有度效果
  if (lingfuData.rarity !== 'common') {
    drawRarityEffect(canvas, width, height, template)
  }
  
  canvas.draw()
}

/**
 * 绘制背景
 */
function drawBackground(ctx, width, height, template) {
  // 渐变背景
  const gradient = ctx.createLinearGradient(0, 0, 0, height)
  gradient.addColorStop(0, template.bgColor)
  gradient.addColorStop(1, adjustBrightness(template.bgColor, -0.1))
  
  ctx.fillStyle = gradient
  ctx.fillRect(0, 0, width, height)
  
  // 纹理效果（可选）
  ctx.globalAlpha = 0.1
  for (let i = 0; i < 20; i++) {
    ctx.fillStyle = '#000'
    ctx.fillRect(Math.random() * width, Math.random() * height, 1, 1)
  }
  ctx.globalAlpha = 1
}

/**
 * 绘制边框
 */
function drawBorder(ctx, width, height, template) {
  ctx.strokeStyle = template.borderColor
  ctx.lineWidth = template.borderWidth
  
  // 外边框
  ctx.strokeRect(10, 10, width - 20, height - 20)
  
  // 内边框
  ctx.lineWidth = 2
  ctx.strokeRect(20, 20, width - 40, height - 40)
  
  // 角落装饰
  drawCornerDecorations(ctx, width, height, template.borderColor)
}

/**
 * 绘制符咒图案
 */
function drawPattern(ctx, x, y, symbolData) {
  const patternSize = 80
  const drawer = patternDrawers[symbolData.pattern] || patternDrawers.default
  
  // 绘制图案
  drawer(ctx, x, y, patternSize, symbolData.color)
  
  // 绘制emoji符号
  ctx.font = '40px Arial'
  ctx.textAlign = 'center'
  ctx.fillStyle = symbolData.color
  ctx.fillText(symbolData.symbol, x, y + patternSize/2 + 50)
}

/**
 * 绘制文字
 */
function drawText(ctx, width, height, lingfuData, symbolData) {
  ctx.textAlign = 'center'
  
  // 符咒名称
  ctx.font = 'bold 24px serif'
  ctx.fillStyle = '#2F4F4F'
  ctx.fillText(lingfuData.name, width/2, 60)
  
  // 符咒文字
  ctx.font = '18px serif'
  ctx.fillStyle = symbolData.color
  ctx.fillText(symbolData.text, width/2, height/2 + 80)
  
  // 祝福语（简化版）
  ctx.font = '14px serif'
  ctx.fillStyle = '#696969'
  const blessing = lingfuData.blessing.substring(0, 12) + '...'
  ctx.fillText(blessing, width/2, height - 60)
  
  // 稀有度标识
  ctx.font = '12px Arial'
  ctx.fillStyle = cardTemplates[lingfuData.rarity].borderColor
  ctx.fillText(lingfuData.rarity.toUpperCase(), width/2, height - 30)
}

/**
 * 绘制稀有度效果
 */
function drawRarityEffect(ctx, width, height, template) {
  // 发光效果
  ctx.shadowColor = template.glowColor
  ctx.shadowBlur = 20
  ctx.strokeStyle = template.borderColor
  ctx.lineWidth = 2
  ctx.strokeRect(5, 5, width - 10, height - 10)
  
  // 重置阴影
  ctx.shadowColor = 'transparent'
  ctx.shadowBlur = 0
}

/**
 * 绘制角落装饰
 */
function drawCornerDecorations(ctx, width, height, color) {
  ctx.strokeStyle = color
  ctx.lineWidth = 2
  
  const size = 15
  // 左上角
  ctx.beginPath()
  ctx.moveTo(25, 35)
  ctx.lineTo(25, 25)
  ctx.lineTo(35, 25)
  ctx.stroke()
  
  // 右上角
  ctx.beginPath()
  ctx.moveTo(width - 35, 25)
  ctx.lineTo(width - 25, 25)
  ctx.lineTo(width - 25, 35)
  ctx.stroke()
  
  // 左下角
  ctx.beginPath()
  ctx.moveTo(25, height - 35)
  ctx.lineTo(25, height - 25)
  ctx.lineTo(35, height - 25)
  ctx.stroke()
  
  // 右下角
  ctx.beginPath()
  ctx.moveTo(width - 35, height - 25)
  ctx.lineTo(width - 25, height - 25)
  ctx.lineTo(width - 25, height - 35)
  ctx.stroke()
}

/**
 * 调整颜色亮度
 */
function adjustBrightness(color, amount) {
  // 简化的颜色调整函数
  return color // 实际项目中可以实现更复杂的颜色调整
}

/**
 * 缓存合成的卡牌
 */
function cacheCard(lingfuId, canvasData) {
  try {
    const cacheKey = `card_${lingfuId}`
    wx.setStorageSync(cacheKey, canvasData)
  } catch (error) {
    console.warn('缓存卡牌失败:', error)
  }
}

/**
 * 获取缓存的卡牌
 */
function getCachedCard(lingfuId) {
  try {
    const cacheKey = `card_${lingfuId}`
    return wx.getStorageSync(cacheKey)
  } catch (error) {
    console.warn('获取缓存卡牌失败:', error)
    return null
  }
}

module.exports = {
  composeCard,
  lingfuSymbols,
  cardTemplates,
  cacheCard,
  getCachedCard
}
