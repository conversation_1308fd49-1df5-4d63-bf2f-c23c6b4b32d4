// utils/draw.js - 抽签逻辑工具

// 测试版灵符数据库 - 20张灵符，使用Canvas绘制或emoji代替图片
const lingfuDatabase = [
  // 趋吉避凶类 (4张)
  {
    id: 'lingfu_001',
    name: '太上老君急急如律令',
    category: ['luck'],
    image: 'dynamic', // 动态合成
    description: '此符乃太上老君亲传，能驱邪避凶，护佑平安。',
    usage: '随身携带或贴于门上，诚心念诵三遍',
    blessing: '愿此符护佑您平安吉祥，邪不侵身',
    rarity: 'legendary',
    power: 95
  },
  {
    id: 'lingfu_002',
    name: '五雷护身符',
    category: ['luck'],
    image: '/images/lingfu/wulei.jpg',
    description: '五雷正法所制，雷霆万钧，邪魔退散。',
    usage: '贴身佩戴，遇险时默念雷神护佑',
    blessing: '雷神护佑，邪魔退散，正气长存',
    rarity: 'rare',
    power: 85
  },
  {
    id: 'lingfu_003',
    name: '平安如意符',
    category: ['luck'],
    image: '/images/lingfu/pingan.jpg',
    description: '保佑出入平安，事事如意。适合日常佩戴。',
    usage: '随身携带，每日祈愿平安',
    blessing: '出入平安，事事如意，福运绵长',
    rarity: 'common',
    power: 70
  },
  {
    id: 'lingfu_004',
    name: '辟邪镇宅符',
    category: ['luck'],
    image: '/images/lingfu/bixie.jpg',
    description: '镇宅辟邪，保家宅平安，驱除不祥之气。',
    usage: '贴于门上或客厅显眼处',
    blessing: '宅院安宁，邪祟远离，家运昌盛',
    rarity: 'rare',
    power: 88
  },

  // 升学就业类 (3张)
  {
    id: 'lingfu_005',
    name: '文昌帝君符',
    category: ['study'],
    image: '/images/lingfu/wenchang.jpg',
    description: '文昌帝君加持，开启智慧，助力学业。',
    usage: '考试前三日开始佩戴，考试时放于文具盒中',
    blessing: '文昌护佑，智慧开启，金榜题名',
    rarity: 'legendary',
    power: 90
  },
  {
    id: 'lingfu_006',
    name: '智慧开启符',
    category: ['study'],
    image: '/images/lingfu/zhihui.jpg',
    description: '开启心智，增强记忆，提升学习效率。',
    usage: '贴于书桌前，每日学习前默念三遍',
    blessing: '智慧如海，学业有成，前程似锦',
    rarity: 'rare',
    power: 80
  },
  {
    id: 'lingfu_007',
    name: '考试顺利符',
    category: ['study'],
    image: '/images/lingfu/kaoshi.jpg',
    description: '专为考试而制，助力发挥最佳状态。',
    usage: '考试当日随身携带',
    blessing: '考试顺利，超常发挥，心想事成',
    rarity: 'common',
    power: 75
  },

  // 生意创意类 (4张)
  {
    id: 'lingfu_008',
    name: '招财进宝符',
    category: ['business'],
    image: '/images/lingfu/zhaocai.jpg',
    description: '招财进宝，生意兴隆。适合商人、创业者。',
    usage: '放于收银台或办公桌，每月初一更换',
    blessing: '财源广进，生意兴隆，富贵满堂',
    rarity: 'rare',
    power: 85
  },
  {
    id: 'lingfu_009',
    name: '创意灵感符',
    category: ['business'],
    image: '/images/lingfu/chuangyi.jpg',
    description: '激发创意灵感，助力创新思维。',
    usage: '工作时佩戴，创作前祈愿灵感降临',
    blessing: '灵感如泉涌，创意无穷尽，成功在望',
    rarity: 'common',
    power: 75
  },
  {
    id: 'lingfu_010',
    name: '事业腾飞符',
    category: ['business'],
    image: '/images/lingfu/shiye.jpg',
    description: '助力事业发展，职场顺遂，步步高升。',
    usage: '放于办公桌或随身携带',
    blessing: '事业腾飞，步步高升，前程无量',
    rarity: 'rare',
    power: 87
  },
  {
    id: 'lingfu_011',
    name: '合作共赢符',
    category: ['business'],
    image: '/images/lingfu/hezuo.jpg',
    description: '促进合作顺利，化解商业纠纷，共创双赢。',
    usage: '商务谈判前祈愿，签约时随身携带',
    blessing: '合作愉快，互利共赢，财源滚滚',
    rarity: 'common',
    power: 78
  },

  // 身体康健类 (3张)
  {
    id: 'lingfu_012',
    name: '药师佛护身符',
    category: ['health'],
    image: '/images/lingfu/yaoshi.jpg',
    description: '药师佛加持，消除病苦，恢复健康。',
    usage: '贴身佩戴，每日念诵药师佛号',
    blessing: '药师护佑，病苦消除，身体康健',
    rarity: 'legendary',
    power: 92
  },
  {
    id: 'lingfu_013',
    name: '长寿健康符',
    category: ['health'],
    image: '/images/lingfu/changshou.jpg',
    description: '延年益寿，强身健体。适合老人和注重养生的人士。',
    usage: '随身携带，配合适当运动和饮食',
    blessing: '长寿健康，精神矍铄，福寿绵长',
    rarity: 'rare',
    power: 82
  },
  {
    id: 'lingfu_014',
    name: '康复平安符',
    category: ['health'],
    image: '/images/lingfu/kangfu.jpg',
    description: '加速康复，驱除病痛，恢复元气。',
    usage: '病中佩戴，康复期间随身携带',
    blessing: '早日康复，身强体健，平安无恙',
    rarity: 'common',
    power: 76
  },

  // 感情姻缘类 (3张)
  {
    id: 'lingfu_015',
    name: '月老红线符',
    category: ['love'],
    image: '/images/lingfu/yuelao.jpg',
    description: '月老牵线，姻缘美满。单身者求姻缘，已婚者求和睦。',
    usage: '放于枕下或随身携带，诚心祈愿',
    blessing: '月老牵线，姻缘美满，白头偕老',
    rarity: 'rare',
    power: 88
  },
  {
    id: 'lingfu_016',
    name: '桃花运符',
    category: ['love'],
    image: '/images/lingfu/taohua.jpg',
    description: '增强个人魅力，招来桃花运，助力脱单。',
    usage: '随身携带，参加社交活动时佩戴',
    blessing: '桃花朵朵开，良缘自然来，爱情甜如蜜',
    rarity: 'common',
    power: 73
  },
  {
    id: 'lingfu_017',
    name: '夫妻和合符',
    category: ['love'],
    image: '/images/lingfu/fuqi.jpg',
    description: '促进夫妻感情和睦，化解矛盾，增进理解。',
    usage: '夫妻共同祈愿，放于卧室',
    blessing: '夫妻恩爱，白头偕老，家庭美满',
    rarity: 'rare',
    power: 85
  },

  // 家庭和睦类 (3张)
  {
    id: 'lingfu_018',
    name: '家和万事兴符',
    category: ['family'],
    image: '/images/lingfu/jiahe.jpg',
    description: '促进家庭和睦，化解家庭矛盾，增进亲情。',
    usage: '贴于客厅显眼处，全家人共同祈愿',
    blessing: '家和万事兴，亲情深如海，幸福满家园',
    rarity: 'common',
    power: 78
  },
  {
    id: 'lingfu_019',
    name: '子孙满堂符',
    category: ['family'],
    image: '/images/lingfu/zisun.jpg',
    description: '祈求子孙兴旺，家族繁荣，传承有序。',
    usage: '新婚夫妇或备孕期间佩戴',
    blessing: '子孙满堂，家族兴旺，传承千秋',
    rarity: 'rare',
    power: 83
  },
  {
    id: 'lingfu_020',
    name: '孝顺和睦符',
    category: ['family'],
    image: '/images/lingfu/xiaoshun.jpg',
    description: '促进家庭成员间的理解与孝顺，增进亲情。',
    usage: '家庭聚会时共同祈愿',
    blessing: '孝顺父母，兄弟和睦，其乐融融',
    rarity: 'common',
    power: 74
  }
]

/**
 * 根据分类抽取随机灵符
 * @param {string} category 分类ID，为空则从所有灵符中抽取
 * @returns {Object} 抽取到的灵符对象
 */
function drawRandomLingfu(category = '') {
  return new Promise((resolve, reject) => {
    try {
      // 模拟网络延迟
      setTimeout(() => {
        let availableLingfu = lingfuDatabase
        
        // 根据分类筛选
        if (category) {
          availableLingfu = lingfuDatabase.filter(lingfu => 
            lingfu.category.includes(category)
          )
        }
        
        if (availableLingfu.length === 0) {
          reject(new Error('没有找到符合条件的灵符'))
          return
        }
        
        // 根据稀有度设置权重
        const weightedLingfu = []
        availableLingfu.forEach(lingfu => {
          let weight = 1
          switch (lingfu.rarity) {
            case 'common':
              weight = 50
              break
            case 'rare':
              weight = 30
              break
            case 'legendary':
              weight = 10
              break
          }
          
          for (let i = 0; i < weight; i++) {
            weightedLingfu.push(lingfu)
          }
        })
        
        // 随机抽取
        const randomIndex = Math.floor(Math.random() * weightedLingfu.length)
        const selectedLingfu = weightedLingfu[randomIndex]
        
        // 添加抽取时间戳
        const result = {
          ...selectedLingfu,
          drawTime: new Date().toISOString(),
          drawTimestamp: Date.now()
        }
        
        resolve(result)
      }, 1500) // 1.5秒延迟增加仪式感
    } catch (error) {
      reject(error)
    }
  })
}

/**
 * 获取指定ID的灵符详情
 * @param {string} id 灵符ID
 * @returns {Object} 灵符对象
 */
function getLingfuById(id) {
  return lingfuDatabase.find(lingfu => lingfu.id === id)
}

/**
 * 根据分类获取灵符列表
 * @param {string} category 分类ID
 * @returns {Array} 灵符数组
 */
function getLingfuByCategory(category) {
  if (!category) {
    return lingfuDatabase
  }
  return lingfuDatabase.filter(lingfu => lingfu.category.includes(category))
}

/**
 * 获取所有灵符
 * @returns {Array} 所有灵符数组
 */
function getAllLingfu() {
  return lingfuDatabase
}

/**
 * 根据稀有度获取灵符
 * @param {string} rarity 稀有度 common/rare/legendary
 * @returns {Array} 灵符数组
 */
function getLingfuByRarity(rarity) {
  return lingfuDatabase.filter(lingfu => lingfu.rarity === rarity)
}

/**
 * 搜索灵符
 * @param {string} keyword 关键词
 * @returns {Array} 匹配的灵符数组
 */
function searchLingfu(keyword) {
  if (!keyword) {
    return lingfuDatabase
  }
  
  const lowerKeyword = keyword.toLowerCase()
  return lingfuDatabase.filter(lingfu => 
    lingfu.name.toLowerCase().includes(lowerKeyword) ||
    lingfu.description.toLowerCase().includes(lowerKeyword) ||
    lingfu.blessing.toLowerCase().includes(lowerKeyword)
  )
}

module.exports = {
  drawRandomLingfu,
  getLingfuById,
  getLingfuByCategory,
  getAllLingfu,
  getLingfuByRarity,
  searchLingfu,
  lingfuDatabase
}
