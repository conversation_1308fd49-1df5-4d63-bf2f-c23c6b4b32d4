// utils/size-monitor.js - 包大小监控工具

/**
 * 获取文件大小（字节）
 */
function getFileSize(filePath) {
  try {
    const fs = wx.getFileSystemManager()
    const stats = fs.statSync(filePath)
    return stats.size || 0
  } catch (error) {
    console.warn(`无法获取文件大小: ${filePath}`, error)
    return 0
  }
}

/**
 * 格式化文件大小
 */
function formatSize(bytes) {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 检查图片文件大小
 */
function checkImageSizes() {
  const imageFiles = [
    // 灵符图片 (20张)
    '/images/lingfu/taishang.jpg',
    '/images/lingfu/wulei.jpg',
    '/images/lingfu/pingan.jpg',
    '/images/lingfu/bixie.jpg',
    '/images/lingfu/wenchang.jpg',
    '/images/lingfu/zhihui.jpg',
    '/images/lingfu/kaoshi.jpg',
    '/images/lingfu/zhaocai.jpg',
    '/images/lingfu/chuangyi.jpg',
    '/images/lingfu/shiye.jpg',
    '/images/lingfu/hezuo.jpg',
    '/images/lingfu/yaoshi.jpg',
    '/images/lingfu/changshou.jpg',
    '/images/lingfu/kangfu.jpg',
    '/images/lingfu/yuelao.jpg',
    '/images/lingfu/taohua.jpg',
    '/images/lingfu/fuqi.jpg',
    '/images/lingfu/jiahe.jpg',
    '/images/lingfu/zisun.jpg',
    '/images/lingfu/xiaoshun.jpg',
    
    // 图标文件
    '/images/icons/shake.png',
    '/images/icons/voice.png',
    '/images/icons/category.png',
    
    // 背景图片
    '/images/bg/main-bg.jpg',
    '/images/bg/result-bg.jpg'
  ]
  
  let totalImageSize = 0
  const oversizedFiles = []
  const missingFiles = []
  
  imageFiles.forEach(filePath => {
    const size = getFileSize(filePath)
    
    if (size === 0) {
      missingFiles.push(filePath)
    } else {
      totalImageSize += size
      
      // 检查灵符图片是否超过60KB
      if (filePath.includes('/lingfu/') && size > 60 * 1024) {
        oversizedFiles.push({
          path: filePath,
          size: size,
          formatted: formatSize(size)
        })
      }
    }
  })
  
  return {
    totalImageSize,
    oversizedFiles,
    missingFiles,
    imageCount: imageFiles.length - missingFiles.length
  }
}

/**
 * 获取包大小报告
 */
function getPackageSizeReport() {
  const maxSize = 2 * 1024 * 1024 // 2MB
  const imageCheck = checkImageSizes()
  
  // 估算代码大小（实际项目中可以更精确计算）
  const estimatedCodeSize = 300 * 1024 // 300KB
  const totalEstimatedSize = estimatedCodeSize + imageCheck.totalImageSize
  
  const report = {
    maxSize: maxSize,
    estimatedCodeSize: estimatedCodeSize,
    totalImageSize: imageCheck.totalImageSize,
    totalEstimatedSize: totalEstimatedSize,
    remainingSpace: maxSize - totalEstimatedSize,
    isOverLimit: totalEstimatedSize > maxSize,
    imageCount: imageCheck.imageCount,
    oversizedFiles: imageCheck.oversizedFiles,
    missingFiles: imageCheck.missingFiles,
    
    // 格式化的大小
    formatted: {
      maxSize: formatSize(maxSize),
      estimatedCodeSize: formatSize(estimatedCodeSize),
      totalImageSize: formatSize(imageCheck.totalImageSize),
      totalEstimatedSize: formatSize(totalEstimatedSize),
      remainingSpace: formatSize(maxSize - totalEstimatedSize)
    }
  }
  
  return report
}

/**
 * 显示包大小报告
 */
function showSizeReport() {
  const report = getPackageSizeReport()
  
  console.log('=== 📦 包大小监控报告 ===')
  console.log(`最大限制: ${report.formatted.maxSize}`)
  console.log(`代码大小: ${report.formatted.estimatedCodeSize} (估算)`)
  console.log(`图片大小: ${report.formatted.totalImageSize} (${report.imageCount}张)`)
  console.log(`总计大小: ${report.formatted.totalEstimatedSize}`)
  console.log(`剩余空间: ${report.formatted.remainingSpace}`)
  
  if (report.isOverLimit) {
    console.error('⚠️ 警告: 包大小超过2MB限制!')
  } else {
    console.log('✅ 包大小在限制范围内')
  }
  
  if (report.oversizedFiles.length > 0) {
    console.log('\n⚠️ 超大文件:')
    report.oversizedFiles.forEach(file => {
      console.log(`  ${file.path}: ${file.formatted}`)
    })
  }
  
  if (report.missingFiles.length > 0) {
    console.log('\n📁 缺失文件:')
    report.missingFiles.forEach(file => {
      console.log(`  ${file}`)
    })
  }
  
  return report
}

/**
 * 获取图片压缩建议
 */
function getCompressionSuggestions(report) {
  const suggestions = []
  
  if (report.isOverLimit) {
    const overSize = report.totalEstimatedSize - report.maxSize
    suggestions.push(`需要减少 ${formatSize(overSize)} 的文件大小`)
  }
  
  if (report.oversizedFiles.length > 0) {
    suggestions.push('以下文件需要压缩:')
    report.oversizedFiles.forEach(file => {
      const targetSize = 60 * 1024 // 60KB
      const reduction = file.size - targetSize
      suggestions.push(`  ${file.path}: 减少 ${formatSize(reduction)}`)
    })
  }
  
  if (report.totalImageSize > 1200 * 1024) { // 1.2MB
    suggestions.push('图片总大小建议控制在1.2MB以内')
  }
  
  return suggestions
}

/**
 * 检查单个图片文件
 */
function checkSingleImage(imagePath) {
  const size = getFileSize(imagePath)
  const maxSize = imagePath.includes('/lingfu/') ? 60 * 1024 : 100 * 1024
  
  return {
    path: imagePath,
    size: size,
    maxSize: maxSize,
    formatted: formatSize(size),
    maxFormatted: formatSize(maxSize),
    isOverLimit: size > maxSize,
    exists: size > 0
  }
}

module.exports = {
  getFileSize,
  formatSize,
  checkImageSizes,
  getPackageSizeReport,
  showSizeReport,
  getCompressionSuggestions,
  checkSingleImage
}
