// utils/voice.js - 声控检测工具

class VoiceDetector {
  constructor() {
    this.recorderManager = null
    this.isListening = false
    this.volumeHistory = []
    this.maxHistoryLength = 10
    this.blowThreshold = 0.3 // 吹气音量阈值
    this.onBlowDetected = null
    this.onVolumeChange = null
  }

  // 初始化录音管理器
  init() {
    this.recorderManager = wx.getRecorderManager()
    
    // 录音开始事件
    this.recorderManager.onStart(() => {
      console.log('开始录音检测')
      this.isListening = true
      this.volumeHistory = []
    })

    // 录音停止事件
    this.recorderManager.onStop(() => {
      console.log('停止录音检测')
      this.isListening = false
    })

    // 录音错误事件
    this.recorderManager.onError((error) => {
      console.error('录音错误:', error)
      this.isListening = false
      wx.showToast({
        title: '录音权限未开启',
        icon: 'none'
      })
    })

    // 实时音量监听
    this.recorderManager.onFrameRecorded((res) => {
      if (!this.isListening) return
      
      // 计算音量
      const volume = this.calculateVolume(res.frameBuffer)
      this.volumeHistory.push(volume)
      
      // 保持历史记录长度
      if (this.volumeHistory.length > this.maxHistoryLength) {
        this.volumeHistory.shift()
      }

      // 触发音量变化回调
      if (this.onVolumeChange) {
        this.onVolumeChange(volume)
      }

      // 检测吹气模式
      this.detectBlow()
    })
  }

  // 计算音量
  calculateVolume(buffer) {
    if (!buffer || buffer.byteLength === 0) return 0
    
    const dataView = new DataView(buffer)
    let sum = 0
    let count = 0
    
    // 计算音频数据的RMS值
    for (let i = 0; i < dataView.byteLength; i += 2) {
      const sample = dataView.getInt16(i, true) / 32768.0
      sum += sample * sample
      count++
    }
    
    const rms = Math.sqrt(sum / count)
    return Math.min(rms * 10, 1) // 归一化到0-1范围
  }

  // 检测吹气模式
  detectBlow() {
    if (this.volumeHistory.length < 5) return

    const recent = this.volumeHistory.slice(-5)
    const current = recent[recent.length - 1]
    const previous = recent[recent.length - 2]
    const beforePrevious = recent[recent.length - 3]

    // 检测音量突然增大然后减小的模式（吹气特征）
    const isBlowPattern = 
      beforePrevious < this.blowThreshold * 0.5 && // 之前音量较低
      previous > this.blowThreshold && // 中间音量突然增大
      current < previous * 0.7 // 当前音量开始下降

    if (isBlowPattern && this.onBlowDetected) {
      console.log('检测到吹气！')
      this.onBlowDetected()
    }
  }

  // 开始监听
  startListening() {
    if (this.isListening) return

    // 请求录音权限
    wx.getSetting({
      success: (res) => {
        if (!res.authSetting['scope.record']) {
          wx.authorize({
            scope: 'scope.record',
            success: () => {
              this.startRecording()
            },
            fail: () => {
              wx.showModal({
                title: '需要录音权限',
                content: '声控功能需要录音权限才能使用，请在设置中开启',
                confirmText: '去设置',
                success: (res) => {
                  if (res.confirm) {
                    wx.openSetting()
                  }
                }
              })
            }
          })
        } else {
          this.startRecording()
        }
      }
    })
  }

  // 开始录音
  startRecording() {
    if (!this.recorderManager) {
      this.init()
    }

    this.recorderManager.start({
      duration: 60000, // 最长录音60秒
      sampleRate: 16000, // 采样率
      numberOfChannels: 1, // 单声道
      encodeBitRate: 48000, // 编码码率
      format: 'mp3', // 音频格式
      frameSize: 50 // 指定帧大小，单位 KB
    })
  }

  // 停止监听
  stopListening() {
    if (this.recorderManager && this.isListening) {
      this.recorderManager.stop()
    }
  }

  // 设置吹气检测回调
  setBlowCallback(callback) {
    this.onBlowDetected = callback
  }

  // 设置音量变化回调
  setVolumeCallback(callback) {
    this.onVolumeChange = callback
  }

  // 设置检测阈值
  setThreshold(threshold) {
    this.blowThreshold = Math.max(0.1, Math.min(1.0, threshold))
  }

  // 获取当前状态
  getStatus() {
    return {
      isListening: this.isListening,
      currentVolume: this.volumeHistory[this.volumeHistory.length - 1] || 0,
      threshold: this.blowThreshold
    }
  }
}

// 创建全局实例
const voiceDetector = new VoiceDetector()

// 导出工具函数
module.exports = {
  // 初始化声控检测
  initVoiceDetector() {
    voiceDetector.init()
    return voiceDetector
  },

  // 开始声控监听
  startVoiceListening(onBlowDetected, onVolumeChange) {
    if (onBlowDetected) {
      voiceDetector.setBlowCallback(onBlowDetected)
    }
    if (onVolumeChange) {
      voiceDetector.setVolumeCallback(onVolumeChange)
    }
    voiceDetector.startListening()
  },

  // 停止声控监听
  stopVoiceListening() {
    voiceDetector.stopListening()
  },

  // 设置检测灵敏度
  setVoiceSensitivity(level) {
    // level: 1-5, 1最敏感，5最不敏感
    const thresholds = [0.15, 0.25, 0.35, 0.45, 0.55]
    const threshold = thresholds[Math.max(0, Math.min(4, level - 1))]
    voiceDetector.setThreshold(threshold)
  },

  // 获取检测状态
  getVoiceStatus() {
    return voiceDetector.getStatus()
  },

  // 检测是否支持录音
  checkRecordPermission() {
    return new Promise((resolve) => {
      wx.getSetting({
        success: (res) => {
          resolve(!!res.authSetting['scope.record'])
        },
        fail: () => {
          resolve(false)
        }
      })
    })
  }
}
