<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>灵符图片压缩工具</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .upload-area {
            border: 3px dashed #ddd;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }
        .upload-area.dragover {
            border-color: #667eea;
            background: #f0f4ff;
        }
        input[type="file"] {
            display: none;
        }
        .upload-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        .upload-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .settings {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .setting-group {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
        }
        .setting-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        .setting-group input, .setting-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .preview-area {
            margin-top: 20px;
        }
        .image-preview {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        .preview-item {
            flex: 1;
            text-align: center;
        }
        .preview-item img {
            max-width: 100%;
            max-height: 200px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .size-info {
            margin-top: 10px;
            padding: 10px;
            background: #e9ecef;
            border-radius: 5px;
            font-size: 14px;
        }
        .download-btn {
            background: #28a745;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 10px;
        }
        .download-btn:hover {
            background: #218838;
        }
        .tips {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }
        .tips h3 {
            color: #856404;
            margin-top: 0;
        }
        .tips ul {
            color: #856404;
            margin: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 灵符图片压缩工具</h1>
        
        <div class="upload-area" id="uploadArea">
            <p>📁 拖拽图片到这里或点击选择文件</p>
            <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                选择图片文件
            </button>
            <input type="file" id="fileInput" accept="image/*" multiple>
        </div>

        <div class="settings">
            <div class="setting-group">
                <label for="targetWidth">目标宽度 (px)</label>
                <input type="number" id="targetWidth" value="400" min="100" max="800">
            </div>
            <div class="setting-group">
                <label for="targetHeight">目标高度 (px)</label>
                <input type="number" id="targetHeight" value="600" min="100" max="1000">
            </div>
            <div class="setting-group">
                <label for="quality">压缩质量 (%)</label>
                <input type="range" id="quality" min="10" max="100" value="70">
                <span id="qualityValue">70%</span>
            </div>
            <div class="setting-group">
                <label for="format">输出格式</label>
                <select id="format">
                    <option value="jpeg">JPEG (推荐)</option>
                    <option value="png">PNG</option>
                    <option value="webp">WebP</option>
                </select>
            </div>
        </div>

        <div class="preview-area" id="previewArea" style="display: none;">
            <h3>📊 压缩预览</h3>
            <div class="image-preview">
                <div class="preview-item">
                    <h4>原图</h4>
                    <img id="originalImage" alt="原图">
                    <div class="size-info" id="originalInfo"></div>
                </div>
                <div class="preview-item">
                    <h4>压缩后</h4>
                    <img id="compressedImage" alt="压缩后">
                    <div class="size-info" id="compressedInfo"></div>
                    <button class="download-btn" id="downloadBtn">下载压缩图片</button>
                </div>
            </div>
        </div>

        <div class="tips">
            <h3>💡 压缩建议</h3>
            <ul>
                <li><strong>灵符图片</strong>: 400x600px, 质量70%, ≤60KB</li>
                <li><strong>图标文件</strong>: 64x64px, 质量80%, ≤10KB</li>
                <li><strong>背景图片</strong>: 750x1334px, 质量60%, ≤100KB</li>
                <li><strong>总图片大小</strong>: 控制在1.2MB以内</li>
                <li><strong>格式选择</strong>: JPEG适合照片，PNG适合图标</li>
            </ul>
        </div>
    </div>

    <script>
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const previewArea = document.getElementById('previewArea');
        const originalImage = document.getElementById('originalImage');
        const compressedImage = document.getElementById('compressedImage');
        const originalInfo = document.getElementById('originalInfo');
        const compressedInfo = document.getElementById('compressedInfo');
        const downloadBtn = document.getElementById('downloadBtn');
        const qualitySlider = document.getElementById('quality');
        const qualityValue = document.getElementById('qualityValue');

        let currentFile = null;
        let compressedBlob = null;

        // 质量滑块更新
        qualitySlider.addEventListener('input', function() {
            qualityValue.textContent = this.value + '%';
            if (currentFile) {
                compressImage(currentFile);
            }
        });

        // 其他设置变化时重新压缩
        ['targetWidth', 'targetHeight', 'format'].forEach(id => {
            document.getElementById(id).addEventListener('change', function() {
                if (currentFile) {
                    compressImage(currentFile);
                }
            });
        });

        // 拖拽上传
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });

        // 文件选择
        fileInput.addEventListener('change', function(e) {
            if (e.target.files.length > 0) {
                handleFile(e.target.files[0]);
            }
        });

        function handleFile(file) {
            if (!file.type.startsWith('image/')) {
                alert('请选择图片文件！');
                return;
            }

            currentFile = file;
            
            // 显示原图
            const reader = new FileReader();
            reader.onload = function(e) {
                originalImage.src = e.target.result;
                originalInfo.innerHTML = `
                    <strong>文件名:</strong> ${file.name}<br>
                    <strong>大小:</strong> ${formatFileSize(file.size)}<br>
                    <strong>类型:</strong> ${file.type}
                `;
                
                // 压缩图片
                compressImage(file);
                previewArea.style.display = 'block';
            };
            reader.readAsDataURL(file);
        }

        function compressImage(file) {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();

            img.onload = function() {
                const targetWidth = parseInt(document.getElementById('targetWidth').value);
                const targetHeight = parseInt(document.getElementById('targetHeight').value);
                const quality = parseInt(document.getElementById('quality').value) / 100;
                const format = document.getElementById('format').value;

                canvas.width = targetWidth;
                canvas.height = targetHeight;

                // 绘制压缩后的图片
                ctx.drawImage(img, 0, 0, targetWidth, targetHeight);

                // 转换为blob
                canvas.toBlob(function(blob) {
                    compressedBlob = blob;
                    
                    // 显示压缩后的图片
                    const url = URL.createObjectURL(blob);
                    compressedImage.src = url;
                    
                    // 显示压缩信息
                    const compressionRatio = ((file.size - blob.size) / file.size * 100).toFixed(1);
                    compressedInfo.innerHTML = `
                        <strong>大小:</strong> ${formatFileSize(blob.size)}<br>
                        <strong>尺寸:</strong> ${targetWidth}x${targetHeight}px<br>
                        <strong>压缩率:</strong> ${compressionRatio}%<br>
                        <strong>状态:</strong> ${blob.size <= 60 * 1024 ? '✅ 符合要求' : '⚠️ 仍需压缩'}
                    `;
                    
                }, `image/${format}`, quality);
            };

            img.src = URL.createObjectURL(file);
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 下载压缩后的图片
        downloadBtn.addEventListener('click', function() {
            if (compressedBlob) {
                const url = URL.createObjectURL(compressedBlob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `compressed_${currentFile.name.split('.')[0]}.${document.getElementById('format').value}`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }
        });
    </script>
</body>
</html>
