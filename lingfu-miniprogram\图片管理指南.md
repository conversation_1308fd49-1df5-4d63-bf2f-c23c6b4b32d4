# 📦 2MB包大小管理指南

## 🎯 精确的大小分配

### 总体规划 (2048KB)
```
代码文件:     ~300KB  (15%)
图片资源:     1500KB  (73%) 
系统预留:     248KB   (12%)
```

### 图片分配策略
- **20张灵符图片**: 每张最大 **60KB**
- **总图片大小**: 最大 **1200KB**
- **安全余量**: 300KB

## 📁 文件夹结构

```
lingfu-miniprogram/
├── images/
│   ├── lingfu/           # 灵符图片 (20张)
│   │   ├── taishang.jpg     # 太上老君符 (≤60KB)
│   │   ├── wulei.jpg        # 五雷护身符 (≤60KB)
│   │   ├── pingan.jpg       # 平安如意符 (≤60KB)
│   │   ├── bixie.jpg        # 辟邪镇宅符 (≤60KB)
│   │   ├── wenchang.jpg     # 文昌帝君符 (≤60KB)
│   │   ├── zhihui.jpg       # 智慧开启符 (≤60KB)
│   │   ├── kaoshi.jpg       # 考试顺利符 (≤60KB)
│   │   ├── zhaocai.jpg      # 招财进宝符 (≤60KB)
│   │   ├── chuangyi.jpg     # 创意灵感符 (≤60KB)
│   │   ├── shiye.jpg        # 事业腾飞符 (≤60KB)
│   │   ├── hezuo.jpg        # 合作共赢符 (≤60KB)
│   │   ├── yaoshi.jpg       # 药师佛符 (≤60KB)
│   │   ├── changshou.jpg    # 长寿健康符 (≤60KB)
│   │   ├── kangfu.jpg       # 康复平安符 (≤60KB)
│   │   ├── yuelao.jpg       # 月老红线符 (≤60KB)
│   │   ├── taohua.jpg       # 桃花运符 (≤60KB)
│   │   ├── fuqi.jpg         # 夫妻和合符 (≤60KB)
│   │   ├── jiahe.jpg        # 家和万事兴符 (≤60KB)
│   │   ├── zisun.jpg        # 子孙满堂符 (≤60KB)
│   │   └── xiaoshun.jpg     # 孝顺和睦符 (≤60KB)
│   ├── icons/            # 小图标 (总计≤100KB)
│   │   ├── shake.png        # 摇一摇图标 (≤10KB)
│   │   ├── voice.png        # 声控图标 (≤10KB)
│   │   └── category.png     # 分类图标 (≤10KB)
│   └── bg/               # 背景图 (总计≤200KB)
│       ├── main-bg.jpg      # 主背景 (≤100KB)
│       └── result-bg.jpg    # 结果背景 (≤100KB)
```

## 🖼️ 图片压缩标准

### 灵符图片规格
- **尺寸**: 400x600px (2:3比例)
- **格式**: JPEG
- **质量**: 65-75%
- **大小**: ≤60KB
- **色彩**: sRGB色彩空间

### 压缩工具推荐
1. **在线工具**: TinyPNG, Squoosh
2. **软件**: Photoshop, GIMP
3. **批量处理**: ImageOptim (Mac), PNGGauntlet (Win)

### 压缩命令 (ImageMagick)
```bash
# 批量压缩灵符图片
magick convert input.jpg -resize 400x600 -quality 70 -strip output.jpg

# 检查文件大小
ls -lh images/lingfu/*.jpg
```

## 📊 包大小监控

### 实时监控脚本
```javascript
// utils/size-monitor.js
const fs = require('fs')
const path = require('path')

function getDirectorySize(dirPath) {
  let totalSize = 0
  const files = fs.readdirSync(dirPath)
  
  files.forEach(file => {
    const filePath = path.join(dirPath, file)
    const stats = fs.statSync(filePath)
    
    if (stats.isDirectory()) {
      totalSize += getDirectorySize(filePath)
    } else {
      totalSize += stats.size
    }
  })
  
  return totalSize
}

function formatSize(bytes) {
  return (bytes / 1024).toFixed(2) + ' KB'
}

// 监控各目录大小
const projectSize = getDirectorySize('./')
const imageSize = getDirectorySize('./images')
const lingfuSize = getDirectorySize('./images/lingfu')

console.log('=== 包大小监控 ===')
console.log(`总项目大小: ${formatSize(projectSize)}`)
console.log(`图片总大小: ${formatSize(imageSize)}`)
console.log(`灵符图片: ${formatSize(lingfuSize)}`)
console.log(`剩余空间: ${formatSize(2048 * 1024 - projectSize)}`)

if (projectSize > 2048 * 1024) {
  console.error('⚠️ 警告: 包大小超过2MB限制!')
}
```

## 🎨 Canvas绘制备选方案

如果图片太大，可以使用Canvas动态绘制：

```javascript
// utils/canvas-draw.js
function drawLingfuCanvas(canvasId, lingfuData) {
  const ctx = wx.createCanvasContext(canvasId)
  
  // 设置背景
  ctx.setFillStyle('#F5F5DC')
  ctx.fillRect(0, 0, 300, 400)
  
  // 绘制边框
  ctx.setStrokeStyle('#DC143C')
  ctx.setLineWidth(4)
  ctx.strokeRect(10, 10, 280, 380)
  
  // 绘制符咒名称
  ctx.setFillStyle('#8B4513')
  ctx.setFontSize(24)
  ctx.setTextAlign('center')
  ctx.fillText(lingfuData.name, 150, 50)
  
  // 绘制符咒图案 (简化版)
  ctx.setFillStyle('#DC143C')
  ctx.setFontSize(60)
  ctx.fillText(lingfuData.symbol || '符', 150, 200)
  
  // 绘制祝福语
  ctx.setFillStyle('#8B4513')
  ctx.setFontSize(16)
  ctx.fillText(lingfuData.blessing, 150, 350)
  
  ctx.draw()
}

module.exports = { drawLingfuCanvas }
```

## 🔧 自动化工具

### 图片批量重命名
```bash
#!/bin/bash
# rename-images.sh
cd images/lingfu/

# 重命名为标准格式
mv "太上老君.jpg" "taishang.jpg"
mv "五雷护身.jpg" "wulei.jpg"
mv "平安如意.jpg" "pingan.jpg"
# ... 其他文件
```

### 大小检查脚本
```bash
#!/bin/bash
# check-size.sh
echo "=== 检查图片大小 ==="
for file in images/lingfu/*.jpg; do
  size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file")
  kb_size=$((size / 1024))
  if [ $kb_size -gt 60 ]; then
    echo "⚠️  $file: ${kb_size}KB (超过60KB限制)"
  else
    echo "✅ $file: ${kb_size}KB"
  fi
done
```

## 📋 检查清单

### 上传前检查
- [ ] 每张灵符图片 ≤ 60KB
- [ ] 总图片大小 ≤ 1.2MB
- [ ] 项目总大小 ≤ 1.8MB (留余量)
- [ ] 图片格式统一为JPEG
- [ ] 图片尺寸统一为400x600px

### 优化建议
1. **优先压缩大图片**
2. **使用渐进式JPEG**
3. **移除图片元数据**
4. **考虑WebP格式** (如果小程序支持)
5. **定期清理无用文件**

---

**严格控制在2MB以内，确保小程序正常发布！** 📦✨
