# 灵符驾到 - 快速开始指南

## 🎯 测试版本特色

**20张精选灵符** - 涵盖6大分类，使用emoji图标，无需担心包大小
**3种抽签方式**：
1. **摇一摇** - 摇动手机抽取灵符
2. **声控** - 对着手机吹口仙气
3. **分类选择** - 选择愿望类别精准抽取

## 📱 微信小程序包大小解决方案

### ❌ 避免的问题
- 外链图片不被允许
- 云存储需要付费
- 20张图片可能超过2MB限制

### ✅ 我们的解决方案
- **使用emoji图标** 代替真实图片
- **Canvas绘制** 可以后期添加自定义图案
- **分包加载** 如需要真实图片可分包处理
- **本地小图** 压缩后的小尺寸图片

## 🚀 快速启动步骤

### 1. 导入项目
```bash
# 打开微信开发者工具
# 选择 "导入项目"
# 选择 lingfu-miniprogram 文件夹
# 填写你的小程序 AppID
```

### 2. 修改配置
在 `project.config.json` 中：
```json
{
  "appid": "你的小程序AppID"
}
```

### 3. 测试功能
- **摇一摇测试** - 在真机上摇动手机
- **声控测试** - 点击声控标签，允许录音权限，对着手机吹气
- **分类测试** - 点击分类标签，选择不同愿望类别

### 4. 权限配置
小程序需要以下权限：
- **录音权限** - 用于声控功能
- **设备运动** - 用于摇一摇功能

## 🎨 自定义图片方案

### 方案1: 压缩图片（推荐测试）
```bash
# 将图片压缩到10KB以下
# 20张图片 = 200KB，完全在限制内
# 放在 /images/lingfu/ 目录下
```

### 方案2: 分包加载
```json
// app.json
{
  "subpackages": [
    {
      "root": "packageA",
      "pages": ["pages/lingfu-images/index"]
    }
  ]
}
```

### 方案3: Canvas绘制
```javascript
// 使用Canvas API绘制传统符咒样式
const ctx = wx.createCanvasContext('lingfu-canvas')
ctx.setFillStyle('#DC143C')
ctx.fillText('太上老君急急如律令', 10, 30)
ctx.draw()
```

## 🔧 核心文件说明

### 主要页面
- `pages/index/` - 首页，包含3种抽签方式
- `pages/detail/` - 灵符详情页
- `pages/library/` - 灵符库（浏览所有20张）

### 核心工具
- `utils/draw.js` - 20张灵符数据和抽签逻辑
- `utils/voice.js` - 声控检测（吹气识别）
- `app.js` - 全局配置和数据管理

### 数据结构
```javascript
// 每张灵符包含
{
  id: 'lingfu_001',
  name: '太上老君急急如律令',
  category: ['luck'],
  image: '🛡️', // emoji图标
  description: '简短描述',
  blessing: '祝福语',
  rarity: 'legendary', // 稀有度
  power: 95 // 灵力值
}
```

## 🎯 测试重点

### 1. 摇一摇功能
- 在真机上测试（模拟器无重力感应）
- 调整灵敏度阈值
- 测试防抖机制

### 2. 声控功能
- 测试录音权限申请
- 调整吹气检测阈值
- 测试音量显示

### 3. 分类抽签
- 测试6个分类的筛选
- 验证每个分类的灵符数量
- 测试自动抽签逻辑

## 📊 性能优化

### 包大小优化
- 当前版本：约500KB（主要是代码）
- emoji图标：几乎不占空间
- 可扩展空间：1.5MB

### 加载优化
- 本地数据存储
- 图片懒加载
- 分包按需加载

## 🐛 常见问题

### Q: 声控功能不工作？
A: 检查录音权限，在真机上测试，调整检测阈值

### Q: 摇一摇不灵敏？
A: 在真机上测试，调整 `speed > 300` 阈值

### Q: 想要真实图片？
A: 压缩图片到10KB以下，或使用分包加载

### Q: 每日限制3次太少？
A: 在 `maxDailyDraw: 3` 处修改次数

## 🎨 后续扩展

1. **真实图片** - 制作传统符咒样式图片
2. **音效** - 添加古典音乐和音效
3. **动画** - 更丰富的抽签动画
4. **社交** - 分享和排行榜功能
5. **个性化** - 用户自定义愿望

---

**开始测试你的灵符小程序吧！** 🎉✨
