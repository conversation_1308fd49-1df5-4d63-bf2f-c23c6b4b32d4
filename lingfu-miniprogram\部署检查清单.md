# 📋 部署检查清单 - 确保2MB以内

## 🎯 包大小控制目标

### 总体分配 (2048KB)
- ✅ **代码文件**: ≤300KB (15%)
- ✅ **灵符图片**: ≤1200KB (20张×60KB)
- ✅ **其他图片**: ≤200KB (图标+背景)
- ✅ **系统预留**: ≤348KB (17%)

## 📁 必需文件检查

### 核心代码文件
- [ ] `app.js` - 应用入口
- [ ] `app.json` - 应用配置
- [ ] `app.wxss` - 全局样式
- [ ] `project.config.json` - 项目配置

### 页面文件
- [ ] `pages/index/` - 首页（摇一摇+声控+分类）
- [ ] `pages/detail/` - 灵符详情页
- [ ] `pages/library/` - 灵符库页面

### 工具文件
- [ ] `utils/draw.js` - 抽签逻辑和灵符数据
- [ ] `utils/voice.js` - 声控检测
- [ ] `utils/size-monitor.js` - 包大小监控

## 🖼️ 图片文件检查

### 灵符图片 (20张，每张≤60KB)
- [ ] `images/lingfu/taishang.jpg` - 太上老君符
- [ ] `images/lingfu/wulei.jpg` - 五雷护身符
- [ ] `images/lingfu/pingan.jpg` - 平安如意符
- [ ] `images/lingfu/bixie.jpg` - 辟邪镇宅符
- [ ] `images/lingfu/wenchang.jpg` - 文昌帝君符
- [ ] `images/lingfu/zhihui.jpg` - 智慧开启符
- [ ] `images/lingfu/kaoshi.jpg` - 考试顺利符
- [ ] `images/lingfu/zhaocai.jpg` - 招财进宝符
- [ ] `images/lingfu/chuangyi.jpg` - 创意灵感符
- [ ] `images/lingfu/shiye.jpg` - 事业腾飞符
- [ ] `images/lingfu/hezuo.jpg` - 合作共赢符
- [ ] `images/lingfu/yaoshi.jpg` - 药师佛符
- [ ] `images/lingfu/changshou.jpg` - 长寿健康符
- [ ] `images/lingfu/kangfu.jpg` - 康复平安符
- [ ] `images/lingfu/yuelao.jpg` - 月老红线符
- [ ] `images/lingfu/taohua.jpg` - 桃花运符
- [ ] `images/lingfu/fuqi.jpg` - 夫妻和合符
- [ ] `images/lingfu/jiahe.jpg` - 家和万事兴符
- [ ] `images/lingfu/zisun.jpg` - 子孙满堂符
- [ ] `images/lingfu/xiaoshun.jpg` - 孝顺和睦符

### 图标文件 (每张≤10KB)
- [ ] `images/icons/shake.png` - 摇一摇图标
- [ ] `images/icons/voice.png` - 声控图标
- [ ] `images/icons/category.png` - 分类图标

### 背景图片 (每张≤100KB)
- [ ] `images/bg/main-bg.jpg` - 主背景
- [ ] `images/bg/result-bg.jpg` - 结果背景

## 🔧 技术检查

### 功能测试
- [ ] **摇一摇功能** - 真机测试重力感应
- [ ] **声控功能** - 录音权限+吹气检测
- [ ] **分类抽签** - 6个分类正常工作
- [ ] **每日限制** - 3次抽签限制生效
- [ ] **收藏功能** - 本地存储正常
- [ ] **分享功能** - 分享卡片生成

### 权限配置
- [ ] **录音权限** - `scope.record`
- [ ] **设备运动** - 重力感应API
- [ ] **本地存储** - 用户数据保存

### 性能优化
- [ ] **图片懒加载** - 大图片按需加载
- [ ] **本地缓存** - 抽签记录缓存
- [ ] **动画优化** - 流畅的交互动画

## 📊 大小监控命令

### 使用内置监控工具
```javascript
// 在小程序中运行
const sizeMonitor = require('./utils/size-monitor.js')
const report = sizeMonitor.showSizeReport()
console.log('包大小报告:', report)
```

### 手动检查命令 (开发环境)
```bash
# Windows PowerShell
Get-ChildItem -Recurse lingfu-miniprogram | Measure-Object -Property Length -Sum

# 检查单个文件夹
Get-ChildItem -Recurse lingfu-miniprogram\images\lingfu | Measure-Object -Property Length -Sum
```

### 在线压缩工具
1. **TinyPNG** - https://tinypng.com/
2. **Squoosh** - https://squoosh.app/
3. **本地工具** - 使用 `图片压缩工具.html`

## ⚠️ 常见问题解决

### 包大小超限
1. **压缩图片** - 降低质量到60-70%
2. **减少图片数量** - 暂时移除部分图片
3. **使用分包** - 将部分图片移到分包
4. **Canvas绘制** - 用代码生成简单图案

### 图片加载失败
1. **检查路径** - 确保路径正确
2. **检查格式** - 使用JPEG格式
3. **检查大小** - 单张图片不超过60KB

### 功能异常
1. **真机测试** - 摇一摇和声控需要真机
2. **权限检查** - 确保录音权限开启
3. **网络环境** - 某些功能需要网络

## 🚀 发布前最终检查

### 微信开发者工具检查
- [ ] **编译通过** - 无错误和警告
- [ ] **包大小显示** - 确认≤2MB
- [ ] **真机预览** - 扫码真机测试
- [ ] **体验版测试** - 上传体验版测试

### 功能完整性测试
- [ ] **三种抽签方式** 都能正常工作
- [ ] **20张灵符** 都能正常显示
- [ ] **音效和震动** 反馈正常
- [ ] **数据存储** 本地保存正常

### 用户体验测试
- [ ] **界面美观** - 传统文化风格
- [ ] **操作流畅** - 无卡顿现象
- [ ] **反馈及时** - 操作有即时反馈
- [ ] **错误处理** - 异常情况有提示

## 📈 优化建议

### 立即优化
1. **图片批量压缩** - 使用工具统一处理
2. **代码压缩** - 移除注释和空行
3. **无用文件清理** - 删除测试文件

### 后续优化
1. **分包加载** - 将图片移到分包
2. **CDN加速** - 使用图片CDN（需付费）
3. **WebP格式** - 更好的压缩比（需兼容性测试）

---

## ✅ 最终确认

- [ ] 总包大小 ≤ 1.8MB（留安全余量）
- [ ] 所有功能测试通过
- [ ] 真机测试无问题
- [ ] 准备发布到微信小程序平台

**严格按照此清单检查，确保小程序顺利发布！** 🎉
