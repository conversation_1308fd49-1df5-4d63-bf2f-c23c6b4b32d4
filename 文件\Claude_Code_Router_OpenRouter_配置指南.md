# Claude Code Router + OpenRouter 配置指南

## 系统要求
- WSL (Windows Subsystem for Linux)
- Node.js 18+
- <PERSON> Code 已安装

## 1. 安装 Claude Code Router

```bash
npm install -g @musistudio/claude-code-router
```

验证安装：
```bash
ccr -v
```

## 2. 创建配置文件

删除旧配置并创建新配置：

```bash
rm ~/.claude-code-router/config.json

cat > ~/.claude-code-router/config.json << 'EOF'
{
  "LOG": false,
  "HOST": "127.0.0.1",
  "MAX_RETRIES": 2,
  "RETRY_DELAY": 20000,
  "Providers": [
    {
      "name": "openrouter",
      "api_base_url": "https://openrouter.ai/api/v1/chat/completions",
      "api_key": "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",
      "models": ["moonshotai/kimi-k2"],
      "transformer": { "use": ["openrouter"] }
    }
  ],
  "Router": {
    "default": "openrouter,moonshotai/kimi-k2"
  }
}
EOF
```

## 3. 验证配置

检查配置文件：
```bash
cat ~/.claude-code-router/config.json
```

验证 JSON 格式：
```bash
python3 -m json.tool ~/.claude-code-router/config.json
```

## 4. 创建启动批处理文件

创建 `claude-router.bat` 文件：

```batch
@echo off
echo Starting Claude Code Router (Multi-Model)...
wsl bash -c "cd /mnt/d/360MoveData/Users/<USER>/Desktop/Gemini_CLI && ccr code"
pause
```

## 5. 启动和使用

### 启动方式一：使用批处理文件
双击运行 `claude-router.bat`

### 启动方式二：在 WSL 中直接启动
```bash
cd /mnt/d/360MoveData/Users/<USER>/Desktop/Gemini_CLI
ccr code
```

## 6. 配置说明

### 关键配置项
- `MAX_RETRIES: 2` - 重试次数
- `RETRY_DELAY: 20000` - 重试延迟（毫秒）
- `transformer: { "use": ["openrouter"] }` - 必需的格式转换器
- `moonshotai/kimi-k2` - 付费版 Kimi K2 模型

### API Key 配置
- OpenRouter API Key: `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`
- 模型: `moonshotai/kimi-k2` (付费版本)

## 7. 故障排除

### 重启服务
```bash
ccr stop
ccr code
```

### 检查服务状态
```bash
ccr status
```

### 验证 API Key
```bash
curl -H "Authorization: Bearer 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" \
  https://openrouter.ai/api/v1/auth/key
```

## 8. 使用技巧

### 模型切换（如果有多个模型）
在 Claude Code 中使用：
```
/model openrouter,moonshotai/kimi-k2
```

### 查看帮助
```
/help
```

## 9. 备用配置（anyrouter 直连）

如果 Router 有问题，可以使用直连方式：

创建 `claude-anyrouter.bat`：
```batch
@echo off
echo Starting Claude Code with anyrouter...
wsl bash -c "cd /mnt/d/360MoveData/Users/<USER>/Desktop/Gemini_CLI && export ANTHROPIC_AUTH_TOKEN=sk-5AMFJjsZ8NARYU9IP9mECr4HQDAGnSGERD6i4HIsCSPGudgy && export ANTHROPIC_BASE_URL=https://anyrouter.top && claude"
pause
```

## 10. 成功标志

启动成功后应该看到：
- 欢迎界面显示 `API Base URL: http://127.0.0.1:3456`
- 能够正常对话，无 404 或 429 错误
- 回答质量高，响应速度快

## 11. 重要说明

### 为什么需要 Claude Code Router？
- **API 格式兼容性**：Claude Code 使用 Anthropic 格式，OpenRouter 使用 OpenAI 格式
- **转换器作用**：`transformer: { "use": ["openrouter"] }` 负责格式转换
- **直连问题**：Claude Code 直连 OpenRouter 会出现 405 错误

### 配置文件关键点
1. **必须包含 transformer**：否则会出现格式兼容性问题
2. **正确的模型 ID**：`moonshotai/kimi-k2` (付费版本)
3. **合理的重试设置**：避免过度重试导致速率限制

---

**注意事项：**
1. 确保 WSL 中已安装 Claude Code
2. 网络连接正常
3. API Key 有效且有余额
4. 工作目录路径正确：`/mnt/d/360MoveData/Users/<USER>/Desktop/Gemini_CLI`
