# Claude Code Router 快速安装指南 🚀

## 📋 项目简介

Claude Code Router 是一个开源项目，可以让 Claude Code 使用任何 OpenAI 格式的 API，包括：
- 🆓 OpenRouter 免费模型 (DeepSeek、Llama 等)
- 💎 Google Gemini API
- 🌐 其他兼容 OpenAI 格式的 API

**优势：** 完全免费使用，开源透明，支持多种模型切换

---

## 🛠️ 安装步骤

### 第一步：安装 Claude Code Router
```bash
npm install -g @musistudio/claude-code-router
```

### 第二步：验证安装
```bash
ccr --version
```

### 第三步：创建配置目录
```bash
mkdir -p ~/.claude-code-router
```

---

## 🔑 获取 API Key

### OpenRouter (推荐 - 有免费模型)
1. 访问：https://openrouter.ai/
2. 注册免费账户
3. 获取 API Key (以 sk- 开头)

### Google Gemini (可选)
1. 访问：https://aistudio.google.com/
2. 获取免费 API Key

---

## ⚙️ 配置文件

### 方案1：OpenRouter 免费模型 (推荐)
创建文件：`~/.claude-code-router/config.json`

```json
{
  "OPENAI_API_KEY": "your-openrouter-api-key",
  "OPENAI_BASE_URL": "https://openrouter.ai/api/v1",
  "OPENAI_MODEL": "deepseek/deepseek-chat:free",
  "Providers": [
    {
      "name": "openrouter",
      "api_base_url": "https://openrouter.ai/api/v1",
      "api_key": "your-openrouter-api-key",
      "models": [
        "deepseek/deepseek-chat:free",
        "deepseek/deepseek-r1:free",
        "meta-llama/llama-3.2-3b-instruct:free",
        "microsoft/phi-3-mini-128k-instruct:free"
      ]
    }
  ],
  "Router": {
    "background": "openrouter,deepseek/deepseek-chat:free",
    "think": "openrouter,deepseek/deepseek-r1:free",
    "longContext": "openrouter,deepseek/deepseek-r1:free"
  }
}
```

### 方案2：Google Gemini (免费额度)
```json
{
  "OPENAI_API_KEY": "your-gemini-api-key",
  "OPENAI_BASE_URL": "https://generativelanguage.googleapis.com/v1beta",
  "OPENAI_MODEL": "gemini-2.5-flash",
  "Providers": [
    {
      "name": "gemini",
      "api_base_url": "https://generativelanguage.googleapis.com/v1beta",
      "api_key": "your-gemini-api-key",
      "models": ["gemini-2.5-pro", "gemini-2.5-flash"]
    }
  ],
  "Router": {
    "background": "gemini,gemini-2.5-flash",
    "think": "gemini,gemini-2.5-pro",
    "longContext": "gemini,gemini-2.5-pro"
  }
}
```

---

## 🚀 使用方法

### 启动 Claude Code Router
```bash
# 在项目目录中运行
ccr code
```

### 切换回 anyrouter.top (备用方案)
```bash
# 设置环境变量
export ANTHROPIC_AUTH_TOKEN=sk-5AMFJjsZ8NARYU9IP9mECr4HQDAGnSGERD6i4HIsCSPGudgy
export ANTHROPIC_BASE_URL=https://anyrouter.top

# 启动原版 Claude Code
claude
```

---

## 🔄 快速切换脚本

创建文件：`switch-claude.sh`

```bash
#!/bin/bash
echo "🚀 选择 Claude Code 方案:"
echo "1) anyrouter.top (付费，稳定，剩余700刀)"
echo "2) OpenRouter (免费，开源)"
echo "3) Google Gemini (免费额度)"
read -p "请选择 (1-3): " choice

case $choice in
    1) 
        echo "🔄 切换到 anyrouter.top..."
        export ANTHROPIC_AUTH_TOKEN=sk-5AMFJjsZ8NARYU9IP9mECr4HQDAGnSGERD6i4HIsCSPGudgy
        export ANTHROPIC_BASE_URL=https://anyrouter.top
        claude
        ;;
    2) 
        echo "🔄 切换到 OpenRouter 免费模型..."
        ccr code
        ;;
    3) 
        echo "🔄 切换到 Google Gemini..."
        # 需要修改配置文件为 Gemini 配置
        ccr code
        ;;
    *) 
        echo "❌ 无效选择"
        ;;
esac
```

### 使用切换脚本
```bash
chmod +x switch-claude.sh
./switch-claude.sh
```

---

## 🧪 测试安装

### 1. 测试 OpenRouter 方案
```bash
ccr code
# 在 Claude Code 中输入
你好，请介绍一下你自己
```

### 2. 测试 anyrouter.top 备用方案
```bash
export ANTHROPIC_AUTH_TOKEN=sk-5AMFJjsZ8NARYU9IP9mECr4HQDAGnSGERD6i4HIsCSPGudgy
export ANTHROPIC_BASE_URL=https://anyrouter.top
claude
# 测试对话
你好
```

---

## 💡 使用建议

### 🎯 **什么时候用哪个方案：**

| 场景 | 推荐方案 | 原因 |
|------|----------|------|
| **日常学习** | OpenRouter 免费 | 节省费用 |
| **重要项目** | anyrouter.top | 稳定可靠 |
| **测试功能** | OpenRouter | 免费试错 |
| **大量使用** | anyrouter.top | 性能更好 |

### 🔧 **配置优化：**
- 定期检查 OpenRouter 新增的免费模型
- 根据任务类型选择合适的模型
- 保持两套配置都可用

---

## ❓ 常见问题

### Q: 两个方案会冲突吗？
A: 不会！它们使用不同的命令和配置方式，完全独立。

### Q: 如何知道当前使用的是哪个方案？
A: 
- `claude` 命令 = anyrouter.top
- `ccr code` 命令 = Claude Code Router

### Q: OpenRouter 免费模型有限制吗？
A: 有一定的速率限制，但对个人使用足够。

### Q: 配置文件在哪里？
A: `~/.claude-code-router/config.json`

---

## 🎉 安装完成

现在您有两个选择：
- 💰 **anyrouter.top** - 付费稳定 (700刀余额)
- 🆓 **OpenRouter** - 免费开源

根据需求灵活切换，享受最佳的 Claude Code 体验！🚀
