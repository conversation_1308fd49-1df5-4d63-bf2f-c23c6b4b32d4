# Claude Code Router 完全隔离安装方案 🛡️

## 🎯 设计原则

**绝对不干扰现有 anyrouter.top 配置！**
- ✅ 使用不同的目录
- ✅ 使用不同的命令
- ✅ 使用不同的配置文件
- ✅ 可以同时运行两个方案

---

## 🗂️ 隔离目录结构

```
/root/
├── .claude.json                    # anyrouter.top 配置 (保持不变)
├── claude-router/                  # 新建隔离目录
│   ├── config.json                 # Router 专用配置
│   ├── start-router.sh             # Router 启动脚本
│   └── logs/                       # Router 日志目录
└── scripts/
    ├── start-anyrouter.sh          # anyrouter.top 启动脚本
    └── switch-claude.sh             # 切换脚本
```

---

## 🛠️ 完全隔离安装步骤

### 第一步：创建隔离目录
```bash
# 创建专用目录
mkdir -p /root/claude-router/logs
mkdir -p /root/scripts
```

### 第二步：安装 Claude Code Router
```bash
# 全局安装 (不影响现有配置)
npm install -g @musistudio/claude-code-router
```

### 第三步：创建隔离配置文件
```bash
# 在隔离目录创建配置
cat > /root/claude-router/config.json << 'EOF'
{
  "OPENAI_API_KEY": "your-openrouter-key-here",
  "OPENAI_BASE_URL": "https://openrouter.ai/api/v1",
  "OPENAI_MODEL": "deepseek/deepseek-chat:free",
  "Providers": [
    {
      "name": "openrouter",
      "api_base_url": "https://openrouter.ai/api/v1",
      "api_key": "your-openrouter-key-here",
      "models": [
        "deepseek/deepseek-chat:free",
        "deepseek/deepseek-r1:free",
        "meta-llama/llama-3.2-3b-instruct:free"
      ]
    }
  ],
  "Router": {
    "background": "openrouter,deepseek/deepseek-chat:free",
    "think": "openrouter,deepseek/deepseek-r1:free",
    "longContext": "openrouter,deepseek/deepseek-r1:free"
  }
}
EOF
```

---

## 🚀 启动脚本

### anyrouter.top 启动脚本
```bash
cat > /root/scripts/start-anyrouter.sh << 'EOF'
#!/bin/bash
echo "🔄 启动 anyrouter.top Claude Code..."
echo "💰 使用付费服务 (700刀余额)"

# 设置 anyrouter.top 环境变量
export ANTHROPIC_AUTH_TOKEN=sk-5AMFJjsZ8NARYU9IP9mECr4HQDAGnSGERD6i4HIsCSPGudgy
export ANTHROPIC_BASE_URL=https://anyrouter.top

# 显示当前配置
echo "📡 API Base URL: $ANTHROPIC_BASE_URL"
echo "🔑 Token: ${ANTHROPIC_AUTH_TOKEN:0:10}..."

# 启动 Claude Code
claude
EOF

chmod +x /root/scripts/start-anyrouter.sh
```

### Claude Code Router 启动脚本
```bash
cat > /root/claude-router/start-router.sh << 'EOF'
#!/bin/bash
echo "🚀 启动 Claude Code Router..."
echo "🆓 使用免费 OpenRouter 模型"

# 切换到 Router 配置目录
cd /root/claude-router

# 设置 Router 配置文件路径
export CLAUDE_CODE_ROUTER_CONFIG=/root/claude-router/config.json

# 显示配置信息
echo "📁 配置文件: $CLAUDE_CODE_ROUTER_CONFIG"
echo "📊 日志目录: /root/claude-router/logs"

# 启动 Router (使用隔离配置)
ccr code --config /root/claude-router/config.json
EOF

chmod +x /root/claude-router/start-router.sh
```

---

## 🔄 智能切换脚本

```bash
cat > /root/scripts/switch-claude.sh << 'EOF'
#!/bin/bash
clear
echo "🎯 Claude Code 方案选择器"
echo "================================"
echo ""
echo "当前可用方案："
echo "1) 💰 anyrouter.top  (付费稳定，700刀余额，无MCP)"
echo "2) 🆓 OpenRouter     (免费开源，可能支持MCP)"
echo "3) 📊 查看运行状态"
echo "4) 🛑 退出"
echo ""
read -p "请选择方案 (1-4): " choice

case $choice in
    1) 
        echo ""
        echo "🔄 启动 anyrouter.top 方案..."
        /root/scripts/start-anyrouter.sh
        ;;
    2) 
        echo ""
        echo "🔄 启动 OpenRouter 方案..."
        /root/claude-router/start-router.sh
        ;;
    3)
        echo ""
        echo "📊 检查运行状态..."
        echo "anyrouter.top 进程:"
        ps aux | grep claude | grep -v grep
        echo ""
        echo "Router 进程:"
        ps aux | grep ccr | grep -v grep
        echo ""
        echo "端口占用:"
        netstat -tlnp | grep 3456
        ;;
    4)
        echo "👋 退出"
        exit 0
        ;;
    *)
        echo "❌ 无效选择，请重新运行脚本"
        ;;
esac
EOF

chmod +x /root/scripts/switch-claude.sh
```

---

## 🔒 安全隔离验证

### 验证配置隔离
```bash
# 检查 anyrouter.top 配置未被修改
cat ~/.claude.json | head -5

# 检查 Router 配置独立存在
cat /root/claude-router/config.json | head -5

# 确认目录隔离
ls -la /root/claude-router/
ls -la /root/scripts/
```

### 验证进程隔离
```bash
# 查看不同的启动命令
which claude          # 原始 Claude Code
which ccr             # Claude Code Router

# 查看进程
ps aux | grep claude
ps aux | grep ccr
```

---

## 🚀 使用方法

### 方法1：使用切换脚本 (推荐)
```bash
/root/scripts/switch-claude.sh
```

### 方法2：直接启动
```bash
# 启动 anyrouter.top
/root/scripts/start-anyrouter.sh

# 启动 OpenRouter (新终端)
/root/claude-router/start-router.sh
```

### 方法3：同时运行 (不同终端)
```bash
# 终端1: anyrouter.top
/root/scripts/start-anyrouter.sh

# 终端2: OpenRouter
/root/claude-router/start-router.sh
```

---

## 🛡️ 安全保障

### 完全隔离保证
- ✅ **配置文件隔离** - 不同目录，不会冲突
- ✅ **环境变量隔离** - 脚本内设置，不影响全局
- ✅ **进程隔离** - 不同命令，可同时运行
- ✅ **端口隔离** - Router 使用 3456，anyrouter 直连

### 回滚方案
```bash
# 如果出现问题，立即回滚
rm -rf /root/claude-router
rm -rf /root/scripts
npm uninstall -g @musistudio/claude-code-router

# anyrouter.top 配置完全不受影响
```

---

## 🧪 测试步骤

### 1. 测试 anyrouter.top (确保未受影响)
```bash
/root/scripts/start-anyrouter.sh
# 测试: 你好
# 测试: /status
```

### 2. 测试 OpenRouter (新功能)
```bash
/root/claude-router/start-router.sh
# 测试: 你好
# 测试: /mcp (查看是否支持MCP)
```

### 3. 同时运行测试
```bash
# 两个不同终端同时启动，验证无冲突
```

---

## 🎯 总结

这个方案确保：
- 🛡️ **绝对安全** - anyrouter.top 配置零影响
- 🔄 **灵活切换** - 一键启动不同方案
- 🚀 **同时运行** - 可以并行使用
- 📊 **完整测试** - 对比 MCP 功能差异

现在可以安全地测试 Claude Code Router，同时保持 anyrouter.top 作为稳定备用！🚀
