# Claude Code 换 API Key 简单方法 🔑

## 🚀 最简单的三步操作

当您的 API Key 额度用完时，按以下步骤操作：

### 第一步：退出 Claude Code
```bash
/exit
```
或者直接关闭终端窗口

### 第二步：设置新的 API Key
```bash
export ANTHROPIC_AUTH_TOKEN=sk-your-new-key-here
```

### 第三步：重新启动 Claude Code
```bash
claude
```

## ✅ 验证新 Key 是否生效

启动后可以检查状态：
```bash
/status
```

## 💡 为什么这个方法最好

- ✨ **无需复杂配置** - 不用编辑配置文件
- ⚡ **立即生效** - 设置后马上可用  
- 🛡️ **不影响系统** - 不会改变系统设置
- 🔄 **灵活切换** - 可以随时换不同的 Key
- ⏱️ **操作快速** - 总共不到1分钟搞定

## 📋 常用命令备忘

### 查看当前 Key（可选）
```bash
echo $ANTHROPIC_AUTH_TOKEN
```

### 查看 API 地址（可选）
```bash
echo $ANTHROPIC_BASE_URL
```

### 完整的环境设置（如果需要）
```bash
export ANTHROPIC_AUTH_TOKEN=sk-your-new-key-here
export ANTHROPIC_BASE_URL=https://anyrouter.top
```

## 🎯 使用建议

1. **准备多个 Key** - 可以准备几个备用 Key
2. **记录 Key 信息** - 安全地保存 Key 和剩余额度
3. **定期检查** - 用 `/cost` 命令查看使用情况

## ⚠️ 注意事项

- Key 格式必须以 `sk-` 开头
- 不要在代码中硬编码 API Key
- 不要分享包含 API Key 的截图
- 确保网络连接正常

---

**总结：退出 → 换 Key → 重启，就这么简单！** 🎉
