# Claude Code 简单教程 🚀

## 📖 目录
1. [什么是 Claude Code](#什么是-claude-code)
2. [安装和配置](#安装和配置)
3. [第一次使用](#第一次使用)
4. [基础操作](#基础操作)
5. [常用命令](#常用命令)
6. [实际案例](#实际案例)
7. [进阶技巧](#进阶技巧)
8. [常见问题](#常见问题)

---

## 什么是 Claude Code

Claude Code 是 Anthropic 官方推出的**命令行 AI 编程助手**，就像有一个聪明的程序员坐在你旁边，随时帮你：
- 📝 写代码和修改文件
- 🔍 分析项目结构
- 🐛 找 bug 和修复问题
- 📚 解释代码逻辑
- 🧪 运行测试和命令

**简单理解：** 它是一个住在终端里的 AI 程序员！

---

## 安装和配置

### 第一步：安装 Node.js
确保你的电脑有 Node.js（版本 ≥ 18）
```bash
node --version  # 检查版本
```

### 第二步：安装 Claude Code
```bash
npm install -g @anthropic-ai/claude-code
```

### 第三步：配置环境变量
```bash
# 设置 API Token（从服务商获取）
export ANTHROPIC_AUTH_TOKEN=sk-your-token-here

# 设置 API 地址（如果使用第三方服务）
export ANTHROPIC_BASE_URL=https://anyrouter.top
```

### 第四步：验证安装
```bash
claude --version
```

---

## 第一次使用

### 启动 Claude Code
```bash
# 在你的项目文件夹中运行
cd your-project-folder
claude
```

### 初始设置
第一次运行会有几个选择：
1. **选择主题** - 选你喜欢的颜色主题
2. **安全须知** - 按 Enter 确认
3. **Terminal 配置** - 使用默认设置
4. **信任目录** - 按 Enter 信任当前目录

### 成功标志
看到这样的界面就成功了：
```
Claude Code v1.x.x
Working directory: /your/project/path
Type your message or use /help for commands
>
```

---

## 基础操作

### 💬 基本对话
直接输入你想要的功能：
```
> 帮我创建一个简单的 Python 计算器
> 解释一下这个函数是做什么的
> 这个错误怎么修复？
```

### 📁 文件操作
Claude Code 可以直接操作文件：
```
> 创建一个新的 README.md 文件
> 修改 main.py 中的 bug
> 查看项目结构
```

### 🔍 项目分析
让 Claude 了解你的项目：
```
> 分析这个项目的结构
> 这个项目是做什么的？
> 有哪些主要的功能模块？
```

---

## 常用命令

### Slash 命令（以 / 开头）

| 命令 | 功能 | 示例 |
|------|------|------|
| `/help` | 显示帮助信息 | `/help` |
| `/clear` | 清空对话历史 | `/clear` |
| `/exit` | 退出 Claude Code | `/exit` |
| `/files` | 显示项目文件 | `/files` |
| `/git` | Git 相关操作 | `/git status` |
| `/run` | 运行命令 | `/run python main.py` |

### 实用示例
```bash
# 查看项目文件
> /files

# 运行测试
> /run npm test

# 查看 Git 状态
> /git status

# 清空历史重新开始
> /clear
```

---

## 实际案例

### 案例1：创建一个简单网页
```
> 帮我创建一个简单的个人主页，包含姓名、介绍和联系方式
```

Claude 会：
1. 创建 `index.html` 文件
2. 添加基本的 CSS 样式
3. 包含你要求的内容

### 案例2：修复 Python 错误
```
> 我的 Python 代码报错了：NameError: name 'x' is not defined
```

Claude 会：
1. 分析错误原因
2. 找到问题所在
3. 提供修复方案
4. 直接修改文件

### 案例3：添加新功能
```
> 给我的计算器添加一个历史记录功能
```

Claude 会：
1. 理解现有代码
2. 设计历史记录功能
3. 修改相关文件
4. 确保功能正常工作

---

## 进阶技巧

### 🎯 明确的指令
**好的指令：**
```
> 创建一个 Python 函数，计算两个数的最大公约数，使用欧几里得算法
```

**不够明确的指令：**
```
> 写个数学函数
```

### 🔄 迭代改进
```
> 刚才的函数能加上错误处理吗？
> 再添加一些注释说明
> 写个测试用例验证一下
```

### 📋 批量操作
```
> 帮我重构整个项目，使用更好的文件结构，并添加适当的注释
```

### 🧪 测试驱动
```
> 先写测试用例，然后实现功能
> 运行测试看看是否通过
> 修复失败的测试
```

---

## 常见问题

### Q: Claude Code 无法启动？
**A:** 检查以下几点：
- Node.js 版本是否 ≥ 18
- 环境变量是否正确设置
- 网络连接是否正常
- API Token 是否有效

### Q: 提示权限错误？
**A:** 确保：
- 在项目目录中运行
- 有文件读写权限
- 信任了工作目录

### Q: API 额度用完了？
**A:** 
- 检查剩余额度
- 联系服务商充值
- 或切换到其他 AI 工具

### Q: 代码质量不满意？
**A:** 
- 提供更详细的需求描述
- 分步骤逐步完善
- 要求添加注释和测试

### Q: 如何保存对话历史？
**A:** 
- Claude Code 会自动保存项目上下文
- 重要的代码变更会体现在文件中
- 可以使用 Git 管理版本

---

## 🎉 开始你的 Claude Code 之旅

现在你已经掌握了 Claude Code 的基础用法！记住：

1. **从简单开始** - 先尝试基本的文件操作
2. **明确表达** - 清楚地描述你想要什么
3. **逐步改进** - 一步步完善你的代码
4. **多多练习** - 在实际项目中使用

**下一步建议：**
- 在一个小项目中试用 Claude Code
- 对比与其他 AI 工具的差异
- 探索更多高级功能

祝你编程愉快！🚀
