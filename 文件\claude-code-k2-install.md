# Claude Code K2 安装教程

> 正所谓，上队昔兵争，必先刺兵器。

## 📋 安装步骤

### 第一步：安装 Node.js

确保系统已安装 Node.js：

```bash
sudo apt-get install -y nodejs
```

### 第二步：一键安装 Claude Code

使用 npm 全局安装 Claude Code：

```bash
npm install -g @anthropic-ai/claude-code
```

### 第三步：执行"神奇语句"

执行以下代码跳过 onboarding 流程：

```javascript
node --eval '
const os = require("os");
const fs = require("fs");
const path = require("path");
const homeDir = os.homedir();
const filePath = path.join(homeDir, ".claude.json");

if (fs.existsSync(filePath)) {
    const content = JSON.parse(fs.readFileSync(filePath, "utf-8"));
    fs.writeFileSync(filePath, JSON.stringify({
        ...content,
        hasCompletedOnboarding: true
    }, null, 2), "utf-8");
} else {
    fs.writeFileSync(filePath, JSON.stringify({
        hasCompletedOnboarding: true
    }), "utf-8");
}
'
```

### 第四步：配置 API 密钥

#### 获取免费"通行证"

1. 前往 [Kimi 开放平台](https://platform.moonshot.cn/)
2. 注册账户并获取 API Key

#### 设置环境变量

```bash
export ANTHROPIC_API_KEY=sk-...
export ANTHROPIC_BASE_URL=api...moonshot.cn/anthropic
```

#### 持久化配置（可选）

将环境变量写入配置文件：

```bash
echo 'export ANTHROPIC_API_KEY=sk-...' >> ~/.profile
echo 'export ANTHROPIC_BASE_URL=api...moonshot.cn/anthropic' >> ~/.profile
```

## 🚀 启动使用

配置完成后，直接运行：

```bash
claude
```

---

## ⚠️ 注意事项

- 这是第三方 API 服务，非官方 Anthropic 服务
- "神奇语句"会跳过官方 onboarding 流程
- 可能存在功能限制或兼容性问题
- 建议仅用于测试和学习目的

## 📝 标签

`#claudecode` `#效率神器`

---

*教程创建时间：今天15:11北京*
